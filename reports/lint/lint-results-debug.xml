<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.11.1">

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application or sdk runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk = 34"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/build.gradle.kts"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/AndroidManifest.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of Gradle than 8.13 is available: 8.14.3"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="distributionUrl=https\://services.gradle.org/distributions/gradle-8.13-bin.zip"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/gradle/wrapper/gradle-wrapper.properties"
            line="4"
            column="17"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of `compileSdkVersion` than 35 is available: 36"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    compileSdk = 35"
        errorLine2="    ~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/build.gradle.kts"
            line="15"
            column="5"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.mockito.kotlin:mockito-kotlin than 5.2.1 is available: 5.4.0"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="mockitoKotlin = &quot;5.2.1&quot;"
        errorLine2="                ~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/gradle/libs.versions.toml"
            line="46"
            column="17"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jlleitschuh.gradle.ktlint than 12.1.0 is available: 13.0.0"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="ktlint = &quot;12.1.0&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/gradle/libs.versions.toml"
            line="52"
            column="10"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jetbrains.dokka than 1.9.10 is available: 2.0.0"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="dokka = &quot;1.9.10&quot;"
        errorLine2="        ~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/gradle/libs.versions.toml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/NetworkUtils.kt"
            line="28"
            column="24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/NetworkUtils.kt"
            line="44"
            column="24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/NetworkUtils.kt"
            line="59"
            column="24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/NetworkUtils.kt"
            line="86"
            column="24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/ProxyService.kt"
            line="246"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/ProxyVpnService.kt"
            line="298"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_500` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.white` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.nav_proxy_config` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;nav_proxy_config&quot;>Proxy Config&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.nav_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;nav_settings&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.nav_about` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;nav_about&quot;>About&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_config_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_config_title&quot;>Proxy Configuration&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_status_connected` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_status_connected&quot;>Connected&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_status_connecting` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_status_connecting&quot;>Connecting&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_status_disconnected` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_status_disconnected&quot;>Disconnected&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_status_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_status_error&quot;>Error&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_type_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_type_label&quot;>Proxy Type&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_type_http` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_type_http&quot;>HTTP&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_type_https` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_type_https&quot;>HTTPS&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.proxy_type_socks` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;proxy_type_socks&quot;>SOCKS&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.server_host_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;server_host_label&quot;>Server Host&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.server_host_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;server_host_hint&quot;>Enter server address&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.server_port_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;server_port_label&quot;>Server Port&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.server_port_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;server_port_hint&quot;>Enter port number&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.username_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;username_label&quot;>Username (Optional)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.username_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;username_hint&quot;>Enter username&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.password_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;password_label&quot;>Password (Optional)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.password_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;password_hint&quot;>Enter password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.domains_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;domains_label&quot;>Allowed Domains&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.domains_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;domains_hint&quot;>Enter domains (one per line)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.domains_help` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;domains_help&quot;>Leave empty to allow all domains&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_connect` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_connect&quot;>Connect&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_disconnect` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_disconnect&quot;>Disconnect&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_save_config` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_save_config&quot;>Save Configuration&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_test_connection` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_test_connection&quot;>Test Connection&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_title&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_general_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_general_title&quot;>General&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_auto_start_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_auto_start_title&quot;>Auto-start on Boot&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_auto_start_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_auto_start_summary&quot;>Automatically start proxy when device boots&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_persistent_notification_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_persistent_notification_title&quot;>Persistent Notification&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_persistent_notification_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_persistent_notification_summary&quot;>Show notification when proxy is active&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_security_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_security_title&quot;>Security&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_encrypt_credentials_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_encrypt_credentials_title&quot;>Encrypt Credentials&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_encrypt_credentials_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_encrypt_credentials_summary&quot;>Store username and password securely&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_advanced_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_advanced_title&quot;>Advanced&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_connection_timeout_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_connection_timeout_title&quot;>Connection Timeout&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_connection_timeout_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_connection_timeout_summary&quot;>Timeout for proxy connections (seconds)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_retry_attempts_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_retry_attempts_title&quot;>Retry Attempts&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_retry_attempts_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_retry_attempts_summary&quot;>Number of connection retry attempts&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_log_level_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_log_level_title&quot;>Log Level&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_log_level_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_log_level_summary&quot;>Verbosity of application logs&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_export_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_export_title&quot;>Export Configuration&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_export_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_export_summary&quot;>Export proxy settings to file&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_import_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_import_title&quot;>Import Configuration&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_import_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_import_summary&quot;>Import proxy settings from file&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_reset_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_reset_title&quot;>Reset to Defaults&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.settings_reset_summary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;settings_reset_summary&quot;>Reset all settings to default values&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_title&quot;>About&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_app_name` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_app_name&quot;>Proxy Self&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_version` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_version&quot;>Version %s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_description&quot;>A secure and efficient proxy application for Android devices. Built with Clean Architecture and modern Android development practices.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="64"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_features_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_features_title&quot;>Features&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_feature_1` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_feature_1&quot;>• HTTP/HTTPS/SOCKS proxy support&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_feature_2` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_feature_2&quot;>• Domain-based filtering&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="67"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_feature_3` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_feature_3&quot;>• Auto-start on boot&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_feature_4` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_feature_4&quot;>• Quick Settings tile&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_feature_5` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_feature_5&quot;>• Secure credential storage&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_developer_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_developer_title&quot;>Developer&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_developer_name` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_developer_name&quot;>Android Proxy Team&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_contact_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_contact_title&quot;>Contact&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_contact_email` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_contact_email&quot;><EMAIL>&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="74"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_privacy_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_privacy_title&quot;>Privacy Policy&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_privacy_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_privacy_description&quot;>We respect your privacy. This app does not collect or share personal data.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_open_source_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_open_source_title&quot;>Open Source&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_open_source_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_open_source_description&quot;>This project is open source and available on GitHub.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_license_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_license_title&quot;>License&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.about_license_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;about_license_description&quot;>Licensed under MIT License&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.notification_action_open` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;notification_action_open&quot;>Open&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_invalid_host` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_invalid_host&quot;>Please enter a valid server host&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_invalid_port` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_invalid_port&quot;>Please enter a valid port number (1-65535)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_connection_failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_connection_failed&quot;>Failed to connect to proxy server&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_authentication_failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_authentication_failed&quot;>Authentication failed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="103"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_network_unavailable` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_network_unavailable&quot;>Network is not available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="104"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success_connection_established` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success_connection_established&quot;>Proxy connection established&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="105"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success_configuration_saved` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success_configuration_saved&quot;>Configuration saved successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="106"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success_settings_exported` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success_settings_exported&quot;>Settings exported successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success_settings_imported` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success_settings_imported&quot;>Settings imported successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.confirm_disconnect_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;confirm_disconnect_title&quot;>Disconnect Proxy&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="109"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.confirm_disconnect_message` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;confirm_disconnect_message&quot;>Are you sure you want to disconnect the proxy?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="110"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.confirm_reset_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;confirm_reset_title&quot;>Reset Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.confirm_reset_message` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;confirm_reset_message&quot;>This will reset all settings to default values. Continue?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="112"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_ok` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_ok&quot;>OK&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="113"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_cancel` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_cancel&quot;>Cancel&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="114"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_yes` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_yes&quot;>Yes&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.btn_no` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;btn_no&quot;>No&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="116"
            column="13"/>
    </issue>

    <issue
        id="TypographyDashes"
        severity="Warning"
        message="Replace &quot;-&quot; with an &quot;en dash&quot; character (–, &amp;#8211;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Hyphen can be replaced with dash"
        explanation="The &quot;n dash&quot; (\u2013, &amp;#8211;) and the &quot;m dash&quot; (\u2014, &amp;#8212;) characters are used for ranges (n dash) and breaks (m dash). Using these instead of plain hyphens can make text easier to read and your application will look more polished."
        url="https://en.wikipedia.org/wiki/Dash"
        urls="https://en.wikipedia.org/wiki/Dash"
        errorLine1="    &lt;string name=&quot;error_invalid_port&quot;>Please enter a valid port number (1-65535)&lt;/string>"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/res/values/strings.xml"
            line="101"
            column="39"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                encryptedSharedPreferences.edit()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/EncryptionUtils.kt"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                encryptedSharedPreferences.edit().clear().apply()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/EncryptionUtils.kt"
            line="96"
            column="17"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    sharedPreferences.edit().clear().apply()"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/SettingsViewModel.kt"
            line="188"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    sharedPreferences.edit()"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/SettingsViewModel.kt"
            line="232"
            column="21"/>
    </issue>

</issues>
