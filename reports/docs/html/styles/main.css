/*!
 * Copyright 2014-2023 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license.
 */#pages-search{cursor:pointer;border:none;border-radius:50%;background:transparent;fill:#fff;fill:var(--dark-mode-and-search-icon-color)}#pages-search:focus{outline:none}#pages-search:hover{background:var(--white-10)}.search,.search [data-test=ring-select],.search [data-test=ring-tooltip],.search [data-test=ring-select_focus],.search #pages-search{display:inline-block;padding:0;margin:0;font-size:0;line-height:0}.search-hotkey-popup{background-color:var(--background-color) !important;padding:4px}.popup-wrapper{min-width:calc(100% - 322px) !important;border:1px solid rgba(255,255,255,.2) !important;background-color:#27282c !important}.popup-wrapper [class^=filterWrapper]{border-bottom:1px solid rgba(255,255,255,.2)}.popup-wrapper input{color:rgba(255,255,255,.8) !important;font-weight:normal !important}.popup-wrapper span[data-test-custom=ring-select-popup-filter-icon]{color:#fff}.popup-wrapper button[data-test=ring-input-clear]{color:#fff !important}@media screen and (max-width: 759px){.popup-wrapper{min-width:100% !important}}.template-wrapper{display:grid;height:32px;grid-template-columns:auto auto}.template-wrapper strong{color:rgba(255,255,255,.8)}.template-wrapper span{color:rgba(255,255,255,.8);line-height:32px}.template-wrapper span.template-description{color:rgba(255,255,255,.6);justify-self:end}@media screen and (max-width: 759px){.template-wrapper{display:flex;flex-direction:column;height:auto}.template-wrapper span{line-height:unset}}.template-name{justify-self:start}[class^=fade]{display:none}[class*=hover]{background-color:rgba(255,255,255,.1) !important}
/* stylelint-disable color-no-hex */

:root {
  --ring-unit: 8px;

  /* Element */
  --ring-line-color: #dfe5eb;
  --ring-dark-line-color: #475159;
  --ring-borders-color: #b8d1e5;
  --ring-dark-borders-color: #406380;
  --ring-icon-color: var(--ring-borders-color);
  --ring-icon-secondary-color: #999;
  --ring-border-disabled-color: #dbdbdb;
  --ring-icon-disabled-color: #bbb;
  --ring-border-hover-color: #80c6ff;
  --ring-dark-border-hover-color: #70b1e6;
  --ring-icon-hover-color: var(--ring-link-hover-color);
  --ring-main-color: #008eff;
  --ring-main-hover-color: #007ee5;
  --ring-icon-error-color: #db5860;
  --ring-icon-warning-color: #eda200;
  --ring-icon-success-color: #59a869;
  --ring-pale-control-color: #cfdbe5;
  --ring-popup-border-components: 0, 42, 76;
  --ring-popup-border-color: rgba(var(--ring-popup-border-components), 0.1);
  --ring-popup-shadow-color: rgba(var(--ring-popup-border-components), 0.15);
  --ring-message-shadow-color: rgba(var(--ring-popup-border-components), 0.3);
  --ring-pinned-shadow-color: #737577;

  /* Text */
  --ring-search-color: #669ecc;
  --ring-hint-color: #406380;
  --ring-link-color: #0f5b99;
  --ring-link-hover-color: #ff008c;
  --ring-error-color: #c22731;
  --ring-warning-color: #cc8b00;
  --ring-success-color: #1b8833;
  --ring-text-color: #1f2326;
  --ring-dark-text-color: #fff;
  --ring-heading-color: var(--ring-text-color);
  --ring-secondary-color: #737577;
  --ring-dark-secondary-color: #888;
  --ring-disabled-color: #999;
  --ring-dark-disabled-color: #444;
  --ring-dark-active-color: #ccc;

  /* Background */
  --ring-content-background-color: #fff;
  --ring-popup-background-color: #fff;
  --ring-sidebar-background-color: #f7f9fa;
  --ring-selected-background-color: #d4edff;
  --ring-hover-background-color: #ebf6ff;
  --ring-dark-selected-background-color: #002a4d;
  --ring-message-background-color: #111314;
  --ring-navigation-background-color: #000;
  --ring-tag-background-color: #e6ecf2;
  --ring-removed-background-color: #ffd5cb;
  --ring-warning-background-color: #faeccd;
  --ring-added-background-color: #bce8bb;

  /* Code */
  --ring-code-background-color: var(--ring-content-background-color);
  --ring-code-color: #000;
  --ring-code-comment-color: #707070;
  --ring-code-meta-color: #707070;
  --ring-code-keyword-color: #000080;
  --ring-code-tag-background-color: #efefef;
  --ring-code-tag-color: var(--ring-code-keyword-color);
  --ring-code-tag-font-weight: bold;
  --ring-code-field-color: #660e7a;
  --ring-code-attribute-color: #00f;
  --ring-code-number-color: var(--ring-code-attribute-color);
  --ring-code-string-color: #007a00;
  --ring-code-addition-color: #aadeaa;
  --ring-code-deletion-color: #c8c8c8;

  /* Metrics */
  --ring-border-radius: 3px;
  --ring-border-radius-small: 2px;
  --ring-font-size-larger: 14px;
  --ring-font-size: 13px;
  --ring-font-size-smaller: 12px;
  --ring-line-height-taller: 21px;
  --ring-line-height: 20px;
  --ring-line-height-lower: 18px;
  --ring-line-height-lowest: 16px;
  --ring-ease: 0.3s ease-out;
  --ring-fast-ease: 0.15s ease-out;
  --ring-font-family: system-ui, Arial, sans-serif;
  --ring-font-family-monospace:
    Menlo,
    "Bitstream Vera Sans Mono",
    "Ubuntu Mono",
    Consolas,
    "Courier New",
    Courier,
    monospace;

  /* Common z-index-values */

  /* Invisible element is an absolutely positioned element which should be below */
  /* all other elements on the page */
  --ring-invisible-element-z-index: -1;

  /* z-index for position: fixed elements */
  --ring-fixed-z-index: 1;

  /* Elements that should overlay all other elements on the page */
  --ring-overlay-z-index: 5;

  /* Alerts should de displayed above overlays */
  --ring-alert-z-index: 6;
}

/*!
 * Copyright 2014-2023 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license.
 *//*!
 * Copyright 2014-2023 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license.
 */html,.app-root{height:100%}.search-root{margin:0;padding:0;background:var(--ring-content-background-color);font-family:var(--ring-font-family);font-size:var(--ring-font-size);line-height:var(--ring-line-height)}.search-content{z-index:8}

/*# sourceMappingURL=main.css.map*/