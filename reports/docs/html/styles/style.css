/*
 * Copyright 2014-2023 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license.
 */

@import url('./font-jb-sans-auto.css');
@import url('https://fonts.googleapis.com/css?family=JetBrains+Mono');

/* --- root styles --- */
:root {
    --default-gray: #f4f4f4;
    --default-font-color: black;
    --header-font-color: var(--default-font-color);

    --breadcrumb-font-color: #637282;
    --breadcrumb-margin: 24px;
    --hover-link-color: #5B5DEF;

    --footer-height: 64px;
    --footer-padding-top: 48px;
    --footer-background: var(--default-gray);
    --footer-font-color: var(--average-color);
    --footer-go-to-top-color: white;

    --horizontal-spacing-for-content: 16px;
    --bottom-spacing: 16px;
    --color-scrollbar: rgba(39, 40, 44, 0.40);
    --color-scrollbar-track: var(--default-gray);
    --default-white: #fff;
    --background-color: var(--default-white);
    --dark-mode-and-search-icon-color: var(--default-white);
    --color-dark: #27282c;
    --default-font-family: JetBrains Sans, Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI,Roboto, Oxygen, Ubuntu,Cantarell, Droid Sans, Helvetica Neue, Arial, sans-serif;
    --default-monospace-font-family: JetBrains Mono, SFMono-Regular, Consolas, Liberation Mono, Menlo, Courier, monospace;
    --default-font-size: 15px;
    --average-color: var(--color-dark);
    --brief-color: var(--average-color);
    --copy-icon-color: rgba(39, 40, 44, .7);
    --copy-icon-hover-color: var(--color-dark);
    --code-background: rgba(39, 40, 44, .05);
    --border-color: rgba(39, 40, 44, .2);
    --navigation-highlight-color: rgba(39, 40, 44, 0.05);
    --top-navigation-height: 73px;
    --max-width: 1160px;
    --white-10: hsla(0, 0%, 100%, .1);

    --active-tab-border-color: #7F52FF;
    --inactive-tab-border-color: rgba(164, 164, 170, 0.7);

    --active-section-color: #7F52FF;
    --inactive-section-color: rgba(25, 25, 28, .7);

    --sidebar-width: 280px;
    --sidemenu-section-active-color: #7F52FF;
}

html {
    height: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    scrollbar-color: rgba(39, 40, 44, 0.40) #F4F4F4;
    scrollbar-color: var(--color-scrollbar) var(--color-scrollbar-track);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    color: var(--default-font-color);
}

html ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

html ::-webkit-scrollbar-track {
    background-color: var(--color-scrollbar-track);
}

html ::-webkit-scrollbar-thumb {
    width: 8px;
    border-radius: 6px;
    background: rgba(39, 40, 44, 0.40);
    background: var(--color-scrollbar);
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
}
/* /--- root styles --- */

/* --- global tags styles --- */
body, table {
    background: var(--background-color);
    font-family: var(--default-font-family);
    font-style: normal;
    font-weight: normal;
    font-size: var(--default-font-size);
    line-height: 1.6;
    margin: 0;
}

h1 {
    font-size: 40px;
    line-height: 48px;
    letter-spacing: -1px;
}

h2 {
    font-size: 31px;
    line-height: 40px;
    letter-spacing: -0.5px;
}

h3 {
    font-size: 20px;
    line-height: 28px;
    letter-spacing: -0.2px;
}

p, ul, ol, table, pre, dl {
    margin: 0;
}

a {
    text-decoration: none;
}

u {
    text-decoration: none;
    padding-bottom: 2px;
    border-bottom: 1px solid var(--border-color);
}

blockquote {
    border-left: 1ch solid var(--default-gray);
    margin: 0;
    padding-left: 1ch;
    font-style: italic;
    color: var(--average-color);
}

.theme-dark blockquote {
    color: var(--default-font-color);
    border-left-color: var(--code-background);
}

pre {
    display: block;
}

dt {
    color: #444;
    font-weight: 530;
}

img {
    max-width: 100%;
}

small {
    font-size: 11px;
}

table {
    width: 100%;
    border-collapse: collapse;
    padding: 5px;
}

th, td {
    padding: 12px 10px 11px;
    text-align: left;
    vertical-align: top;
}

tbody > tr {
    min-height: 56px;
}

td:first-child {
    width: 20vw;
}
/* /---  global tags styles --- */

/* ---  utils classes --- */
.w-100 {
    width: 100%;
}

.no-gutters {
    margin: 0;
    padding: 0;
}

.d-flex {
    display: flex;
}

.floating-right {
    float: right;
}

.pull-right {
    float: right;
    margin-left: auto
}

.clearfix::after {
    display: block;
    content: '';
    clear: both;
    height: 0;
}
/* /---  utils classes --- */

/* ---dark theme --- */
.theme-dark {
    --background-color: #27282c;
    --color-dark: #3d3d41;
    --default-font-color: hsla(0, 0%, 100%, 0.8);
    --border-color: hsla(0, 0%, 100%, 0.2);
    --code-background: hsla(0, 0%, 100%, 0.05);
    --breadcrumb-font-color: #8c8c8e;
    --brief-color: hsla(0, 0%, 100%, 0.4);
    --copy-icon-color: hsla(0, 0%, 100%, 0.6);
    --copy-icon-hover-color: #fff;

    --active-tab-border-color: var(--default-font-color);
    --inactive-tab-border-color: hsla(0, 0%, 100%, 0.4);

    --active-section-color: var(--default-font-color);
    --inactive-section-color: hsla(0, 0%, 100%, 0.4);

    --navigation-highlight-color: rgba(255, 255, 255, 0.05);
    --footer-background: hsla(0, 0%, 100%, 0.05);
    --footer-font-color: hsla(0, 0%, 100%, 0.6);
    --footer-go-to-top-color: var(--footer-font-color);

    --sidemenu-section-active-color: var(--color-dark);
}
/* /---dark theme --- */

.root {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* --- Navigation styles --- */
.navigation {
    display: flex;
    justify-content: space-between;

    color: #fff;
    background-color: var(--color-dark);
    font-family: var(--default-font-family);
    letter-spacing: -0.1px;

    /* Reset margin and use padding for border */
    margin-left: 0;
    margin-right: 0;
    padding: 10px var(--horizontal-spacing-for-content);

    z-index: 4;
}

.navigation--inner {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    flex: 1 1 auto;
}

.navigation--inner, .navigation-title {
    min-height: 40px;
}

.navigation-title, .filter-section {
    align-items: center;
}

.navigation-title {
    display: flex;
    align-items: center;
}

/* --- Navigation MENU --- */
.menu-toggle {
    color: var(--background-color);
    line-height: 0;
    font-size: 0;
    text-indent: -9999px;

    background: transparent;
    border: none;
    padding: 0;
    margin-right: 16px;
    outline: none;

    transition: margin .2s ease-out;
    z-index: 5;
}

@media (min-width: 760px) {
    .menu-toggle {
        display: none;
    }
}

.menu-toggle::before {
    display: block;
    content: '';
    background: url('../images/burger.svg') no-repeat center;
    height: 28px;
    width: 28px;
}
/* /--- Navigation MENU --- */

.library-version {
    position: relative;
    top: -4px;
    margin-left: 3px;

    color: rgba(255,255,255,.7);
    font-size: 13px;
    font-weight: normal;
    line-height: 16px;
}

.filter-section {
    z-index: 0;
}

.no-js .filter-section {
    display: none;
}

@media (min-width: 760px) {
    .filter-section {
        padding: 5px 0 5px;
    }
}
/* --- Navigation controls --- */
.navigation-controls {
    display: flex;
}

@media (min-width: 760px) {
    .navigation-controls {
        align-items: center;
    }
}

.no-js .navigation-controls {
    display: none;
}

/* --- Navigation THEME --- */
.navigation-controls--search {
    display: inline-flex;
    font-size: 0;
    line-height: 0;
}

.navigation-controls--theme {
    display: block;
    border-radius: 50%;
    background-color: inherit;
    margin-left: 4px;
    padding: 0;
    border: none;
    cursor: pointer;
    font-size: 0;
    line-height: 0;
}

.navigation-controls--theme::before {
    height: 40px;
    width: 40px;
}

.navigation-controls--theme:hover {
    background: var(--white-10);
}

.navigation-controls--theme::before {
    display: block;
    content: url("../images/theme-toggle.svg");
}

@media (max-width: 759px) {
    .navigation-controls--theme {
        display: none;
    }
}
/* /--- Navigation THEME --- */

.navigation .platform-selector:not([data-active]) {
    color: #fff;
}
/* /--- Navigation controls --- */
/* /--- Navigation styles --- */

/* --- Layout styles --- */

#container {
    display: flex;
    flex: 1 1 auto;
    min-height: 0; /* full height exclude header */
}

#container > .sidebar, #container > #main {
    overflow: auto;
}

#main {
    display: flex;
    flex-direction: column;
    flex: 1 1 0; /* full width, but no affects for sidebar */
}

.sidebar {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    border-right: 1px solid var(--border-color);
    width: var(--sidebar-width);
}

.no-js .sidebar {
    display: none;
}

@media (max-width: 759px) {
    #container {
        position: relative;
    }

    .sidebar {
        position: absolute;
        top: 0;
        bottom: 0;
        box-sizing: border-box;
        background: var(--background-color);
        margin-left: calc(-1 * var(--sidebar-width));
        transition: margin .2s ease-out;
        z-index: 4;
    }

    .sidebar.open {
        margin-left: 0;
    }

    .sidebar.open ~ #main .navigation-controls--search {
        display: none;
    }

    .sidebar.open ~ #main .menu-toggle {
        margin-left: var(--sidebar-width);
    }
}

.sidebar--inner {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    padding-top: 22px;
    padding-bottom: 16px;
}
/* /--- Layout styles --- */

/* --- Main Content styles --- */
.main-content {
    padding-bottom: var(--bottom-spacing);
    margin-left: auto;
    margin-right: auto;
    max-width: var(--max-width);
    width: 100%;
    z-index: 0;
}

.main-content > * {
    margin-left: var(--horizontal-spacing-for-content);
    margin-right: var(--horizontal-spacing-for-content);
}

.main-content .content > hr {
    margin: 30px 0;
    border-top: 3px double #8c8b8b;
}

.main-content :is(h1, h2) {
    font-weight: 530;
}
/* /--- Main Content styles --- */

/* /--- Breadcrumbs styles --- */
.breadcrumbs, .breadcrumbs a, .breadcrumbs a:hover {
    margin-top: var(--breadcrumb-margin);
    color: var(--breadcrumb-font-color);
    overflow-wrap: break-word;
}

.breadcrumbs .delimiter {
    margin: auto 2px;
}

.breadcrumbs .current {
    color: var(--default-font-color);
}
/* /--- Breadcrumbs styles --- */

.tabs-section,
.platform-hinted > .platform-bookmarks-row {
    margin-left: -8px;
    margin-right: -8px;
}

.section-tab,
.platform-hinted > .platform-bookmarks-row > .platform-bookmark {
    border: 0;
    padding: 11px 3px;
    margin: 0 8px;
    cursor: pointer;
    outline: none;
    font-size: var(--default-font-size);
    background-color: transparent;
    color: var(--inactive-section-color);
    border-bottom: 1px solid var(--inactive-tab-border-color);
}

.platform-hinted > .platform-bookmarks-row {
    margin-bottom: 16px;
}

.no-js .platform-bookmarks-row + .sourceset-dependent-content {
    margin-top: 8px;
}

.no-js .platform-bookmarks-row + .sourceset-dependent-content:last-of-type {
    margin-top: 0;
}

.section-tab:hover {
    color: var(--default-font-color);
    border-bottom: 2px solid var(--default-font-color);
}

.section-tab[data-active=''] {
    color: var(--active-section-color);
    border-bottom: 2px solid var(--active-tab-border-color);
}

.tabs-section-body > div {
    margin-top: 12px;
}

.tabs-section-body .with-platform-tabs {
    padding-top: 12px;
    padding-bottom: 12px;
}

.cover > .platform-hinted {
    padding-bottom: 12px;
}

.cover {
    display: flex;
    flex-direction: column;
}

.cover .platform-hinted.with-platform-tabs .sourceset-dependent-content > .block ~ .symbol {
    padding-top: 16px;
    padding-left: 0;
}

.cover .sourceset-dependent-content > .block {
    padding: 16px 0;
    font-size: 18px;
    line-height: 28px;
}

.cover .platform-hinted.with-platform-tabs .sourceset-dependent-content > .block {
    padding: 0;
    font-size: var(--default-font-size);
}

.cover ~ .divergent-group {
    margin-top: 24px;
    padding: 24px 8px 8px 8px;
}

.cover ~ .divergent-group .main-subrow .symbol {
    width: 100%;
}

.main-content p.paragraph,
.sample-container, blockquote,
.content > .symbol {
    margin-top: 8px;
}

blockquote,
.content > .symbol:first-of-type,
p.paragraph:first-child,
.brief p.paragraph {
    margin-top: 0;
}

.content .kdoc-tag > p.paragraph {
    margin-top: 0;
}

.content h4 {
    margin-bottom: 0;
}

.divergent-group {
    background-color: var(--background-color);
    padding: 16px 0 8px 0;
    margin-bottom: 2px;
}

.divergent-group .table-row, tbody > tr {
    border-bottom: 1px solid var(--border-color);
}

.divergent-group .table-row:last-of-type, tbody > tr:last-of-type {
    border-bottom: none;
}

.title > .divergent-group:first-of-type {
    padding-top: 0;
}

.sample-container, div.CodeMirror {
    position: relative;
    display: flex;
    flex-direction: column;
}

code.paragraph {
    display: block;
}

.overview > .navButton {
    position: absolute;
    align-items: center;
    display: flex;
    justify-content: flex-end;
    padding: 2px 2px 2px 0;
    margin-right: 5px;
    cursor: pointer;
}

.strikethrough {
    text-decoration: line-through;
}

.symbol:empty {
    padding: 0;
}

.symbol:not(.token), code {
    background-color: var(--code-background);
    align-items: center;
    box-sizing: border-box;
    white-space: pre-wrap;
    font-family: var(--default-monospace-font-family);
    font-size: var(--default-font-size);
}

.symbol:not(.token), code.block {
    display: block;
    padding: 12px 32px 12px 12px;
    border-radius: 8px;
    line-height: 24px;
    position: relative;
}

code {
    overflow-x: auto;
    max-width: 100%;
}

code:not(.block) {
    display: inline-block;
    vertical-align: middle;
}

.symbol > a {
    color: var(--hover-link-color);
}

.copy-icon {
    cursor: pointer;
}

.sample-container span.copy-icon {
    display: none;
}

.js .sample-container:hover span.copy-icon {
    display: inline-block;
}

.sample-container span.copy-icon::before {
    width: 24px;
    height: 24px;
    display: inline-block;
    content: '';
    /* masks are required if you want to change color of the icon dynamically instead of using those provided with the SVG */
    -webkit-mask: url("../images/copy-icon.svg") no-repeat 50% 50%;
    mask: url("../images/copy-icon.svg") no-repeat 50% 50%;
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: var(--copy-icon-color);
}

.sample-container span.copy-icon:hover::before {
    background-color: var(--copy-icon-hover-color);
}

.copy-popup-wrapper {
    display: none;
    align-items: center;
    position: absolute;
    z-index: 1000;
    background: var(--background-color);
    font-weight: normal;
    font-family: var(--default-font-family);
    width: max-content;
    font-size: var(--default-font-size);
    cursor: default;
    border: 1px solid #D8DCE1;
    box-sizing: border-box;
    box-shadow: 0 5px 10px var(--ring-popup-shadow-color);
    border-radius: 3px;
    color: var(--default-font-color);
}

.copy-popup-wrapper > .copy-popup-icon::before {
    content: url("../images/copy-successful-icon.svg");
    padding: 8px;
}

.copy-popup-wrapper > .copy-popup-icon {
    position: relative;
    top: 3px;
}

.copy-popup-wrapper.popup-to-left {
    /* since it is in position absolute we can just move it to the left to make it always appear on the left side of the icon */
    left: -15em;
}

.table-row:hover .copy-popup-wrapper.active-popup,
.sample-container:hover .copy-popup-wrapper.active-popup {
    display: flex !important;
}

.copy-popup-wrapper:hover {
    font-weight: normal;
}

.copy-popup-wrapper > span:last-child {
    padding-right: 14px;
}

.symbol .top-right-position, .sample-container .top-right-position {
    /* it is important for a parent to have a position: relative */
    position: absolute;
    top: 8px;
    right: 8px;
}

.sideMenuPart > .overview {
    display: flex;
    align-items: center;
    position: relative;
    user-select: none; /* there's a weird bug with text selection */
    padding: 8px 0;
}

.sideMenuPart a {
    display: block;
    align-items: center;
    color: var(--default-font-color);
    overflow: hidden;
    padding-left: 23px;
}

.sideMenuPart a:hover {
    text-decoration: none;
    color: var(--default-font-color);
}

.sideMenuPart > .overview:before {
    box-sizing: border-box;
    content: '';
    top: 0;
    width: var(--sidebar-width);
    right: 0;
    bottom: 0;
    position: absolute;
    z-index: -1;
}

.overview:hover:before {
    background-color: var(--navigation-highlight-color);
}

#nav-submenu {
    padding-left: 24px;
}

.sideMenuPart {
    padding-left: 12px;
    box-sizing: border-box;
}

.sideMenuPart.hidden > .overview .navButtonContent::before {
    transform: rotate(0deg);
}

.sideMenuPart > .overview .navButtonContent::before {
    content: '';

    -webkit-mask: url("../images/arrow_down.svg") no-repeat 50% 50%;
    mask: url("../images/arrow_down.svg") no-repeat 50% 50%;
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: var(--default-font-color);

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    transform: rotate(90deg);
    width: 16px;
    height: 16px;
}

.sideMenuPart[data-active] > .overview .navButtonContent::before {
    background-color: var(--default-white);
}

.sideMenuPart.hidden > .navButton .navButtonContent::after {
    content: '\02192';
}

.sideMenuPart.hidden > .sideMenuPart {
    display: none;
}

.overview .nav-link-grid {
    display: grid;
    grid-template-columns: 16px auto; /* first is the icon, then name */
    grid-gap: 6px;
    align-items: center;
}

.nav-icon {
    width: 16px;
    height: 16px;
}

.nav-icon.class::before {
    content: url("../images/nav-icons/class.svg");
}

.nav-icon.class-kt::before {
    content: url("../images/nav-icons/class-kotlin.svg");
}

.nav-icon.function::before {
    content: url("../images/nav-icons/function.svg");
}

.nav-icon.enum-class::before {
    content: url("../images/nav-icons/enum.svg");
}

.nav-icon.enum-class-kt::before {
    content: url("../images/nav-icons/enum-kotlin.svg");
}

.nav-icon.annotation-class::before {
    content: url("../images/nav-icons/annotation.svg");
}

.nav-icon.annotation-class-kt::before {
    content: url("../images/nav-icons/annotation-kotlin.svg");
}

.nav-icon.abstract-class::before {
    content: url("../images/nav-icons/abstract-class.svg");
}

.nav-icon.abstract-class-kt::before {
    content: url("../images/nav-icons/abstract-class-kotlin.svg");
}

.nav-icon.exception-class::before {
    content: url("../images/nav-icons/exception-class.svg");
}

.nav-icon.interface::before {
    content: url("../images/nav-icons/interface.svg");
}

.nav-icon.interface-kt::before {
    content: url("../images/nav-icons/interface-kotlin.svg");
}

.nav-icon.object::before {
    content: url("../images/nav-icons/object.svg");
}

.nav-icon.typealias-kt::before {
    content: url("../images/nav-icons/typealias-kotlin.svg");
}

.nav-icon.val::before {
    content: url("../images/nav-icons/field-value.svg");
}

.nav-icon.var::before {
    content: url("../images/nav-icons/field-variable.svg");
}

.filtered > a, .filtered > .navButton {
    display: none;
}


.brief {
    white-space: pre-wrap;
    overflow: hidden;
}

h1.cover {
    font-size: 52px;
    line-height: 56px;
    letter-spacing: -1.5px;
    margin-bottom: 0;
    padding-bottom: 32px;
    display: block;
}

@media (max-width: 1119px) {
    h1.cover {
        font-size: 48px;
        line-height: 48px;
        padding-bottom: 8px;
    }
}

@media (max-width: 759px) {
    h1.cover {
        font-size: 32px;
        line-height: 32px;
    }
}

.UnderCoverText {
    font-size: 16px;
    line-height: 28px;
}

.UnderCoverText code {
    font-size: inherit;
}

.UnderCoverText table {
    margin: 8px 0 8px 0;
    word-break: break-word;
}

@media (max-width: 960px) {
    .UnderCoverText table {
        display: block;
        word-break: normal;
        overflow:  auto;
    }
}

.main-content a:not([data-name]) {
    padding-bottom: 2px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    transition: color .1s, border-color .1s;
}

.main-content a:hover {
    border-bottom-color: unset;
    color: inherit
}

a small {
    font-size: 11px;
    margin-top: -0.6em;
    display: block;
}

p.paragraph img {
    display: block;
}

.deprecation-content {
    margin: 20px 10px;
    border:1px solid var(--border-color);
    padding: 13px 15px 16px 15px;
}

.deprecation-content > h3 {
    margin-top: 0;
    margin-bottom: 0;
}

.deprecation-content > h4 {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 0;
}

.deprecation-content code.block {
    padding: 5px 10px;
    display: inline-block;
}

.deprecation-content .footnote {
    margin-left: 25px;
    font-size: 13px;
    font-weight: bold;
    display: block;
}

.deprecation-content .footnote > p {
    margin: 0;
}

[data-filterable-current=''] {
    display: none !important;
}

.platform-tags, .filter-section {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -8px;
    margin-left: -4px;
}

.platform-tag {
    --platform-tag-color: #bababb;
    border: 0 none;
    margin-right: 4px;
    margin-bottom: 8px;

    font-family: var(--default-font-family);
    font-size: 13px;
    line-height: 1.5;
    text-transform: capitalize;
}

.platform-tag.js-like, .platform-tag.jvm-like, .platform-tag.wasm-like {
    text-transform: uppercase;
}

.filter-section .platform-tag {
    cursor: pointer;
    border-radius: 4px;
    padding: 2px 16px;
}

.filter-section .platform-tag.jvm-like[data-active], .platform-tags .platform-tag.jvm-like {
    --platform-tag-color: #4dbb5f;
}

.filter-section .platform-tag.js-like[data-active], .platform-tags .platform-tag.js-like {
    --platform-tag-color: #ffc700;
}

.filter-section .platform-tag.native-like[data-active], .platform-tags .platform-tag.native-like {
    --platform-tag-color: #E082F3;
}

.filter-section .platform-tag.wasm-like[data-active], .platform-tags .platform-tag.wasm-like {
    --platform-tag-color: #9585F9;
}

.filter-section .platform-tag[data-active]:hover {
    color: #fff;
    background-color: rgba(186, 186, 187, .7);
}

.filter-section .platform-tag:not([data-active]) {
    color: #fff;
    /* Safari doesn't work correctly for `outline` with `border-radius` */
    /* outline: 1px solid rgba(255,255,255,.6); */
    /* ...use `box-shadow` instead: */
    box-shadow: 0 0 0 1px rgb(255 255 255 / 60%);
    background-color: rgba(255,255,255,.05);
}

.filter-section .platform-tag[data-active] {
    color: #19191c;
    background-color: var(--platform-tag-color);
}

.platform-tags .platform-tag {
    display: flex;
    align-items: center;
}

.platform-tags .platform-tag::before {
    display: inline-block;
    content: '';
    border-radius: 50%;
    background: var(--platform-tag-color);
    margin: 0 4px 0 8px;
    height: 8px;
    width: 8px;

    font-size: 13px;
    line-height: 1.6;
}

td.content {
    padding-left: 24px;
    padding-top: 16px;
    display: flex;
    flex-direction: column;
}

.main-subrow {
    display: flex;
    flex-direction: row;
    padding: 0;
    flex-wrap: wrap;
}

.main-subrow > div {
    margin-bottom: 8px;
}

.main-subrow > div > span {
    display: flex;
    position: relative;
}

.js .main-subrow:hover .anchor-icon {
    opacity: 1;
    transition: 0.2s;
}

.main-subrow .anchor-icon {
    opacity: 0;
    transition: 0.2s 0.5s;
}

.main-subrow .anchor-icon::before {
    content: url("../images/anchor-copy-button.svg");
}

.main-subrow .anchor-icon:hover {
    cursor: pointer;
}

.main-subrow .anchor-icon:hover > svg path {
    fill: var(--hover-link-color);
}

@media (hover: none) {
    .main-subrow .anchor-icon {
        display: none;
    }
}

.main-subrow .anchor-wrapper {
    position: relative;
    width: 24px;
    height: 16px;
    margin-left: 3px;
}

.inline-flex {
    display: inline-flex;
}

.platform-hinted {
    flex: auto;
    display: block;
}

.platform-hinted > .platform-bookmarks-row > .platform-bookmark {
    min-width: 64px;
    background: inherit;
    flex: none;
    order: 5;
    align-self: flex-start;
}

.platform-hinted > .platform-bookmarks-row > .platform-bookmark:hover {
    color: var(--default-font-color);
    border-bottom: 2px solid var(--default-font-color);
}

.platform-hinted > .platform-bookmarks-row > .platform-bookmark[data-active=''] {
    border-bottom: 2px solid var(--active-tab-border-color);
    color: var(--active-section-color);
}

.no-js .platform-bookmarks-row, .no-js .tabs-section {
    display: none;
}

.js .platform-hinted > .content:not([data-active]),
.js .tabs-section-body *[data-togglable]:not([data-active]) {
    display: none;
}

/* Work around an issue: https://github.com/JetBrains/kotlin-playground/issues/91
Applies for main description blocks with platform tabs.
Just in case of possible performance degradation it excluding tabs with briefs on classlike page */
#content > div:not(.tabbedcontent) .sourceset-dependent-content:not([data-active]) {
    display: block !important;
    visibility: hidden;
    height: 0;
    position: fixed;
    top: 0;
}

.with-platform-tags {
    display: flex;
}

.with-platform-tags ~ .main-subrow {
    padding-top: 8px;
}

.cover .with-platform-tabs {
    font-size: var(--default-font-size);
}

.cover > .with-platform-tabs > .content {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
}

.cover > .block {
    padding-top: 48px;
    padding-bottom: 24px;
    font-size: 18px;
    line-height: 28px;
}

.cover > .block:empty {
    padding-bottom: 0;
}

.parameters.wrapped > .parameter {
    display: block;
}

.table-row .inline-comment {
    padding-top: 8px;
    padding-bottom: 8px;
}

.table-row .platform-hinted .sourceset-dependent-content .brief,
.table-row .platform-hinted .sourceset-dependent-content .inline-comment {
    padding: 8px;
}

.sideMenuPart[data-active] > .overview:before {
    background: var(--sidemenu-section-active-color);
}

.sideMenuPart[data-active] > .overview > a {
    color: var(--default-white);
}

.table {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--border-color);
    padding: 11px 0 12px 0;
    background-color: var(--background-color);
}

.table-row:last-of-type {
    border-bottom: none;
}

.table-row .brief-comment {
    color: var(--brief-color);
}

.platform-dependent-row {
    display: grid;
    padding-top: 8px;
}

.title-row {
    display: grid;
    grid-template-columns: auto auto 7em;
    width: 100%;
}

@media print, (min-width: 960px) {
    .title-row {
        grid-template-columns: 20% auto 7em;
    }
}

.keyValue {
    display: grid;
    grid-gap: 8px;
}

@media print, (min-width: 960px) {
    .keyValue {
        grid-template-columns: 20% 80%;
    }
    .keyValue > div:first-child {
        word-break: break-word;
    }
}

@media print, (max-width: 960px) {
    div.wrapper {
        width: auto;
        margin: 0;
    }

    header, section, footer {
        float: none;
        position: static;
        width: auto;
    }

    header {
        padding-right: 320px;
    }

    section {
        border: 1px solid #e5e5e5;
        border-width: 1px 0;
        padding: 20px 0;
        margin: 0 0 20px;
    }

    header a small {
        display: inline;
    }

    header ul {
        position: absolute;
        right: 50px;
        top: 52px;
    }
}

.anchor-highlight {
    border: 1px solid var(--hover-link-color) !important;
    box-shadow: 0 0 0 0.2em #c8e1ff;
    margin-top: 0.2em;
    margin-bottom: 0.2em;
}

.filtered-message {
    margin: 25px;
    font-size: 20px;
    font-weight: bolder;
}

div.runnablesample {
    height: fit-content;
}

/* --- footer --- */
.footer {
    clear: both;
    display: flex;
    align-items: center;
    position: relative;
    min-height: var(--footer-height);
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.2px;
    color: var(--footer-font-color);
    margin-top: auto;
    background-color: var(--footer-background);
}

.footer span.go-to-top-icon {
    border-radius: 2em;
    padding: 11px 10px !important;
    background-color: var(--footer-go-to-top-color);
}

.footer span.go-to-top-icon > a::before {
    content: url("../images/go-to-top-icon.svg");
}

.footer > span:first-child {
    margin-left: var(--horizontal-spacing-for-content);
    padding-left: 0;
}

.footer > span:last-child {
    margin-right: var(--horizontal-spacing-for-content);
    padding-right: 0;
}

.footer > span {
    padding: 0 16px;
}

.footer a {
    color: var(--breadcrumb-font-color);
}

.footer span.go-to-top-icon > #go-to-top-link {
    padding: 0;
    border: none;
}

.footer .padded-icon {
    padding-left: 0.5em;
}

.footer .padded-icon::before {
    content: url("../images/footer-go-to-link.svg");
}
/* /--- footer --- */

/* Logo styles */
:root {
    --dokka-logo-image-url: url('../images/logo-icon.svg');
    --dokka-logo-height: 50px;
    --dokka-logo-width: 50px;
}

.library-name--link {
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: 530;
}

.library-name--link::before {
    content: '';
    background: var(--dokka-logo-image-url) center no-repeat;
    background-size: var(--dokka-logo-height) var(--dokka-logo-width);
    margin-right: 5px;
    width: var(--dokka-logo-height);
    height: var(--dokka-logo-width);
}

@media (max-width: 759px) {
    .library-name--link::before {
        display: none;
    }
}
/* / Logo styles */

/*
the hack to hide the headers inside tabs for a package page because each tab
has only one header, and the header text is the same as the tab name, so no point in showing it
*/
.main-content[data-page-type="package"] .tabs-section-body h2 {
    display: none;
}
