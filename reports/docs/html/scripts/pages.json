[{"name": "Available", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.Available", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-available/index.html", "searchKeys": ["Available", "Available", "com.android.proxy_self.infrastructure.managers.NetworkState.Available"]}, {"name": "CONNECTED", "description": "com.android.proxy_self.domain.entities.ProxyStatus.CONNECTED", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-c-o-n-n-e-c-t-e-d/index.html", "searchKeys": ["CONNECTED", "CONNECTED", "com.android.proxy_self.domain.entities.ProxyStatus.CONNECTED"]}, {"name": "CONNECTING", "description": "com.android.proxy_self.domain.entities.ProxyStatus.CONNECTING", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-c-o-n-n-e-c-t-i-n-g/index.html", "searchKeys": ["CONNECTING", "CONNECTING", "com.android.proxy_self.domain.entities.ProxyStatus.CONNECTING"]}, {"name": "DISCONNECTED", "description": "com.android.proxy_self.domain.entities.ProxyStatus.DISCONNECTED", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-d-i-s-c-o-n-n-e-c-t-e-d/index.html", "searchKeys": ["DISCONNECTED", "DISCONNECTED", "com.android.proxy_self.domain.entities.ProxyStatus.DISCONNECTED"]}, {"name": "ERROR", "description": "com.android.proxy_self.domain.entities.ProxyStatus.ERROR", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-e-r-r-o-r/index.html", "searchKeys": ["ERROR", "ERROR", "com.android.proxy_self.domain.entities.ProxyStatus.ERROR"]}, {"name": "Filled", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.Filled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-filled/index.html", "searchKeys": ["Filled", "Filled", "com.android.proxy_self.presentation.ui.components.ButtonVariant.Filled"]}, {"name": "HTTP", "description": "com.android.proxy_self.domain.entities.ProxyType.HTTP", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-h-t-t-p/index.html", "searchKeys": ["HTTP", "HTTP", "com.android.proxy_self.domain.entities.ProxyType.HTTP"]}, {"name": "Limited", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.Limited", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-limited/index.html", "searchKeys": ["Limited", "Limited", "com.android.proxy_self.infrastructure.managers.NetworkState.Limited"]}, {"name": "Lost", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.Lost", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-lost/index.html", "searchKeys": ["Lost", "Lost", "com.android.proxy_self.infrastructure.managers.NetworkState.Lost"]}, {"name": "Outlined", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-outlined/index.html", "searchKeys": ["Outlined", "Outlined", "com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined"]}, {"name": "RECONNECTING", "description": "com.android.proxy_self.domain.entities.ProxyStatus.RECONNECTING", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-r-e-c-o-n-n-e-c-t-i-n-g/index.html", "searchKeys": ["RECONNECTING", "RECONNECTING", "com.android.proxy_self.domain.entities.ProxyStatus.RECONNECTING"]}, {"name": "SOCKS5", "description": "com.android.proxy_self.domain.entities.ProxyType.SOCKS5", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-s-o-c-k-s5/index.html", "searchKeys": ["SOCKS5", "SOCKS5", "com.android.proxy_self.domain.entities.ProxyType.SOCKS5"]}, {"name": "Text", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.Text", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-text/index.html", "searchKeys": ["Text", "Text", "com.android.proxy_self.presentation.ui.components.ButtonVariant.Text"]}, {"name": "abstract class BaseUseCase<T, P>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/index.html", "searchKeys": ["BaseUseCase", "abstract class BaseUseCase<T, P>", "com.android.proxy_self.domain.usecases.base.BaseUseCase"]}, {"name": "abstract class BaseUseCaseNoParams<T>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/index.html", "searchKeys": ["BaseUseCaseNoParams", "abstract class BaseUseCaseNoParams<T>", "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams"]}, {"name": "abstract class ProxyDatabase : RoomDatabase", "description": "com.android.proxy_self.data.datasources.local.ProxyDatabase", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/index.html", "searchKeys": ["ProxyDatabase", "abstract class ProxyDatabase : RoomDatabase", "com.android.proxy_self.data.datasources.local.ProxyDatabase"]}, {"name": "abstract class RepositoryModule", "description": "com.android.proxy_self.di.RepositoryModule", "location": "-proxy -self -android -app/com.android.proxy_self.di/-repository-module/index.html", "searchKeys": ["RepositoryModule", "abstract class RepositoryModule", "com.android.proxy_self.di.RepositoryModule"]}, {"name": "abstract fun bindProxyRepository(proxyRepositoryImpl: ProxyRepositoryImpl): ProxyRepository", "description": "com.android.proxy_self.di.RepositoryModule.bindProxyRepository", "location": "-proxy -self -android -app/com.android.proxy_self.di/-repository-module/bind-proxy-repository.html", "searchKeys": ["bindProxyRepository", "abstract fun bindProxyRepository(proxyRepositoryImpl: ProxyRepositoryImpl): ProxyRepository", "com.android.proxy_self.di.RepositoryModule.bindProxyRepository"]}, {"name": "abstract fun getAllConfigs(): Flow<List<ProxyEntity>>", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.getAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-all-configs.html", "searchKeys": ["getAllConfigs", "abstract fun getAllConfigs(): Flow<List<ProxyEntity>>", "com.android.proxy_self.data.datasources.local.ProxyDao.getAllConfigs"]}, {"name": "abstract fun getProxyStatus(): Flow<ProxyStatusInfo>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.getProxyStatus", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-proxy-status.html", "searchKeys": ["getProxyStatus", "abstract fun getProxyStatus(): Flow<ProxyStatusInfo>", "com.android.proxy_self.domain.repositories.ProxyRepository.getProxyStatus"]}, {"name": "abstract fun isProxyEnabled(): Flow<Boolean>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.isProxyEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/is-proxy-enabled.html", "searchKeys": ["isProxyEnabled", "abstract fun isProxyEnabled(): Flow<Boolean>", "com.android.proxy_self.domain.repositories.ProxyRepository.isProxyEnabled"]}, {"name": "abstract fun proxyDao(): ProxyDao", "description": "com.android.proxy_self.data.datasources.local.ProxyDatabase.proxyDao", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/proxy-dao.html", "searchKeys": ["proxyDao", "abstract fun proxyDao(): ProxyDao", "com.android.proxy_self.data.datasources.local.ProxyDatabase.proxyDao"]}, {"name": "abstract suspend fun deleteAllConfigs()", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.deleteAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-all-configs.html", "searchKeys": ["deleteAllConfigs", "abstract suspend fun deleteAllConfigs()", "com.android.proxy_self.data.datasources.local.ProxyDao.deleteAllConfigs"]}, {"name": "abstract suspend fun deleteConfig(config: ProxyEntity)", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.deleteConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-config.html", "searchKeys": ["deleteConfig", "abstract suspend fun deleteConfig(config: ProxyEntity)", "com.android.proxy_self.data.datasources.local.ProxyDao.deleteConfig"]}, {"name": "abstract suspend fun deleteConfig(configId: Long): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.deleteConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/delete-config.html", "searchKeys": ["deleteConfig", "abstract suspend fun deleteConfig(configId: Long): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.deleteConfig"]}, {"name": "abstract suspend fun deleteConfigById(id: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.deleteConfigById", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-config-by-id.html", "searchKeys": ["deleteConfigById", "abstract suspend fun deleteConfigById(id: Long)", "com.android.proxy_self.data.datasources.local.ProxyDao.deleteConfigById"]}, {"name": "abstract suspend fun disableAllConfigs()", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.disableAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/disable-all-configs.html", "searchKeys": ["disableAllConfigs", "abstract suspend fun disableAllConfigs()", "com.android.proxy_self.data.datasources.local.ProxyDao.disableAllConfigs"]}, {"name": "abstract suspend fun enableConfigById(id: Long, timestamp: Long = System.currentTimeMillis())", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.enableConfigById", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/enable-config-by-id.html", "searchKeys": ["enableConfigById", "abstract suspend fun enableConfigById(id: Long, timestamp: Long = System.currentTimeMillis())", "com.android.proxy_self.data.datasources.local.ProxyDao.enableConfigById"]}, {"name": "abstract suspend fun execute(): Flow<Result<T>>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/execute.html", "searchKeys": ["execute", "abstract suspend fun execute(): Flow<Result<T>>", "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.execute"]}, {"name": "abstract suspend fun execute(params: P): Flow<Result<T>>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/execute.html", "searchKeys": ["execute", "abstract suspend fun execute(params: P): Flow<Result<T>>", "com.android.proxy_self.domain.usecases.base.BaseUseCase.execute"]}, {"name": "abstract suspend fun getAllConfigs(): Flow<Result<List<ProxyConfig>>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.getAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-all-configs.html", "searchKeys": ["getAllConfigs", "abstract suspend fun getAllConfigs(): Flow<Result<List<ProxyConfig>>>", "com.android.proxy_self.domain.repositories.ProxyRepository.getAllConfigs"]}, {"name": "abstract suspend fun getConfigById(id: Long): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.getConfigById", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-config-by-id.html", "searchKeys": ["getConfigById", "abstract suspend fun getConfigById(id: Long): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyDao.getConfigById"]}, {"name": "abstract suspend fun getConfigCount(): Int", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.getConfigCount", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-config-count.html", "searchKeys": ["getConfigCount", "abstract suspend fun getConfigCount(): Int", "com.android.proxy_self.data.datasources.local.ProxyDao.getConfigCount"]}, {"name": "abstract suspend fun getEnabledConfig(): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.getEnabledConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-enabled-config.html", "searchKeys": ["getEnabledConfig", "abstract suspend fun getEnabledConfig(): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyDao.getEnabledConfig"]}, {"name": "abstract suspend fun getLatestConfig(): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.getLatestConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-latest-config.html", "searchKeys": ["getLatestConfig", "abstract suspend fun getLatestConfig(): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyDao.getLatestConfig"]}, {"name": "abstract suspend fun getProxyConfig(): Flow<Result<ProxyConfig>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.getProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-proxy-config.html", "searchKeys": ["getProxyConfig", "abstract suspend fun getProxyConfig(): Flow<Result<ProxyConfig>>", "com.android.proxy_self.domain.repositories.ProxyRepository.getProxyConfig"]}, {"name": "abstract suspend fun insertConfig(config: ProxyEntity): Long", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.insertConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/insert-config.html", "searchKeys": ["insertConfig", "abstract suspend fun insertConfig(config: ProxyEntity): Long", "com.android.proxy_self.data.datasources.local.ProxyDao.insertConfig"]}, {"name": "abstract suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.saveProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/save-proxy-config.html", "searchKeys": ["saveProxyConfig", "abstract suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.saveProxyConfig"]}, {"name": "abstract suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.startProxy", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/start-proxy.html", "searchKeys": ["startProxy", "abstract suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.startProxy"]}, {"name": "abstract suspend fun stopProxy(): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.stopProxy", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/stop-proxy.html", "searchKeys": ["stopProxy", "abstract suspend fun stopProxy(): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.stopProxy"]}, {"name": "abstract suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.testConnection", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/test-connection.html", "searchKeys": ["testConnection", "abstract suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.testConnection"]}, {"name": "abstract suspend fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.repositories.ProxyRepository.updateConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/update-config.html", "searchKeys": ["updateConfig", "abstract suspend fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.repositories.ProxyRepository.updateConfig"]}, {"name": "abstract suspend fun updateConfig(config: ProxyEntity)", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.updateConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/update-config.html", "searchKeys": ["updateConfig", "abstract suspend fun updateConfig(config: ProxyEntity)", "com.android.proxy_self.data.datasources.local.ProxyDao.updateConfig"]}, {"name": "class AutoStartProxyWorker constructor(context: Context, workerParams: WorkerParameters, getProxyConfigUseCase: GetProxyConfigUseCase) : CoroutineWorker", "description": "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/index.html", "searchKeys": ["AutoStartProxyWorker", "class AutoStartProxyWorker constructor(context: Context, workerParams: WorkerParameters, getProxyConfigUseCase: GetProxyConfigUseCase) : CoroutineWorker", "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker"]}, {"name": "class BootReceiver : BroadcastReceiver", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/index.html", "searchKeys": ["BootReceiver", "class BootReceiver : BroadcastReceiver", "com.android.proxy_self.infrastructure.receivers.BootReceiver"]}, {"name": "class EncryptionUtils constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/index.html", "searchKeys": ["EncryptionUtils", "class EncryptionUtils constructor(context: Context)", "com.android.proxy_self.infrastructure.utils.EncryptionUtils"]}, {"name": "class GetProxyConfigUseCase constructor(repository: ProxyRepository) : BaseUseCaseNoParams<ProxyConfig> ", "description": "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/index.html", "searchKeys": ["GetProxyConfigUseCase", "class GetProxyConfigUseCase constructor(repository: ProxyRepository) : BaseUseCaseNoParams<ProxyConfig> ", "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase"]}, {"name": "class MainActivity : ComponentActivity", "description": "com.android.proxy_self.MainActivity", "location": "-proxy -self -android -app/com.android.proxy_self/-main-activity/index.html", "searchKeys": ["MainActivity", "class MainActivity : ComponentActivity", "com.android.proxy_self.MainActivity"]}, {"name": "class NetworkManager constructor(context: Context, networkUtils: NetworkUtils)", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/index.html", "searchKeys": ["NetworkManager", "class NetworkManager constructor(context: Context, networkUtils: NetworkUtils)", "com.android.proxy_self.infrastructure.managers.NetworkManager"]}, {"name": "class NetworkUtils constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/index.html", "searchKeys": ["NetworkUtils", "class NetworkUtils constructor(context: Context)", "com.android.proxy_self.infrastructure.utils.NetworkUtils"]}, {"name": "class ProxyApplication : Application, Configuration.Provider", "description": "com.android.proxy_self.ProxyApplication", "location": "-proxy -self -android -app/com.android.proxy_self/-proxy-application/index.html", "searchKeys": ["ProxyApplication", "class ProxyApplication : Application, Configuration.Provider", "com.android.proxy_self.ProxyApplication"]}, {"name": "class ProxyLocalDataSource constructor(proxyDao: ProxyDao)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/index.html", "searchKeys": ["ProxyLocalDataSource", "class ProxyLocalDataSource constructor(proxyDao: ProxyDao)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource"]}, {"name": "class ProxyManager constructor(networkUtils: NetworkUtils)", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/index.html", "searchKeys": ["ProxyManager", "class ProxyManager constructor(networkUtils: NetworkUtils)", "com.android.proxy_self.infrastructure.managers.ProxyManager"]}, {"name": "class ProxyMapper constructor", "description": "com.android.proxy_self.data.mappers.ProxyMapper", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/index.html", "searchKeys": ["ProxyMapper", "class ProxyMapper constructor", "com.android.proxy_self.data.mappers.ProxyMapper"]}, {"name": "class ProxyNotificationManager constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/index.html", "searchKeys": ["ProxyNotificationManager", "class ProxyNotificationManager constructor(context: Context)", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager"]}, {"name": "class ProxyQuickSettingsTile : TileService", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/index.html", "searchKeys": ["ProxyQuickSettingsTile", "class ProxyQuickSettingsTile : TileService", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile"]}, {"name": "class ProxyRemoteDataSource constructor", "description": "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/index.html", "searchKeys": ["ProxyRemoteDataSource", "class ProxyRemoteDataSource constructor", "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource"]}, {"name": "class ProxyRepositoryImpl constructor(localDataSource: ProxyLocalDataSource, remoteDataSource: ProxyRemoteDataSource, mapper: ProxyMapper, encryptionUtils: EncryptionUtils) : ProxyRepository", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/index.html", "searchKeys": ["ProxyRepositoryImpl", "class ProxyRepositoryImpl constructor(localDataSource: ProxyLocalDataSource, remoteDataSource: ProxyRemoteDataSource, mapper: ProxyMapper, encryptionUtils: EncryptionUtils) : ProxyRepository", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl"]}, {"name": "class ProxyService : Service", "description": "com.android.proxy_self.infrastructure.services.ProxyService", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/index.html", "searchKeys": ["ProxyService", "class ProxyService : Service", "com.android.proxy_self.infrastructure.services.ProxyService"]}, {"name": "class ProxyViewModel constructor(startProxyUseCase: StartProxyUseCase, stopProxyUseCase: StopProxyUseCase, saveProxyConfigUseCase: SaveProxyConfigUseCase, getProxyConfigUseCase: GetProxyConfigUseCase, testProxyConnectionUseCase: TestProxyConnectionUseCase, validationUtils: ValidationUtils) : ViewModel", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/index.html", "searchKeys": ["ProxyViewModel", "class ProxyViewModel constructor(startProxyUseCase: StartProxyUseCase, stopProxyUseCase: StopProxyUseCase, saveProxyConfigUseCase: SaveProxyConfigUseCase, getProxyConfigUseCase: GetProxyConfigUseCase, testProxyConnectionUseCase: TestProxyConnectionUseCase, validationUtils: ValidationUtils) : ViewModel", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel"]}, {"name": "class ProxyVpnService : VpnService", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/index.html", "searchKeys": ["ProxyVpnService", "class ProxyVpnService : VpnService", "com.android.proxy_self.infrastructure.services.ProxyVpnService"]}, {"name": "class SaveProxyConfigUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "description": "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/index.html", "searchKeys": ["SaveProxyConfigUseCase", "class SaveProxyConfigUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase"]}, {"name": "class SettingsViewModel constructor(context: Context) : ViewModel", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/index.html", "searchKeys": ["SettingsViewModel", "class SettingsViewModel constructor(context: Context) : ViewModel", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel"]}, {"name": "class StartProxyUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "description": "com.android.proxy_self.domain.usecases.StartProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/index.html", "searchKeys": ["StartProxyUseCase", "class StartProxyUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "com.android.proxy_self.domain.usecases.StartProxyUseCase"]}, {"name": "class StopProxyUseCase constructor(repository: ProxyRepository) : BaseUseCaseNoParams<Boolean> ", "description": "com.android.proxy_self.domain.usecases.StopProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/index.html", "searchKeys": ["StopProxyUseCase", "class StopProxyUseCase constructor(repository: ProxyRepository) : BaseUseCaseNoParams<Boolean> ", "com.android.proxy_self.domain.usecases.StopProxyUseCase"]}, {"name": "class TestProxyConnectionUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "description": "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/index.html", "searchKeys": ["TestProxyConnectionUseCase", "class TestProxyConnectionUseCase constructor(repository: ProxyRepository) : BaseUseCase<Boolean, ProxyConfig> ", "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase"]}, {"name": "class ValidationUtils constructor", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/index.html", "searchKeys": ["ValidationUtils", "class ValidationUtils constructor", "com.android.proxy_self.infrastructure.utils.ValidationUtils"]}, {"name": "const val ACTION_START_PROXY: String", "description": "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_START_PROXY", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-s-t-a-r-t_-p-r-o-x-y.html", "searchKeys": ["ACTION_START_PROXY", "const val ACTION_START_PROXY: String", "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_START_PROXY"]}, {"name": "const val ACTION_START_VPN: String", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.ACTION_START_VPN", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-a-c-t-i-o-n_-s-t-a-r-t_-v-p-n.html", "searchKeys": ["ACTION_START_VPN", "const val ACTION_START_VPN: String", "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.ACTION_START_VPN"]}, {"name": "const val ACTION_STOP_PROXY: String", "description": "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_STOP_PROXY", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-s-t-o-p_-p-r-o-x-y.html", "searchKeys": ["ACTION_STOP_PROXY", "const val ACTION_STOP_PROXY: String", "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_STOP_PROXY"]}, {"name": "const val ACTION_STOP_VPN: String", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.ACTION_STOP_VPN", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-a-c-t-i-o-n_-s-t-o-p_-v-p-n.html", "searchKeys": ["ACTION_STOP_VPN", "const val ACTION_STOP_VPN: String", "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.ACTION_STOP_VPN"]}, {"name": "const val ACTION_UPDATE_CONFIG: String", "description": "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_UPDATE_CONFIG", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-u-p-d-a-t-e_-c-o-n-f-i-g.html", "searchKeys": ["ACTION_UPDATE_CONFIG", "const val ACTION_UPDATE_CONFIG: String", "com.android.proxy_self.infrastructure.services.ProxyService.Companion.ACTION_UPDATE_CONFIG"]}, {"name": "const val CHANNEL_ID: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.CHANNEL_ID", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-c-h-a-n-n-e-l_-i-d.html", "searchKeys": ["CHANNEL_ID", "const val CHANNEL_ID: String", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.CHANNEL_ID"]}, {"name": "const val EXTRA_PROXY_CONFIG: String", "description": "com.android.proxy_self.infrastructure.services.ProxyService.Companion.EXTRA_PROXY_CONFIG", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-e-x-t-r-a_-p-r-o-x-y_-c-o-n-f-i-g.html", "searchKeys": ["EXTRA_PROXY_CONFIG", "const val EXTRA_PROXY_CONFIG: String", "com.android.proxy_self.infrastructure.services.ProxyService.Companion.EXTRA_PROXY_CONFIG"]}, {"name": "const val EXTRA_PROXY_CONFIG: String", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.EXTRA_PROXY_CONFIG", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-e-x-t-r-a_-p-r-o-x-y_-c-o-n-f-i-g.html", "searchKeys": ["EXTRA_PROXY_CONFIG", "const val EXTRA_PROXY_CONFIG: String", "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion.EXTRA_PROXY_CONFIG"]}, {"name": "const val NOTIFICATION_ID: Int = 1001", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.NOTIFICATION_ID", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-n-o-t-i-f-i-c-a-t-i-o-n_-i-d.html", "searchKeys": ["NOTIFICATION_ID", "const val NOTIFICATION_ID: Int = 1001", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.NOTIFICATION_ID"]}, {"name": "const val OPEN_APP_ACTION: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.OPEN_APP_ACTION", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-o-p-e-n_-a-p-p_-a-c-t-i-o-n.html", "searchKeys": ["OPEN_APP_ACTION", "const val OPEN_APP_ACTION: String", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.OPEN_APP_ACTION"]}, {"name": "const val STOP_ACTION: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.STOP_ACTION", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-s-t-o-p_-a-c-t-i-o-n.html", "searchKeys": ["STOP_ACTION", "const val STOP_ACTION: String", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion.STOP_ACTION"]}, {"name": "constructor()", "description": "com.android.proxy_self.MainActivity.MainActivity", "location": "-proxy -self -android -app/com.android.proxy_self/-main-activity/-main-activity.html", "searchKeys": ["MainActivity", "constructor()", "com.android.proxy_self.MainActivity.MainActivity"]}, {"name": "constructor()", "description": "com.android.proxy_self.ProxyApplication.ProxyApplication", "location": "-proxy -self -android -app/com.android.proxy_self/-proxy-application/-proxy-application.html", "searchKeys": ["ProxyApplication", "constructor()", "com.android.proxy_self.ProxyApplication.ProxyApplication"]}, {"name": "constructor()", "description": "com.android.proxy_self.data.datasources.local.ProxyDatabase.ProxyDatabase", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-proxy-database.html", "searchKeys": ["ProxyDatabase", "constructor()", "com.android.proxy_self.data.datasources.local.ProxyDatabase.ProxyDatabase"]}, {"name": "constructor()", "description": "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.ProxyRemoteDataSource", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/-proxy-remote-data-source.html", "searchKeys": ["ProxyRemoteDataSource", "constructor()", "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.ProxyRemoteDataSource"]}, {"name": "constructor()", "description": "com.android.proxy_self.data.mappers.ProxyMapper.ProxyMapper", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/-proxy-mapper.html", "searchKeys": ["ProxyMapper", "constructor()", "com.android.proxy_self.data.mappers.ProxyMapper.ProxyMapper"]}, {"name": "constructor()", "description": "com.android.proxy_self.di.RepositoryModule.RepositoryModule", "location": "-proxy -self -android -app/com.android.proxy_self.di/-repository-module/-repository-module.html", "searchKeys": ["RepositoryModule", "constructor()", "com.android.proxy_self.di.RepositoryModule.RepositoryModule"]}, {"name": "constructor()", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCase.BaseUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/-base-use-case.html", "searchKeys": ["BaseUseCase", "constructor()", "com.android.proxy_self.domain.usecases.base.BaseUseCase.BaseUseCase"]}, {"name": "constructor()", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.BaseUseCaseNoParams", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/-base-use-case-no-params.html", "searchKeys": ["BaseUseCaseNoParams", "constructor()", "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.BaseUseCaseNoParams"]}, {"name": "constructor()", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver.BootReceiver", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/-boot-receiver.html", "searchKeys": ["BootReceiver", "constructor()", "com.android.proxy_self.infrastructure.receivers.BootReceiver.BootReceiver"]}, {"name": "constructor()", "description": "com.android.proxy_self.infrastructure.services.ProxyService.ProxyService", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service.html", "searchKeys": ["ProxyService", "constructor()", "com.android.proxy_self.infrastructure.services.ProxyService.ProxyService"]}, {"name": "constructor()", "description": "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder.ProxyServiceBinder", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/-proxy-service-binder.html", "searchKeys": ["ProxyServiceBinder", "constructor()", "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder.ProxyServiceBinder"]}, {"name": "constructor()", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.ProxyVpnService", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-proxy-vpn-service.html", "searchKeys": ["ProxyVpnService", "constructor()", "com.android.proxy_self.infrastructure.services.ProxyVpnService.ProxyVpnService"]}, {"name": "constructor()", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.ValidationUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/-validation-utils.html", "searchKeys": ["ValidationUtils", "constructor()", "com.android.proxy_self.infrastructure.utils.ValidationUtils.ValidationUtils"]}, {"name": "constructor()", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.ProxyQuickSettingsTile", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-proxy-quick-settings-tile.html", "searchKeys": ["ProxyQuickSettingsTile", "constructor()", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.ProxyQuickSettingsTile"]}, {"name": "constructor(appName: String = \"Proxy Self\", appVersion: String = \"\", buildNumber: String = \"\", buildDate: String = \"\", developerName: String = \"Android Developer\", developerEmail: String = \"<EMAIL>\", githubUrl: String = \"\", licenseUrl: String = \"\", privacyPolicyUrl: String = \"\", openSourceLibraries: List<LibraryInfo> = emptyList())", "description": "com.android.proxy_self.presentation.state.AboutUiState.AboutUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/-about-ui-state.html", "searchKeys": ["AboutUiState", "constructor(appName: String = \"Proxy Self\", appVersion: String = \"\", buildNumber: String = \"\", buildDate: String = \"\", developerName: String = \"Android Developer\", developerEmail: String = \"<EMAIL>\", githubUrl: String = \"\", licenseUrl: String = \"\", privacyPolicyUrl: String = \"\", openSourceLibraries: List<LibraryInfo> = emptyList())", "com.android.proxy_self.presentation.state.AboutUiState.AboutUiState"]}, {"name": "constructor(bytesReceived: Long, bytesSent: Long, packetsReceived: Long, packetsSent: Long, timestamp: Long)", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.NetworkStats", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/-network-stats.html", "searchKeys": ["NetworkStats", "constructor(bytesReceived: Long, bytesSent: Long, packetsReceived: Long, packetsSent: Long, timestamp: Long)", "com.android.proxy_self.infrastructure.managers.NetworkStats.NetworkStats"]}, {"name": "constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.ProxyNotificationManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-proxy-notification-manager.html", "searchKeys": ["ProxyNotificationManager", "constructor(context: Context)", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.ProxyNotificationManager"]}, {"name": "constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils.EncryptionUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/-encryption-utils.html", "searchKeys": ["EncryptionUtils", "constructor(context: Context)", "com.android.proxy_self.infrastructure.utils.EncryptionUtils.EncryptionUtils"]}, {"name": "constructor(context: Context)", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.NetworkUtils", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/-network-utils.html", "searchKeys": ["NetworkUtils", "constructor(context: Context)", "com.android.proxy_self.infrastructure.utils.NetworkUtils.NetworkUtils"]}, {"name": "constructor(context: Context)", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.SettingsViewModel", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/-settings-view-model.html", "searchKeys": ["SettingsViewModel", "constructor(context: Context)", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.SettingsViewModel"]}, {"name": "constructor(context: Context, networkUtils: NetworkUtils)", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.NetworkManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/-network-manager.html", "searchKeys": ["NetworkManager", "constructor(context: Context, networkUtils: NetworkUtils)", "com.android.proxy_self.infrastructure.managers.NetworkManager.NetworkManager"]}, {"name": "constructor(context: Context, workerParams: WorkerParameters, getProxyConfigUseCase: GetProxyConfigUseCase)", "description": "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker.AutoStartProxyWorker", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/-auto-start-proxy-worker.html", "searchKeys": ["AutoStartProxyWorker", "constructor(context: Context, workerParams: WorkerParameters, getProxyConfigUseCase: GetProxyConfigUseCase)", "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker.AutoStartProxyWorker"]}, {"name": "constructor(data: T)", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Success.Success", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/-success.html", "searchKeys": ["Success", "constructor(data: T)", "com.android.proxy_self.infrastructure.utils.ValidationResult.Success.Success"]}, {"name": "constructor(error: String)", "description": "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure.Failure", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/-failure.html", "searchKeys": ["Failure", "constructor(error: String)", "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure.Failure"]}, {"name": "constructor(errors: List<String>)", "description": "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid.Invalid", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/-invalid.html", "searchKeys": ["Invalid", "constructor(errors: List<String>)", "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid.Invalid"]}, {"name": "constructor(id: Long = 0, serverAddress: String = \"\", serverPort: Int = 0, username: String = \"\", password: String = \"\", proxyType: ProxyType = ProxyType.SOCKS5, domains: List<String> = emptyList(), isEnabled: Boolean = false, createdAt: Long = System.currentTimeMillis(), updatedAt: Long = System.currentTimeMillis())", "description": "com.android.proxy_self.domain.entities.ProxyConfig.ProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-proxy-config.html", "searchKeys": ["ProxyConfig", "constructor(id: Long = 0, serverAddress: String = \"\", serverPort: Int = 0, username: String = \"\", password: String = \"\", proxyType: ProxyType = ProxyType.SOCKS5, domains: List<String> = emptyList(), isEnabled: Boolean = false, createdAt: Long = System.currentTimeMillis(), updatedAt: Long = System.currentTimeMillis())", "com.android.proxy_self.domain.entities.ProxyConfig.ProxyConfig"]}, {"name": "constructor(id: Long = 0, serverAddress: String, serverPort: Int, username: String, encryptedPassword: String, proxyType: String, domains: String, isEnabled: <PERSON><PERSON>an, createdAt: Long, updatedAt: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.ProxyEntity", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/-proxy-entity.html", "searchKeys": ["ProxyEntity", "constructor(id: Long = 0, serverAddress: String, serverPort: Int, username: String, encryptedPassword: String, proxyType: String, domains: String, isEnabled: <PERSON><PERSON>an, createdAt: Long, updatedAt: Long)", "com.android.proxy_self.data.datasources.local.ProxyEntity.ProxyEntity"]}, {"name": "constructor(isAutoStartEnabled: Boolean = false, isDarkThemeEnabled: Boolean = false, isNotificationsEnabled: Boolean = true, isLoading: Boolean = false, isSaving: Boolean = false, message: String? = null, error: String? = null, isExporting: Boolean = false, isImporting: Boolean = false, isClearing: Boolean = false, appVersion: String = \"\", buildNumber: String = \"\")", "description": "com.android.proxy_self.presentation.state.SettingsUiState.SettingsUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/-settings-ui-state.html", "searchKeys": ["SettingsUiState", "constructor(isAutoStartEnabled: Boolean = false, isDarkThemeEnabled: Boolean = false, isNotificationsEnabled: Boolean = true, isLoading: Boolean = false, isSaving: Boolean = false, message: String? = null, error: String? = null, isExporting: Boolean = false, isImporting: Boolean = false, isClearing: Boolean = false, appVersion: String = \"\", buildNumber: String = \"\")", "com.android.proxy_self.presentation.state.SettingsUiState.SettingsUiState"]}, {"name": "constructor(isAvailable: <PERSON>olean, isWifi: <PERSON><PERSON>an, isCellular: Boolean, isMetered: Boolean, type: String)", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.NetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/-network-info.html", "searchKeys": ["NetworkInfo", "constructor(isAvailable: <PERSON>olean, isWifi: <PERSON><PERSON>an, isCellular: Boolean, isMetered: Boolean, type: String)", "com.android.proxy_self.infrastructure.utils.NetworkInfo.NetworkInfo"]}, {"name": "constructor(isConnected: <PERSON><PERSON><PERSON>, connectionType: String, isWifi: Boolean, isCellular: <PERSON>olean, isMetered: <PERSON>olean, timestamp: Long)", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.NetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/-network-info.html", "searchKeys": ["NetworkInfo", "constructor(isConnected: <PERSON><PERSON><PERSON>, connectionType: String, isWifi: Boolean, isCellular: <PERSON>olean, isMetered: <PERSON>olean, timestamp: Long)", "com.android.proxy_self.infrastructure.managers.NetworkInfo.NetworkInfo"]}, {"name": "constructor(localDataSource: ProxyLocalDataSource, remoteDataSource: ProxyRemoteDataSource, mapper: ProxyMapper, encryptionUtils: EncryptionUtils)", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.ProxyRepositoryImpl", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/-proxy-repository-impl.html", "searchKeys": ["ProxyRepositoryImpl", "constructor(localDataSource: ProxyLocalDataSource, remoteDataSource: ProxyRemoteDataSource, mapper: ProxyMapper, encryptionUtils: EncryptionUtils)", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.ProxyRepositoryImpl"]}, {"name": "constructor(message: String)", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error.Error", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/-error.html", "searchKeys": ["Error", "constructor(message: String)", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error.Error"]}, {"name": "constructor(message: String)", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success.Success", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/-success.html", "searchKeys": ["Success", "constructor(message: String)", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success.Success"]}, {"name": "constructor(message: String)", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Error.Error", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/-error.html", "searchKeys": ["Error", "constructor(message: String)", "com.android.proxy_self.infrastructure.utils.ValidationResult.Error.Error"]}, {"name": "constructor(name: String, version: String, license: String, url: String)", "description": "com.android.proxy_self.presentation.state.LibraryInfo.LibraryInfo", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/-library-info.html", "searchKeys": ["LibraryInfo", "constructor(name: String, version: String, license: String, url: String)", "com.android.proxy_self.presentation.state.LibraryInfo.LibraryInfo"]}, {"name": "constructor(networkUtils: NetworkUtils)", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.ProxyManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/-proxy-manager.html", "searchKeys": ["ProxyManager", "constructor(networkUtils: NetworkUtils)", "com.android.proxy_self.infrastructure.managers.ProxyManager.ProxyManager"]}, {"name": "constructor(proxyConfig: ProxyConfig = ProxyConfig(), isProxyEnabled: Boolean = false, isLoading: Boolean = false, isTestingConnection: Boolean = false, isSaving: Boolean = false, proxyStatus: ProxyStatusInfo? = null, error: String? = null, validationErrors: Map<String, String> = emptyMap(), serverAddress: String = \"\", serverPort: String = \"\", username: String = \"\", password: String = \"\", domains: String = \"\", selectedProxyType: ProxyType = ProxyType.SOCKS5, isPasswordVisible: Boolean = false, successMessage: String? = null, connectionTestResult: ConnectionTestResult? = null)", "description": "com.android.proxy_self.presentation.state.ProxyUiState.ProxyUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/-proxy-ui-state.html", "searchKeys": ["ProxyUiState", "constructor(proxyConfig: ProxyConfig = ProxyConfig(), isProxyEnabled: Boolean = false, isLoading: Boolean = false, isTestingConnection: Boolean = false, isSaving: Boolean = false, proxyStatus: ProxyStatusInfo? = null, error: String? = null, validationErrors: Map<String, String> = emptyMap(), serverAddress: String = \"\", serverPort: String = \"\", username: String = \"\", password: String = \"\", domains: String = \"\", selectedProxyType: ProxyType = ProxyType.SOCKS5, isPasswordVisible: Boolean = false, successMessage: String? = null, connectionTestResult: ConnectionTestResult? = null)", "com.android.proxy_self.presentation.state.ProxyUiState.ProxyUiState"]}, {"name": "constructor(proxyDao: ProxyDao)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.ProxyLocalDataSource", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/-proxy-local-data-source.html", "searchKeys": ["ProxyLocalDataSource", "constructor(proxyDao: ProxyDao)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.ProxyLocalDataSource"]}, {"name": "constructor(repository: ProxyRepository)", "description": "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase.GetProxyConfigUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/-get-proxy-config-use-case.html", "searchKeys": ["GetProxyConfigUseCase", "constructor(repository: ProxyRepository)", "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase.GetProxyConfigUseCase"]}, {"name": "constructor(repository: ProxyRepository)", "description": "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase.SaveProxyConfigUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/-save-proxy-config-use-case.html", "searchKeys": ["SaveProxyConfigUseCase", "constructor(repository: ProxyRepository)", "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase.SaveProxyConfigUseCase"]}, {"name": "constructor(repository: ProxyRepository)", "description": "com.android.proxy_self.domain.usecases.StartProxyUseCase.StartProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/-start-proxy-use-case.html", "searchKeys": ["StartProxyUseCase", "constructor(repository: ProxyRepository)", "com.android.proxy_self.domain.usecases.StartProxyUseCase.StartProxyUseCase"]}, {"name": "constructor(repository: ProxyRepository)", "description": "com.android.proxy_self.domain.usecases.StopProxyUseCase.StopProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/-stop-proxy-use-case.html", "searchKeys": ["StopProxyUseCase", "constructor(repository: ProxyRepository)", "com.android.proxy_self.domain.usecases.StopProxyUseCase.StopProxyUseCase"]}, {"name": "constructor(repository: ProxyRepository)", "description": "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase.TestProxyConnectionUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/-test-proxy-connection-use-case.html", "searchKeys": ["TestProxyConnectionUseCase", "constructor(repository: ProxyRepository)", "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase.TestProxyConnectionUseCase"]}, {"name": "constructor(serverAddress: String, serverPort: Int, proxyType: String, username: String, domainCount: Int, domains: List<String>)", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.ProxyConnectionInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/-proxy-connection-info.html", "searchKeys": ["ProxyConnectionInfo", "constructor(serverAddress: String, serverPort: Int, proxyType: String, username: String, domainCount: Int, domains: List<String>)", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.ProxyConnectionInfo"]}, {"name": "constructor(startProxyUseCase: StartProxyUseCase, stopProxyUseCase: StopProxyUseCase, saveProxyConfigUseCase: SaveProxyConfigUseCase, getProxyConfigUseCase: GetProxyConfigUseCase, testProxyConnectionUseCase: TestProxyConnectionUseCase, validationUtils: ValidationUtils)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.ProxyViewModel", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/-proxy-view-model.html", "searchKeys": ["ProxyViewModel", "constructor(startProxyUseCase: StartProxyUseCase, stopProxyUseCase: StopProxyUseCase, saveProxyConfigUseCase: SaveProxyConfigUseCase, getProxyConfigUseCase: GetProxyConfigUseCase, testProxyConnectionUseCase: TestProxyConnectionUseCase, validationUtils: ValidationUtils)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.ProxyViewModel"]}, {"name": "constructor(status: ProxyStatus = ProxyStatus.DISCONNECTED, connectionTime: Long? = null, lastError: String? = null, bytesTransferred: Long = 0, activeConnections: Int = 0, activeDomains: List<String> = emptyList(), timestamp: Long = System.currentTimeMillis())", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.ProxyStatusInfo", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/-proxy-status-info.html", "searchKeys": ["ProxyStatusInfo", "constructor(status: ProxyStatus = ProxyStatus.DISCONNECTED, connectionTime: Long? = null, lastError: String? = null, bytesTransferred: Long = 0, activeConnections: Int = 0, activeDomains: List<String> = emptyList(), timestamp: Long = System.currentTimeMillis())", "com.android.proxy_self.domain.entities.ProxyStatusInfo.ProxyStatusInfo"]}, {"name": "constructor(version: Int, protocol: Int, sourceIp: ByteArray, destIp: ByteArray, data: ByteArray)", "description": "com.android.proxy_self.infrastructure.services.IpPacket.IpPacket", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/-ip-packet.html", "searchKeys": ["IpPacket", "constructor(version: Int, protocol: Int, sourceIp: ByteArray, destIp: ByteArray, data: ByteArray)", "com.android.proxy_self.infrastructure.services.IpPacket.IpPacket"]}, {"name": "data class AboutUiState(val appName: String = \"Proxy Self\", val appVersion: String = \"\", val buildNumber: String = \"\", val buildDate: String = \"\", val developerName: String = \"Android Developer\", val developerEmail: String = \"<EMAIL>\", val githubUrl: String = \"\", val licenseUrl: String = \"\", val privacyPolicyUrl: String = \"\", val openSourceLibraries: List<LibraryInfo> = emptyList())", "description": "com.android.proxy_self.presentation.state.AboutUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/index.html", "searchKeys": ["AboutUiState", "data class AboutUiState(val appName: String = \"Proxy Self\", val appVersion: String = \"\", val buildNumber: String = \"\", val buildDate: String = \"\", val developerName: String = \"Android Developer\", val developerEmail: String = \"<EMAIL>\", val githubUrl: String = \"\", val licenseUrl: String = \"\", val privacyPolicyUrl: String = \"\", val openSourceLibraries: List<LibraryInfo> = emptyList())", "com.android.proxy_self.presentation.state.AboutUiState"]}, {"name": "data class Error(val message: String) : SocksProxyResult", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/index.html", "searchKeys": ["Error", "data class Error(val message: String) : SocksProxyResult", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error"]}, {"name": "data class Error(val message: String) : ValidationResult<Nothing> ", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Error", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/index.html", "searchKeys": ["Error", "data class Error(val message: String) : ValidationResult<Nothing> ", "com.android.proxy_self.infrastructure.utils.ValidationResult.Error"]}, {"name": "data class Failure(val error: String) : ConnectionTestResult", "description": "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/index.html", "searchKeys": ["Failure", "data class Failure(val error: String) : ConnectionTestResult", "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure"]}, {"name": "data class Invalid(val errors: List<String>) : ProxyValidationResult", "description": "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/index.html", "searchKeys": ["Invalid", "data class Invalid(val errors: List<String>) : ProxyValidationResult", "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid"]}, {"name": "data class IpPacket(val version: Int, val protocol: Int, val sourceIp: ByteArray, val destIp: ByteArray, val data: ByteArray)", "description": "com.android.proxy_self.infrastructure.services.IpPacket", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/index.html", "searchKeys": ["IpPacket", "data class IpPacket(val version: Int, val protocol: Int, val sourceIp: ByteArray, val destIp: ByteArray, val data: ByteArray)", "com.android.proxy_self.infrastructure.services.IpPacket"]}, {"name": "data class LibraryInfo(val name: String, val version: String, val license: String, val url: String)", "description": "com.android.proxy_self.presentation.state.LibraryInfo", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/index.html", "searchKeys": ["LibraryInfo", "data class LibraryInfo(val name: String, val version: String, val license: String, val url: String)", "com.android.proxy_self.presentation.state.LibraryInfo"]}, {"name": "data class NetworkInfo(val isAvailable: Boolean, val isWifi: Boolean, val isCellular: Boolean, val isMetered: Boolean, val type: String)", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/index.html", "searchKeys": ["NetworkInfo", "data class NetworkInfo(val isAvailable: Boolean, val isWifi: Boolean, val isCellular: Boolean, val isMetered: Boolean, val type: String)", "com.android.proxy_self.infrastructure.utils.NetworkInfo"]}, {"name": "data class NetworkInfo(val isConnected: Boolean, val connectionType: String, val isWifi: Boolean, val isCellular: Boolean, val isMetered: <PERSON>olean, val timestamp: Long)", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/index.html", "searchKeys": ["NetworkInfo", "data class NetworkInfo(val isConnected: Boolean, val connectionType: String, val isWifi: Boolean, val isCellular: Boolean, val isMetered: <PERSON>olean, val timestamp: Long)", "com.android.proxy_self.infrastructure.managers.NetworkInfo"]}, {"name": "data class NetworkStats(val bytesReceived: Long, val bytesSent: Long, val packetsReceived: Long, val packetsSent: Long, val timestamp: Long)", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/index.html", "searchKeys": ["NetworkStats", "data class NetworkStats(val bytesReceived: Long, val bytesSent: Long, val packetsReceived: Long, val packetsSent: Long, val timestamp: Long)", "com.android.proxy_self.infrastructure.managers.NetworkStats"]}, {"name": "data class ProxyConfig(val id: Long = 0, val serverAddress: String = \"\", val serverPort: Int = 0, val username: String = \"\", val password: String = \"\", val proxyType: ProxyType = ProxyType.SOCKS5, val domains: List<String> = emptyList(), val isEnabled: Boolean = false, val createdAt: Long = System.currentTimeMillis(), val updatedAt: Long = System.currentTimeMillis()) : Parcelable", "description": "com.android.proxy_self.domain.entities.ProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/index.html", "searchKeys": ["ProxyConfig", "data class ProxyConfig(val id: Long = 0, val serverAddress: String = \"\", val serverPort: Int = 0, val username: String = \"\", val password: String = \"\", val proxyType: ProxyType = ProxyType.SOCKS5, val domains: List<String> = emptyList(), val isEnabled: Boolean = false, val createdAt: Long = System.currentTimeMillis(), val updatedAt: Long = System.currentTimeMillis()) : Parcelable", "com.android.proxy_self.domain.entities.ProxyConfig"]}, {"name": "data class ProxyConnectionInfo(val serverAddress: String, val serverPort: Int, val proxyType: String, val username: String, val domainCount: Int, val domains: List<String>)", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/index.html", "searchKeys": ["ProxyConnectionInfo", "data class ProxyConnectionInfo(val serverAddress: String, val serverPort: Int, val proxyType: String, val username: String, val domainCount: Int, val domains: List<String>)", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo"]}, {"name": "data class ProxyEntity(val id: Long = 0, val serverAddress: String, val serverPort: Int, val username: String, val encryptedPassword: String, val proxyType: String, val domains: String, val isEnabled: Boolean, val createdAt: Long, val updatedAt: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/index.html", "searchKeys": ["ProxyEntity", "data class ProxyEntity(val id: Long = 0, val serverAddress: String, val serverPort: Int, val username: String, val encryptedPassword: String, val proxyType: String, val domains: String, val isEnabled: Boolean, val createdAt: Long, val updatedAt: Long)", "com.android.proxy_self.data.datasources.local.ProxyEntity"]}, {"name": "data class ProxyStatusInfo(val status: ProxyStatus = ProxyStatus.DISCONNECTED, val connectionTime: Long? = null, val lastError: String? = null, val bytesTransferred: Long = 0, val activeConnections: Int = 0, val activeDomains: List<String> = emptyList(), val timestamp: Long = System.currentTimeMillis())", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/index.html", "searchKeys": ["ProxyStatusInfo", "data class ProxyStatusInfo(val status: ProxyStatus = ProxyStatus.DISCONNECTED, val connectionTime: Long? = null, val lastError: String? = null, val bytesTransferred: Long = 0, val activeConnections: Int = 0, val activeDomains: List<String> = emptyList(), val timestamp: Long = System.currentTimeMillis())", "com.android.proxy_self.domain.entities.ProxyStatusInfo"]}, {"name": "data class ProxyUiState(val proxyConfig: ProxyConfig = ProxyConfig(), val isProxyEnabled: Boolean = false, val isLoading: Boolean = false, val isTestingConnection: Boolean = false, val isSaving: Boolean = false, val proxyStatus: ProxyStatusInfo? = null, val error: String? = null, val validationErrors: Map<String, String> = emptyMap(), val serverAddress: String = \"\", val serverPort: String = \"\", val username: String = \"\", val password: String = \"\", val domains: String = \"\", val selectedProxyType: ProxyType = ProxyType.SOCKS5, val isPasswordVisible: Boolean = false, val successMessage: String? = null, val connectionTestResult: ConnectionTestResult? = null)", "description": "com.android.proxy_self.presentation.state.ProxyUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/index.html", "searchKeys": ["ProxyUiState", "data class ProxyUiState(val proxyConfig: ProxyConfig = ProxyConfig(), val isProxyEnabled: Boolean = false, val isLoading: Boolean = false, val isTestingConnection: Boolean = false, val isSaving: Boolean = false, val proxyStatus: ProxyStatusInfo? = null, val error: String? = null, val validationErrors: Map<String, String> = emptyMap(), val serverAddress: String = \"\", val serverPort: String = \"\", val username: String = \"\", val password: String = \"\", val domains: String = \"\", val selectedProxyType: ProxyType = ProxyType.SOCKS5, val isPasswordVisible: Boolean = false, val successMessage: String? = null, val connectionTestResult: ConnectionTestResult? = null)", "com.android.proxy_self.presentation.state.ProxyUiState"]}, {"name": "data class SettingsUiState(val isAutoStartEnabled: <PERSON>olean = false, val isDarkThemeEnabled: <PERSON>olean = false, val isNotificationsEnabled: <PERSON>olean = true, val isLoading: <PERSON>olean = false, val isSaving: Boolean = false, val message: String? = null, val error: String? = null, val isExporting: Boolean = false, val isImporting: Boolean = false, val isClearing: Boolean = false, val appVersion: String = \"\", val buildNumber: String = \"\")", "description": "com.android.proxy_self.presentation.state.SettingsUiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/index.html", "searchKeys": ["SettingsUiState", "data class SettingsUiState(val isAutoStartEnabled: <PERSON>olean = false, val isDarkThemeEnabled: <PERSON>olean = false, val isNotificationsEnabled: <PERSON>olean = true, val isLoading: <PERSON>olean = false, val isSaving: Boolean = false, val message: String? = null, val error: String? = null, val isExporting: Boolean = false, val isImporting: Boolean = false, val isClearing: Boolean = false, val appVersion: String = \"\", val buildNumber: String = \"\")", "com.android.proxy_self.presentation.state.SettingsUiState"]}, {"name": "data class Success(val message: String) : SocksProxyResult", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/index.html", "searchKeys": ["Success", "data class Success(val message: String) : SocksProxyResult", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success"]}, {"name": "data class Success<T>(val data: T) : ValidationResult<T> ", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Success", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/index.html", "searchKeys": ["Success", "data class Success<T>(val data: T) : ValidationResult<T> ", "com.android.proxy_self.infrastructure.utils.ValidationResult.Success"]}, {"name": "enum ButtonVariant : Enum<ButtonVariant> ", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/index.html", "searchKeys": ["ButtonV<PERSON>t", "enum ButtonVariant : Enum<ButtonVariant> ", "com.android.proxy_self.presentation.ui.components.ButtonVariant"]}, {"name": "enum NetworkState : Enum<NetworkState> ", "description": "com.android.proxy_self.infrastructure.managers.NetworkState", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/index.html", "searchKeys": ["NetworkState", "enum NetworkState : Enum<NetworkState> ", "com.android.proxy_self.infrastructure.managers.NetworkState"]}, {"name": "enum ProxyStatus : Enum<ProxyStatus> ", "description": "com.android.proxy_self.domain.entities.ProxyStatus", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/index.html", "searchKeys": ["ProxyStatus", "enum ProxyStatus : Enum<ProxyStatus> ", "com.android.proxy_self.domain.entities.ProxyStatus"]}, {"name": "enum ProxyType : Enum<ProxyType> ", "description": "com.android.proxy_self.domain.entities.ProxyType", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/index.html", "searchKeys": ["ProxyType", "enum ProxyType : Enum<ProxyType> ", "com.android.proxy_self.domain.entities.ProxyType"]}, {"name": "fun <T> T.asSuccess(): Result<T>", "description": "com.android.proxy_self.domain.usecases.base.asSuccess", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/as-success.html", "searchKeys": ["asSuccess", "fun <T> T.asSuccess(): Result<T>", "com.android.proxy_self.domain.usecases.base.asSuccess"]}, {"name": "fun <T> Throwable.asFailure(): Result<T>", "description": "com.android.proxy_self.domain.usecases.base.asFailure", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/as-failure.html", "searchKeys": ["asFailure", "fun <T> Throwable.asFailure(): Result<T>", "com.android.proxy_self.domain.usecases.base.asFailure"]}, {"name": "fun AboutScreen(onNavigateBack: () -> Unit)", "description": "com.android.proxy_self.presentation.ui.screens.AboutScreen", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-about-screen.html", "searchKeys": ["AboutScreen", "fun AboutScreen(onNavigateBack: () -> Unit)", "com.android.proxy_self.presentation.ui.screens.AboutScreen"]}, {"name": "fun LoadingButton(onClick: () -> Unit, text: String, modifier: Modifier = Modifier, isLoading: Boolean = false, enabled: Boolean = true, variant: ButtonVariant = ButtonVariant.Filled)", "description": "com.android.proxy_self.presentation.ui.components.LoadingButton", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-loading-button.html", "searchKeys": ["LoadingButton", "fun LoadingButton(onClick: () -> Unit, text: String, modifier: Modifier = Modifier, isLoading: Boolean = false, enabled: Boolean = true, variant: ButtonVariant = ButtonVariant.Filled)", "com.android.proxy_self.presentation.ui.components.LoadingButton"]}, {"name": "fun ProxyConfigForm(uiState: ProxyUiState, onServerAddressChange: (String) -> Unit, onServerPortChange: (String) -> Unit, onUsernameChange: (String) -> Unit, onPasswordChange: (String) -> Unit, onDomainsChange: (String) -> Unit, onProxyTypeChange: (ProxyType) -> Unit, onPasswordVisibilityToggle: () -> Unit, modifier: Modifier = Modifier)", "description": "com.android.proxy_self.presentation.ui.components.ProxyConfigForm", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-proxy-config-form.html", "searchKeys": ["ProxyConfigForm", "fun ProxyConfigForm(uiState: ProxyUiState, onServerAddressChange: (String) -> Unit, onServerPortChange: (String) -> Unit, onUsernameChange: (String) -> Unit, onPasswordChange: (String) -> Unit, onDomainsChange: (String) -> Unit, onProxyTypeChange: (ProxyType) -> Unit, onPasswordVisibilityToggle: () -> Unit, modifier: Modifier = Modifier)", "com.android.proxy_self.presentation.ui.components.ProxyConfigForm"]}, {"name": "fun ProxyConfigScreen(onNavigateToSettings: () -> Unit, onNavigateToAbout: () -> Unit, viewModel: ProxyViewModel = hiltViewModel())", "description": "com.android.proxy_self.presentation.ui.screens.ProxyConfigScreen", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-proxy-config-screen.html", "searchKeys": ["ProxyConfigScreen", "fun ProxyConfigScreen(onNavigateToSettings: () -> Unit, onNavigateToAbout: () -> Unit, viewModel: ProxyViewModel = hiltViewModel())", "com.android.proxy_self.presentation.ui.screens.ProxyConfigScreen"]}, {"name": "fun ProxyNavigation(navController: NavHostController = rememberNavController())", "description": "com.android.proxy_self.presentation.ui.navigation.ProxyNavigation", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-proxy-navigation.html", "searchKeys": ["ProxyNavigation", "fun ProxyNavigation(navController: NavHostController = rememberNavController())", "com.android.proxy_self.presentation.ui.navigation.ProxyNavigation"]}, {"name": "fun ProxySelfTheme(darkTheme: Boolean = isSystemInDarkTheme(), dynamicColor: Boolean = true, content: () -> Unit)", "description": "com.android.proxy_self.presentation.ui.theme.ProxySelfTheme", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-self-theme.html", "searchKeys": ["ProxySelfTheme", "fun ProxySelfTheme(darkTheme: Boolean = isSystemInDarkTheme(), dynamicColor: Boolean = true, content: () -> Unit)", "com.android.proxy_self.presentation.ui.theme.ProxySelfTheme"]}, {"name": "fun ProxyStatusCard(statusInfo: ProxyStatusInfo?, isEnabled: Boolean, modifier: Modifier = Modifier)", "description": "com.android.proxy_self.presentation.ui.components.ProxyStatusCard", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-proxy-status-card.html", "searchKeys": ["ProxyStatusCard", "fun ProxyStatusCard(statusInfo: ProxyStatusInfo?, isEnabled: Boolean, modifier: Modifier = Modifier)", "com.android.proxy_self.presentation.ui.components.ProxyStatusCard"]}, {"name": "fun SettingsScreen(onNavigateBack: () -> Unit, onNavigateToAbout: () -> Unit, viewModel: SettingsViewModel = hiltViewModel())", "description": "com.android.proxy_self.presentation.ui.screens.SettingsScreen", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-settings-screen.html", "searchKeys": ["SettingsScreen", "fun SettingsScreen(onNavigateBack: () -> Unit, onNavigateToAbout: () -> Unit, viewModel: SettingsViewModel = hiltViewModel())", "com.android.proxy_self.presentation.ui.screens.SettingsScreen"]}, {"name": "fun cancelAllNotifications()", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.cancelAllNotifications", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/cancel-all-notifications.html", "searchKeys": ["cancelAllNotifications", "fun cancelAllNotifications()", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.cancelAllNotifications"]}, {"name": "fun cancelAutoStartWork(context: Context)", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver.cancelAutoStartWork", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/cancel-auto-start-work.html", "searchKeys": ["cancelAutoStartWork", "fun cancelAutoStartWork(context: Context)", "com.android.proxy_self.infrastructure.receivers.BootReceiver.cancelAutoStartWork"]}, {"name": "fun cancelNotification(notificationId: Int)", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.cancelNotification", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/cancel-notification.html", "searchKeys": ["cancelNotification", "fun cancelNotification(notificationId: Int)", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.cancelNotification"]}, {"name": "fun clearAllData()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearAllData", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-all-data.html", "searchKeys": ["clearAllData", "fun clearAllData()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearAllData"]}, {"name": "fun clearAllEncryptedData()", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils.clearAllEncryptedData", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/clear-all-encrypted-data.html", "searchKeys": ["clearAllEncryptedData", "fun clearAllEncryptedData()", "com.android.proxy_self.infrastructure.utils.EncryptionUtils.clearAllEncryptedData"]}, {"name": "fun clearConnectionTestResult()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearConnectionTestResult", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-connection-test-result.html", "searchKeys": ["clearConnectionTestResult", "fun clearConnectionTestResult()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearConnectionTestResult"]}, {"name": "fun clearError()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearError", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-error.html", "searchKeys": ["clearError", "fun clearError()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearError"]}, {"name": "fun clearError()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearError", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-error.html", "searchKeys": ["clearError", "fun clearError()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearError"]}, {"name": "fun clearMessage()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearMessage", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-message.html", "searchKeys": ["clearMessage", "fun clearMessage()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.clearMessage"]}, {"name": "fun clearSuccessMessage()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearSuccessMessage", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-success-message.html", "searchKeys": ["clearSuccessMessage", "fun clearSuccessMessage()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.clearSuccessMessage"]}, {"name": "fun createServiceNotification(status: ProxyStatus, serverHost: String = \"\", serverPort: Int = 0): Notification", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.createServiceNotification", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/create-service-notification.html", "searchKeys": ["createServiceNotification", "fun createServiceNotification(status: ProxyStatus, serverHost: String = \"\", serverPort: Int = 0): Notification", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.createServiceNotification"]}, {"name": "fun decrypt(encryptedText: String): String", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils.decrypt", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/decrypt.html", "searchKeys": ["decrypt", "fun decrypt(encryptedText: String): String", "com.android.proxy_self.infrastructure.utils.EncryptionUtils.decrypt"]}, {"name": "fun encrypt(plainText: String): String", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils.encrypt", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/encrypt.html", "searchKeys": ["encrypt", "fun encrypt(plainText: String): String", "com.android.proxy_self.infrastructure.utils.EncryptionUtils.encrypt"]}, {"name": "fun exportConfiguration()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.exportConfiguration", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/export-configuration.html", "searchKeys": ["exportConfiguration", "fun exportConfiguration()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.exportConfiguration"]}, {"name": "fun extractDomain(url: String): String?", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.extractDomain", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/extract-domain.html", "searchKeys": ["extractDomain", "fun extractDomain(url: String): String?", "com.android.proxy_self.infrastructure.managers.NetworkManager.extractDomain"]}, {"name": "fun extractDomainFromUrl(url: String): String?", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.extractDomainFromUrl", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/extract-domain-from-url.html", "searchKeys": ["extractDomainFromUrl", "fun extractDomainFromUrl(url: String): String?", "com.android.proxy_self.infrastructure.utils.NetworkUtils.extractDomainFromUrl"]}, {"name": "fun fromDomainsString(id: Long = 0, serverAddress: String = \"\", serverPort: Int = 0, username: String = \"\", password: String = \"\", proxyType: ProxyType = ProxyType.SOCKS5, domainsString: String = \"\", isEnabled: Boolean = false, createdAt: Long = System.currentTimeMillis(), updatedAt: Long = System.currentTimeMillis()): ProxyConfig", "description": "com.android.proxy_self.domain.entities.ProxyConfig.Companion.fromDomainsString", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-companion/from-domains-string.html", "searchKeys": ["fromDomainsString", "fun fromDomainsString(id: Long = 0, serverAddress: String = \"\", serverPort: Int = 0, username: String = \"\", password: String = \"\", proxyType: ProxyType = ProxyType.SOCKS5, domainsString: String = \"\", isEnabled: Boolean = false, createdAt: Long = System.currentTimeMillis(), updatedAt: Long = System.currentTimeMillis()): ProxyConfig", "com.android.proxy_self.domain.entities.ProxyConfig.Companion.fromDomainsString"]}, {"name": "fun fromValue(value: String): ProxyType", "description": "com.android.proxy_self.domain.entities.ProxyType.Companion.fromValue", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/from-value.html", "searchKeys": ["fromValue", "fun fromValue(value: String): ProxyType", "com.android.proxy_self.domain.entities.ProxyType.Companion.fromValue"]}, {"name": "fun getActiveNetworkCapabilities(): NetworkCapabilities?", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.getActiveNetworkCapabilities", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-active-network-capabilities.html", "searchKeys": ["getActiveNetworkCapabilities", "fun getActiveNetworkCapabilities(): NetworkCapabilities?", "com.android.proxy_self.infrastructure.managers.NetworkManager.getActiveNetworkCapabilities"]}, {"name": "fun getAllConfigs(): Flow<List<ProxyEntity>>", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-all-configs.html", "searchKeys": ["getAllConfigs", "fun getAllConfigs(): Flow<List<ProxyEntity>>", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getAllConfigs"]}, {"name": "fun getConnectionDuration(): String?", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.getConnectionDuration", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/get-connection-duration.html", "searchKeys": ["getConnectionDuration", "fun getConnectionDuration(): String?", "com.android.proxy_self.domain.entities.ProxyStatusInfo.getConnectionDuration"]}, {"name": "fun getCurrentNetworkState(): NetworkState", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.getCurrentNetworkState", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-current-network-state.html", "searchKeys": ["getCurrentNetworkState", "fun getCurrentNetworkState(): NetworkState", "com.android.proxy_self.infrastructure.managers.NetworkManager.getCurrentNetworkState"]}, {"name": "fun getCurrentStatus(): ProxyStatusInfo", "description": "com.android.proxy_self.infrastructure.services.ProxyService.getCurrentStatus", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/get-current-status.html", "searchKeys": ["getCurrentStatus", "fun getCurrentStatus(): ProxyStatusInfo", "com.android.proxy_self.infrastructure.services.ProxyService.getCurrentStatus"]}, {"name": "fun getDatabase(context: Context): ProxyDatabase", "description": "com.android.proxy_self.data.datasources.local.ProxyDatabase.Companion.getDatabase", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-companion/get-database.html", "searchKeys": ["getDatabase", "fun getDatabase(context: Context): ProxyDatabase", "com.android.proxy_self.data.datasources.local.ProxyDatabase.Companion.getDatabase"]}, {"name": "fun getDisplayNames(): List<String>", "description": "com.android.proxy_self.domain.entities.ProxyType.Companion.getDisplayNames", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/get-display-names.html", "searchKeys": ["getDisplayNames", "fun getDisplayNames(): List<String>", "com.android.proxy_self.domain.entities.ProxyType.Companion.getDisplayNames"]}, {"name": "fun getDomainsAsString(): String", "description": "com.android.proxy_self.domain.entities.ProxyConfig.getDomainsAsString", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/get-domains-as-string.html", "searchKeys": ["getDomainsAsString", "fun getDomainsAsString(): String", "com.android.proxy_self.domain.entities.ProxyConfig.getDomainsAsString"]}, {"name": "fun getDomainsList(): List<String>", "description": "com.android.proxy_self.presentation.state.ProxyUiState.getDomainsList", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/get-domains-list.html", "searchKeys": ["getDomainsList", "fun getDomainsList(): List<String>", "com.android.proxy_self.presentation.state.ProxyUiState.getDomainsList"]}, {"name": "fun getDomainsValidationError(domains: String): String?", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.getDomainsValidationError", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-domains-validation-error.html", "searchKeys": ["getDomainsValidationError", "fun getDomainsValidationError(domains: String): String?", "com.android.proxy_self.infrastructure.utils.ValidationUtils.getDomainsValidationError"]}, {"name": "fun getFormattedBytesTransferred(): String", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.getFormattedBytesTransferred", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/get-formatted-bytes-transferred.html", "searchKeys": ["getFormattedBytesTransferred", "fun getFormattedBytesTransferred(): String", "com.android.proxy_self.domain.entities.ProxyStatusInfo.getFormattedBytesTransferred"]}, {"name": "fun getIpValidationError(ip: String): String?", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.getIpValidationError", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-ip-validation-error.html", "searchKeys": ["getIpValidationError", "fun getIpValidationError(ip: String): String?", "com.android.proxy_self.infrastructure.utils.ValidationUtils.getIpValidationError"]}, {"name": "fun getNetworkInfo(): NetworkInfo", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.getNetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-network-info.html", "searchKeys": ["getNetworkInfo", "fun getNetworkInfo(): NetworkInfo", "com.android.proxy_self.infrastructure.managers.NetworkManager.getNetworkInfo"]}, {"name": "fun getNetworkInfo(): NetworkInfo", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.getNetworkInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/get-network-info.html", "searchKeys": ["getNetworkInfo", "fun getNetworkInfo(): NetworkInfo", "com.android.proxy_self.infrastructure.utils.NetworkUtils.getNetworkInfo"]}, {"name": "fun getNetworkStats(): NetworkStats", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.getNetworkStats", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-network-stats.html", "searchKeys": ["getNetworkStats", "fun getNetworkStats(): NetworkStats", "com.android.proxy_self.infrastructure.managers.NetworkManager.getNetworkStats"]}, {"name": "fun getNetworkType(): String", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.getNetworkType", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/get-network-type.html", "searchKeys": ["getNetworkType", "fun getNetworkType(): String", "com.android.proxy_self.infrastructure.utils.NetworkUtils.getNetworkType"]}, {"name": "fun getPasswordValidationError(password: String): String?", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.getPasswordValidationError", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-password-validation-error.html", "searchKeys": ["getPasswordValidationError", "fun getPasswordValidationError(password: String): String?", "com.android.proxy_self.infrastructure.utils.ValidationUtils.getPasswordValidationError"]}, {"name": "fun getPortValidationError(port: String): String?", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.getPortValidationError", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-port-validation-error.html", "searchKeys": ["getPortValidationError", "fun getPortValidationError(port: String): String?", "com.android.proxy_self.infrastructure.utils.ValidationUtils.getPortValidationError"]}, {"name": "fun getProxyConnectionInfo(config: ProxyConfig): ProxyConnectionInfo", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.getProxyConnectionInfo", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/get-proxy-connection-info.html", "searchKeys": ["getProxyConnectionInfo", "fun getProxyConnectionInfo(config: ProxyConfig): ProxyConnectionInfo", "com.android.proxy_self.infrastructure.managers.ProxyManager.getProxyConnectionInfo"]}, {"name": "fun getService(): ProxyService", "description": "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder.getService", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/get-service.html", "searchKeys": ["getService", "fun getService(): ProxyService", "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder.getService"]}, {"name": "fun getUsernameValidationError(username: String): String?", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.getUsernameValidationError", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-username-validation-error.html", "searchKeys": ["getUsernameValidationError", "fun getUsernameValidationError(username: String): String?", "com.android.proxy_self.infrastructure.utils.ValidationUtils.getUsernameValidationError"]}, {"name": "fun hasError(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.domain.entities.ProxyStatus.hasError", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/has-error.html", "searchKeys": ["<PERSON><PERSON><PERSON><PERSON>", "fun hasError(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.domain.entities.ProxyStatus.hasError"]}, {"name": "fun hasValidationErrors(): Bo<PERSON>an", "description": "com.android.proxy_self.presentation.state.ProxyUiState.hasValidationErrors", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/has-validation-errors.html", "searchKeys": ["hasValidationErrors", "fun hasValidationErrors(): Bo<PERSON>an", "com.android.proxy_self.presentation.state.ProxyUiState.hasValidationErrors"]}, {"name": "fun importConfiguration()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.importConfiguration", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/import-configuration.html", "searchKeys": ["importConfiguration", "fun importConfiguration()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.importConfiguration"]}, {"name": "fun isActive(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.domain.entities.ProxyStatus.isActive", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/is-active.html", "searchKeys": ["isActive", "fun isActive(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.domain.entities.ProxyStatus.isActive"]}, {"name": "fun isAnyOperationInProgress(): Bo<PERSON>an", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isAnyOperationInProgress", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-any-operation-in-progress.html", "searchKeys": ["isAnyOperationInProgress", "fun isAnyOperationInProgress(): Bo<PERSON>an", "com.android.proxy_self.presentation.state.ProxyUiState.isAnyOperationInProgress"]}, {"name": "fun isAnyOperationInProgress(): Bo<PERSON>an", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isAnyOperationInProgress", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-any-operation-in-progress.html", "searchKeys": ["isAnyOperationInProgress", "fun isAnyOperationInProgress(): Bo<PERSON>an", "com.android.proxy_self.presentation.state.SettingsUiState.isAnyOperationInProgress"]}, {"name": "fun isAutoStartEnabled(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isAutoStartEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-auto-start-enabled.html", "searchKeys": ["isAutoStartEnabled", "fun isAutoStartEnabled(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isAutoStartEnabled"]}, {"name": "fun isCellularConnected(): Boolean", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isCellularConnected", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-cellular-connected.html", "searchKeys": ["isCellularConnected", "fun isCellularConnected(): Boolean", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isCellularConnected"]}, {"name": "fun isDarkThemeEnabled(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isDarkThemeEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-dark-theme-enabled.html", "searchKeys": ["isDarkThemeEnabled", "fun isDarkThemeEnabled(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isDarkThemeEnabled"]}, {"name": "fun isDomainInWhitelist(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isDomainInWhitelist", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-domain-in-whitelist.html", "searchKeys": ["isDomain<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "fun isDomainInWhitelist(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isDomainInWhitelist"]}, {"name": "fun isEnabled(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.services.ProxyService.isEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/is-enabled.html", "searchKeys": ["isEnabled", "fun isEnabled(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.services.ProxyService.isEnabled"]}, {"name": "fun isEncryptionAvailable(): Bo<PERSON>an", "description": "com.android.proxy_self.infrastructure.utils.EncryptionUtils.isEncryptionAvailable", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/is-encryption-available.html", "searchKeys": ["isEncryptionAvailable", "fun isEncryptionAvailable(): Bo<PERSON>an", "com.android.proxy_self.infrastructure.utils.EncryptionUtils.isEncryptionAvailable"]}, {"name": "fun isFormValid(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isFormValid", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-form-valid.html", "searchKeys": ["isFormValid", "fun isFormValid(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.presentation.state.ProxyUiState.isFormValid"]}, {"name": "fun isMeteredConnection(): Boolean", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isMeteredConnection", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-metered-connection.html", "searchKeys": ["isMeteredConnection", "fun isMeteredConnection(): Boolean", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isMeteredConnection"]}, {"name": "fun isNetworkAvailable(): Bo<PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isNetworkAvailable", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-network-available.html", "searchKeys": ["isNetworkAvailable", "fun isNetworkAvailable(): Bo<PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isNetworkAvailable"]}, {"name": "fun isNotificationsEnabled(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isNotificationsEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-notifications-enabled.html", "searchKeys": ["isNotificationsEnabled", "fun isNotificationsEnabled(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.isNotificationsEnabled"]}, {"name": "fun isValid(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.domain.entities.ProxyConfig.isValid", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/is-valid.html", "searchKeys": ["<PERSON><PERSON><PERSON><PERSON>", "fun isValid(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.domain.entities.ProxyConfig.isValid"]}, {"name": "fun isValidDomain(domain: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidDomain", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-domain.html", "searchKeys": ["isValidDomain", "fun isValidDomain(domain: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidDomain"]}, {"name": "fun isValidDomainList(domains: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidDomainList", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-domain-list.html", "searchKeys": ["isValidDomainList", "fun isValidDomainList(domains: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidDomainList"]}, {"name": "fun isValidIpAddress(ip: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpAddress", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ip-address.html", "searchKeys": ["isValidIpAddress", "fun isValidIpAddress(ip: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpAddress"]}, {"name": "fun isValidIpv4Address(ip: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpv4Address", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ipv4-address.html", "searchKeys": ["isValidIpv4Address", "fun isValidIpv4Address(ip: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpv4Address"]}, {"name": "fun isValidIpv6Address(ip: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpv6Address", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ipv6-address.html", "searchKeys": ["isValidIpv6Address", "fun isValidIpv6Address(ip: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidIpv6Address"]}, {"name": "fun isValidPassword(password: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPassword", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-password.html", "searchKeys": ["isValidPassword", "fun isValidPassword(password: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPassword"]}, {"name": "fun isValidPort(port: Int): Boolean", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPort", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-port.html", "searchKeys": ["isValidPort", "fun isValidPort(port: Int): Boolean", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPort"]}, {"name": "fun isValidPort(port: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPort", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-port.html", "searchKeys": ["isValidPort", "fun isValidPort(port: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidPort"]}, {"name": "fun isValidUsername(username: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidUsername", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-username.html", "searchKeys": ["isValidUsername", "fun isValidUsername(username: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.isValidUsername"]}, {"name": "fun isVpnActive(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.isVpnActive", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/is-vpn-active.html", "searchKeys": ["isVpnActive", "fun isVpnActive(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkManager.isVpnActive"]}, {"name": "fun isWifiConnected(): Boolean", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isWifiConnected", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-wifi-connected.html", "searchKeys": ["isWifiConnected", "fun isWifiConnected(): Boolean", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isWifiConnected"]}, {"name": "fun mapToDomain(entity: ProxyEntity, decryptedPassword: String): ProxyConfig", "description": "com.android.proxy_self.data.mappers.ProxyMapper.mapToDomain", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-domain.html", "searchKeys": ["mapToDomain", "fun mapToDomain(entity: ProxyEntity, decryptedPassword: String): ProxyConfig", "com.android.proxy_self.data.mappers.ProxyMapper.mapToDomain"]}, {"name": "fun mapToDomainList(entities: List<ProxyEntity>, decryptPassword: (String) -> String): List<ProxyConfig>", "description": "com.android.proxy_self.data.mappers.ProxyMapper.mapToDomainList", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-domain-list.html", "searchKeys": ["mapToDomainList", "fun mapToDomainList(entities: List<ProxyEntity>, decryptPassword: (String) -> String): List<ProxyConfig>", "com.android.proxy_self.data.mappers.ProxyMapper.mapToDomainList"]}, {"name": "fun mapToEntity(config: ProxyConfig, encryptedPassword: String): ProxyEntity", "description": "com.android.proxy_self.data.mappers.ProxyMapper.mapToEntity", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-entity.html", "searchKeys": ["mapToEntity", "fun mapToEntity(config: ProxyConfig, encryptedPassword: String): ProxyEntity", "com.android.proxy_self.data.mappers.ProxyMapper.mapToEntity"]}, {"name": "fun observeNetworkConnectivity(): Flow<NetworkState>", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.observeNetworkConnectivity", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/observe-network-connectivity.html", "searchKeys": ["observeNetworkConnectivity", "fun observeNetworkConnectivity(): Flow<NetworkState>", "com.android.proxy_self.infrastructure.managers.NetworkManager.observeNetworkConnectivity"]}, {"name": "fun parseAndValidateDomains(domains: String): ValidationResult<List<String>>", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.parseAndValidateDomains", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/parse-and-validate-domains.html", "searchKeys": ["parseAndValidateDomains", "fun parseAndValidateDomains(domains: String): ValidationResult<List<String>>", "com.android.proxy_self.infrastructure.utils.ValidationUtils.parseAndValidateDomains"]}, {"name": "fun provideEncryptionUtils(context: Context): EncryptionUtils", "description": "com.android.proxy_self.di.NetworkModule.provideEncryptionUtils", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-encryption-utils.html", "searchKeys": ["provideEncryptionUtils", "fun provideEncryptionUtils(context: Context): EncryptionUtils", "com.android.proxy_self.di.NetworkModule.provideEncryptionUtils"]}, {"name": "fun provideNetworkManager(context: Context, networkUtils: NetworkUtils): NetworkManager", "description": "com.android.proxy_self.di.ManagerModule.provideNetworkManager", "location": "-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-network-manager.html", "searchKeys": ["provideNetworkManager", "fun provideNetworkManager(context: Context, networkUtils: NetworkUtils): NetworkManager", "com.android.proxy_self.di.ManagerModule.provideNetworkManager"]}, {"name": "fun provideNetworkUtils(context: Context): NetworkUtils", "description": "com.android.proxy_self.di.NetworkModule.provideNetworkUtils", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-network-utils.html", "searchKeys": ["provideNetworkUtils", "fun provideNetworkUtils(context: Context): NetworkUtils", "com.android.proxy_self.di.NetworkModule.provideNetworkUtils"]}, {"name": "fun provideOkHttpClient(): OkHttpClient", "description": "com.android.proxy_self.di.NetworkModule.provideOkHttpClient", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-ok-http-client.html", "searchKeys": ["provideOkHttpClient", "fun provideOkHttpClient(): OkHttpClient", "com.android.proxy_self.di.NetworkModule.provideOkHttpClient"]}, {"name": "fun provideProxyDao(database: ProxyDatabase): ProxyDao", "description": "com.android.proxy_self.di.DatabaseModule.provideProxyDao", "location": "-proxy -self -android -app/com.android.proxy_self.di/-database-module/provide-proxy-dao.html", "searchKeys": ["provideProxyDao", "fun provideProxyDao(database: ProxyDatabase): ProxyDao", "com.android.proxy_self.di.DatabaseModule.provideProxyDao"]}, {"name": "fun provideProxyDatabase(context: Context): ProxyDatabase", "description": "com.android.proxy_self.di.DatabaseModule.provideProxyDatabase", "location": "-proxy -self -android -app/com.android.proxy_self.di/-database-module/provide-proxy-database.html", "searchKeys": ["provideProxyDatabase", "fun provideProxyDatabase(context: Context): ProxyDatabase", "com.android.proxy_self.di.DatabaseModule.provideProxyDatabase"]}, {"name": "fun provideProxyManager(networkUtils: NetworkUtils): ProxyManager", "description": "com.android.proxy_self.di.ManagerModule.provideProxyManager", "location": "-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-proxy-manager.html", "searchKeys": ["provideProxyManager", "fun provideProxyManager(networkUtils: NetworkUtils): ProxyManager", "com.android.proxy_self.di.ManagerModule.provideProxyManager"]}, {"name": "fun provideProxyNotificationManager(context: Context): ProxyNotificationManager", "description": "com.android.proxy_self.di.ManagerModule.provideProxyNotificationManager", "location": "-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-proxy-notification-manager.html", "searchKeys": ["provideProxyNotificationManager", "fun provideProxyNotificationManager(context: Context): ProxyNotificationManager", "com.android.proxy_self.di.ManagerModule.provideProxyNotificationManager"]}, {"name": "fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit", "description": "com.android.proxy_self.di.NetworkModule.provideRetrofit", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-retrofit.html", "searchKeys": ["provideRetrofit", "fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit", "com.android.proxy_self.di.NetworkModule.provideRetrofit"]}, {"name": "fun provideSharedPreferences(context: Context): SharedPreferences", "description": "com.android.proxy_self.di.NetworkModule.provideSharedPreferences", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-shared-preferences.html", "searchKeys": ["provideSharedPreferences", "fun provideSharedPreferences(context: Context): SharedPreferences", "com.android.proxy_self.di.NetworkModule.provideSharedPreferences"]}, {"name": "fun provideValidationUtils(): ValidationUtils", "description": "com.android.proxy_self.di.NetworkModule.provideValidationUtils", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-validation-utils.html", "searchKeys": ["provideValidationUtils", "fun provideValidationUtils(): ValidationUtils", "com.android.proxy_self.di.NetworkModule.provideValidationUtils"]}, {"name": "fun requestTileUpdate(context: Context)", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.Companion.requestTileUpdate", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-companion/request-tile-update.html", "searchKeys": ["requestTileUpdate", "fun requestTileUpdate(context: Context)", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.Companion.requestTileUpdate"]}, {"name": "fun saveConfiguration()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.saveConfiguration", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/save-configuration.html", "searchKeys": ["saveConfiguration", "fun saveConfiguration()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.saveConfiguration"]}, {"name": "fun shouldRouteThoughProxy(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.shouldRouteThoughProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/should-route-though-proxy.html", "searchKeys": ["shouldRouteThoughProxy", "fun shouldRouteThoughProxy(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkManager.shouldRouteThoughProxy"]}, {"name": "fun shouldUseProxy(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.shouldUseProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/should-use-proxy.html", "searchKeys": ["shouldUseProxy", "fun shouldUseProxy(domain: String, whitelist: List<String>): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.ProxyManager.shouldUseProxy"]}, {"name": "fun showStatusNotification(title: String, message: String, isOngoing: <PERSON><PERSON><PERSON> = false)", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.showStatusNotification", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/show-status-notification.html", "searchKeys": ["showStatusNotification", "fun showStatusNotification(title: String, message: String, isOngoing: <PERSON><PERSON><PERSON> = false)", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.showStatusNotification"]}, {"name": "fun startProxy()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.startProxy", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/start-proxy.html", "searchKeys": ["startProxy", "fun startProxy()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.startProxy"]}, {"name": "fun startProxy(config: ProxyConfig)", "description": "com.android.proxy_self.infrastructure.services.ProxyService.startProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/start-proxy.html", "searchKeys": ["startProxy", "fun startProxy(config: ProxyConfig)", "com.android.proxy_self.infrastructure.services.ProxyService.startProxy"]}, {"name": "fun stopProxy()", "description": "com.android.proxy_self.infrastructure.services.ProxyService.stopProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/stop-proxy.html", "searchKeys": ["stopProxy", "fun stopProxy()", "com.android.proxy_self.infrastructure.services.ProxyService.stopProxy"]}, {"name": "fun stopProxy()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.stopProxy", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/stop-proxy.html", "searchKeys": ["stopProxy", "fun stopProxy()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.stopProxy"]}, {"name": "fun testConnection()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.testConnection", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/test-connection.html", "searchKeys": ["testConnection", "fun testConnection()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.testConnection"]}, {"name": "fun toProxyConfig(): ProxyConfig", "description": "com.android.proxy_self.presentation.state.ProxyUiState.toProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/to-proxy-config.html", "searchKeys": ["toProxyConfig", "fun toProxyConfig(): ProxyConfig", "com.android.proxy_self.presentation.state.ProxyUiState.toProxyConfig"]}, {"name": "fun toggleAutoStart()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleAutoStart", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-auto-start.html", "searchKeys": ["toggleAutoStart", "fun toggleAutoStart()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleAutoStart"]}, {"name": "fun toggleDarkTheme()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleDarkTheme", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-dark-theme.html", "searchKeys": ["toggleDarkTheme", "fun toggleDarkTheme()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleDarkTheme"]}, {"name": "fun toggleNotifications()", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleNotifications", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-notifications.html", "searchKeys": ["toggleNotifications", "fun toggleNotifications()", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.toggleNotifications"]}, {"name": "fun togglePasswordVisibility()", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.togglePasswordVisibility", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/toggle-password-visibility.html", "searchKeys": ["togglePasswordVisibility", "fun togglePasswordVisibility()", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.togglePasswordVisibility"]}, {"name": "fun updateConfig(config: ProxyConfig)", "description": "com.android.proxy_self.infrastructure.services.ProxyService.updateConfig", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/update-config.html", "searchKeys": ["updateConfig", "fun updateConfig(config: ProxyConfig)", "com.android.proxy_self.infrastructure.services.ProxyService.updateConfig"]}, {"name": "fun updateDomains(domains: String)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateDomains", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-domains.html", "searchKeys": ["updateDomains", "fun updateDomains(domains: String)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateDomains"]}, {"name": "fun updateNotification(status: ProxyStatus, serverHost: String = \"\", serverPort: Int = 0)", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.updateNotification", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/update-notification.html", "searchKeys": ["updateNotification", "fun updateNotification(status: ProxyStatus, serverHost: String = \"\", serverPort: Int = 0)", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.updateNotification"]}, {"name": "fun updatePassword(password: String)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updatePassword", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-password.html", "searchKeys": ["updatePassword", "fun updatePassword(password: String)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updatePassword"]}, {"name": "fun updateProxyType(proxyType: ProxyType)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateProxyType", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-proxy-type.html", "searchKeys": ["updateProxyType", "fun updateProxyType(proxyType: ProxyType)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateProxyType"]}, {"name": "fun updateServerAddress(address: String)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateServerAddress", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-server-address.html", "searchKeys": ["updateServerAddress", "fun updateServerAddress(address: String)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateServerAddress"]}, {"name": "fun updateServerPort(port: String)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateServerPort", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-server-port.html", "searchKeys": ["updateServerPort", "fun updateServerPort(port: String)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateServerPort"]}, {"name": "fun updateTileFromService(enabled: Boolean, status: ProxyStatus)", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.updateTileFromService", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/update-tile-from-service.html", "searchKeys": ["updateTileFromService", "fun updateTileFromService(enabled: Boolean, status: ProxyStatus)", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.updateTileFromService"]}, {"name": "fun updateTimestamp(entity: ProxyEntity): ProxyEntity", "description": "com.android.proxy_self.data.mappers.ProxyMapper.updateTimestamp", "location": "-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/update-timestamp.html", "searchKeys": ["updateTimestamp", "fun updateTimestamp(entity: ProxyEntity): ProxyEntity", "com.android.proxy_self.data.mappers.ProxyMapper.updateTimestamp"]}, {"name": "fun updateUsername(username: String)", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateUsername", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-username.html", "searchKeys": ["updateUsername", "fun updateUsername(username: String)", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.updateUsername"]}, {"name": "fun validateProxyConfig(config: ProxyConfig): ProxyValidationResult", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.validateProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/validate-proxy-config.html", "searchKeys": ["validateProxyConfig", "fun validateProxyConfig(config: ProxyConfig): ProxyValidationResult", "com.android.proxy_self.infrastructure.managers.ProxyManager.validateProxyConfig"]}, {"name": "fun valueOf(value: String): ButtonVariant", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.valueOf", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/value-of.html", "searchKeys": ["valueOf", "fun valueOf(value: String): ButtonVariant", "com.android.proxy_self.presentation.ui.components.ButtonVariant.valueOf"]}, {"name": "fun valueOf(value: String): NetworkState", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.valueOf", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/value-of.html", "searchKeys": ["valueOf", "fun valueOf(value: String): NetworkState", "com.android.proxy_self.infrastructure.managers.NetworkState.valueOf"]}, {"name": "fun valueOf(value: String): ProxyStatus", "description": "com.android.proxy_self.domain.entities.ProxyStatus.valueOf", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/value-of.html", "searchKeys": ["valueOf", "fun valueOf(value: String): ProxyStatus", "com.android.proxy_self.domain.entities.ProxyStatus.valueOf"]}, {"name": "fun valueOf(value: String): ProxyType", "description": "com.android.proxy_self.domain.entities.ProxyType.valueOf", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/value-of.html", "searchKeys": ["valueOf", "fun valueOf(value: String): ProxyType", "com.android.proxy_self.domain.entities.ProxyType.valueOf"]}, {"name": "fun values(): A<PERSON>y<ButtonVariant>", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.values", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/values.html", "searchKeys": ["values", "fun values(): A<PERSON>y<ButtonVariant>", "com.android.proxy_self.presentation.ui.components.ButtonVariant.values"]}, {"name": "fun values(): Array<NetworkState>", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.values", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/values.html", "searchKeys": ["values", "fun values(): Array<NetworkState>", "com.android.proxy_self.infrastructure.managers.NetworkState.values"]}, {"name": "fun values(): Array<ProxyStatus>", "description": "com.android.proxy_self.domain.entities.ProxyStatus.values", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/values.html", "searchKeys": ["values", "fun values(): Array<ProxyStatus>", "com.android.proxy_self.domain.entities.ProxyStatus.values"]}, {"name": "fun values(): Array<ProxyType>", "description": "com.android.proxy_self.domain.entities.ProxyType.values", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/values.html", "searchKeys": ["values", "fun values(): Array<ProxyType>", "com.android.proxy_self.domain.entities.ProxyType.values"]}, {"name": "inner class ProxyServiceBinder : Binder", "description": "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/index.html", "searchKeys": ["ProxyServiceBinder", "inner class ProxyServiceBinder : Binder", "com.android.proxy_self.infrastructure.services.ProxyService.ProxyServiceBinder"]}, {"name": "interface ProxyDao", "description": "com.android.proxy_self.data.datasources.local.ProxyDao", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/index.html", "searchKeys": ["ProxyDao", "interface ProxyDao", "com.android.proxy_self.data.datasources.local.ProxyDao"]}, {"name": "interface ProxyRepository", "description": "com.android.proxy_self.domain.repositories.ProxyRepository", "location": "-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/index.html", "searchKeys": ["ProxyRepository", "interface ProxyRepository", "com.android.proxy_self.domain.repositories.ProxyRepository"]}, {"name": "lateinit var getProxyConfigUseCase: GetProxyConfigUseCase", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.getProxyConfigUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/get-proxy-config-use-case.html", "searchKeys": ["getProxyConfigUseCase", "lateinit var getProxyConfigUseCase: GetProxyConfigUseCase", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.getProxyConfigUseCase"]}, {"name": "lateinit var networkManager: NetworkManager", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.networkManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/network-manager.html", "searchKeys": ["networkManager", "lateinit var networkManager: NetworkManager", "com.android.proxy_self.infrastructure.services.ProxyVpnService.networkManager"]}, {"name": "lateinit var notificationManager: ProxyNotificationManager", "description": "com.android.proxy_self.infrastructure.services.ProxyService.notificationManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/notification-manager.html", "searchKeys": ["notificationManager", "lateinit var notificationManager: ProxyNotificationManager", "com.android.proxy_self.infrastructure.services.ProxyService.notificationManager"]}, {"name": "lateinit var proxyManager: ProxyManager", "description": "com.android.proxy_self.infrastructure.services.ProxyService.proxyManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/proxy-manager.html", "searchKeys": ["proxyManager", "lateinit var proxyManager: ProxyManager", "com.android.proxy_self.infrastructure.services.ProxyService.proxyManager"]}, {"name": "lateinit var proxyManager: ProxyManager", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.proxyManager", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/proxy-manager.html", "searchKeys": ["proxyManager", "lateinit var proxyManager: ProxyManager", "com.android.proxy_self.infrastructure.services.ProxyVpnService.proxyManager"]}, {"name": "lateinit var sharedPreferences: SharedPreferences", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver.sharedPreferences", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/shared-preferences.html", "searchKeys": ["sharedPreferences", "lateinit var sharedPreferences: SharedPreferences", "com.android.proxy_self.infrastructure.receivers.BootReceiver.sharedPreferences"]}, {"name": "lateinit var startProxyUseCase: StartProxyUseCase", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.startProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/start-proxy-use-case.html", "searchKeys": ["startProxyUseCase", "lateinit var startProxyUseCase: StartProxyUseCase", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.startProxyUseCase"]}, {"name": "lateinit var stopProxyUseCase: StopProxyUseCase", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.stopProxyUseCase", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/stop-proxy-use-case.html", "searchKeys": ["stopProxyUseCase", "lateinit var stopProxyUseCase: StopProxyUseCase", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.stopProxyUseCase"]}, {"name": "lateinit var workerFactory: HiltWorkerFactory", "description": "com.android.proxy_self.ProxyApplication.workerFactory", "location": "-proxy -self -android -app/com.android.proxy_self/-proxy-application/worker-factory.html", "searchKeys": ["workerFactory", "lateinit var workerFactory: HiltWorkerFactory", "com.android.proxy_self.ProxyApplication.workerFactory"]}, {"name": "object About : Screen", "description": "com.android.proxy_self.presentation.ui.navigation.Screen.About", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-about/index.html", "searchKeys": ["About", "object About : Screen", "com.android.proxy_self.presentation.ui.navigation.Screen.About"]}, {"name": "object Companion", "description": "com.android.proxy_self.data.datasources.local.ProxyDatabase.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.data.datasources.local.ProxyDatabase.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.domain.entities.ProxyConfig.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.domain.entities.ProxyConfig.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.domain.entities.ProxyType.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.domain.entities.ProxyType.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.infrastructure.managers.ProxyNotificationManager.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.infrastructure.receivers.BootReceiver.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.infrastructure.services.ProxyService.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.infrastructure.services.ProxyService.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.infrastructure.services.ProxyVpnService.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.infrastructure.utils.ValidationUtils.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.infrastructure.utils.ValidationUtils.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.Companion"]}, {"name": "object Companion", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.Companion", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/-companion/index.html", "searchKeys": ["Companion", "object Companion", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.Companion"]}, {"name": "object DatabaseModule", "description": "com.android.proxy_self.di.DatabaseModule", "location": "-proxy -self -android -app/com.android.proxy_self.di/-database-module/index.html", "searchKeys": ["DatabaseModule", "object DatabaseModule", "com.android.proxy_self.di.DatabaseModule"]}, {"name": "object ManagerModule", "description": "com.android.proxy_self.di.ManagerModule", "location": "-proxy -self -android -app/com.android.proxy_self.di/-manager-module/index.html", "searchKeys": ["ManagerModule", "object ManagerModule", "com.android.proxy_self.di.ManagerModule"]}, {"name": "object NetworkModule", "description": "com.android.proxy_self.di.NetworkModule", "location": "-proxy -self -android -app/com.android.proxy_self.di/-network-module/index.html", "searchKeys": ["NetworkModule", "object NetworkModule", "com.android.proxy_self.di.NetworkModule"]}, {"name": "object ProxyConfig : Screen", "description": "com.android.proxy_self.presentation.ui.navigation.Screen.ProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-proxy-config/index.html", "searchKeys": ["ProxyConfig", "object ProxyConfig : Screen", "com.android.proxy_self.presentation.ui.navigation.Screen.ProxyConfig"]}, {"name": "object Settings : Screen", "description": "com.android.proxy_self.presentation.ui.navigation.Screen.Settings", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-settings/index.html", "searchKeys": ["Settings", "object Settings : Screen", "com.android.proxy_self.presentation.ui.navigation.Screen.Settings"]}, {"name": "object Success : ConnectionTestResult", "description": "com.android.proxy_self.presentation.state.ConnectionTestResult.Success", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-success/index.html", "searchKeys": ["Success", "object Success : ConnectionTestResult", "com.android.proxy_self.presentation.state.ConnectionTestResult.Success"]}, {"name": "object Valid : ProxyValidationResult", "description": "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Valid", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-valid/index.html", "searchKeys": ["<PERSON><PERSON>", "object Valid : ProxyValidationResult", "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Valid"]}, {"name": "open operator override fun equals(other: Any?): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.services.IpPacket.equals", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/equals.html", "searchKeys": ["equals", "open operator override fun equals(other: Any?): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.services.IpPacket.equals"]}, {"name": "open override fun getProxyStatus(): Flow<ProxyStatusInfo>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getProxyStatus", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-proxy-status.html", "searchKeys": ["getProxyStatus", "open override fun getProxyStatus(): Flow<ProxyStatusInfo>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getProxyStatus"]}, {"name": "open override fun hashCode(): Int", "description": "com.android.proxy_self.infrastructure.services.IpPacket.hashCode", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/hash-code.html", "searchKeys": ["hashCode", "open override fun hashCode(): Int", "com.android.proxy_self.infrastructure.services.IpPacket.hashCode"]}, {"name": "open override fun isProxyEnabled(): Flow<Boolean>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.isProxyEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/is-proxy-enabled.html", "searchKeys": ["isProxyEnabled", "open override fun isProxyEnabled(): Flow<Boolean>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.isProxyEnabled"]}, {"name": "open override fun onBind(intent: Intent?): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.services.ProxyService.onBind", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-bind.html", "searchKeys": ["onBind", "open override fun onBind(intent: Intent?): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.services.ProxyService.onBind"]}, {"name": "open override fun onClick()", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.onClick", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/on-click.html", "searchKeys": ["onClick", "open override fun onClick()", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.onClick"]}, {"name": "open override fun onCreate()", "description": "com.android.proxy_self.ProxyApplication.onCreate", "location": "-proxy -self -android -app/com.android.proxy_self/-proxy-application/on-create.html", "searchKeys": ["onCreate", "open override fun onCreate()", "com.android.proxy_self.ProxyApplication.onCreate"]}, {"name": "open override fun onCreate()", "description": "com.android.proxy_self.infrastructure.services.ProxyService.onCreate", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-create.html", "searchKeys": ["onCreate", "open override fun onCreate()", "com.android.proxy_self.infrastructure.services.ProxyService.onCreate"]}, {"name": "open override fun onCreate()", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.onCreate", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-create.html", "searchKeys": ["onCreate", "open override fun onCreate()", "com.android.proxy_self.infrastructure.services.ProxyVpnService.onCreate"]}, {"name": "open override fun onDestroy()", "description": "com.android.proxy_self.infrastructure.services.ProxyService.onDestroy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-destroy.html", "searchKeys": ["onDestroy", "open override fun onDestroy()", "com.android.proxy_self.infrastructure.services.ProxyService.onDestroy"]}, {"name": "open override fun onDestroy()", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.onDestroy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-destroy.html", "searchKeys": ["onDestroy", "open override fun onDestroy()", "com.android.proxy_self.infrastructure.services.ProxyVpnService.onDestroy"]}, {"name": "open override fun onReceive(context: Context, intent: Intent)", "description": "com.android.proxy_self.infrastructure.receivers.BootReceiver.onReceive", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/on-receive.html", "searchKeys": ["onReceive", "open override fun onReceive(context: Context, intent: Intent)", "com.android.proxy_self.infrastructure.receivers.BootReceiver.onReceive"]}, {"name": "open override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int", "description": "com.android.proxy_self.infrastructure.services.ProxyService.onStartCommand", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-start-command.html", "searchKeys": ["onStartCommand", "open override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int", "com.android.proxy_self.infrastructure.services.ProxyService.onStartCommand"]}, {"name": "open override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int", "description": "com.android.proxy_self.infrastructure.services.ProxyVpnService.onStartCommand", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-start-command.html", "searchKeys": ["onStartCommand", "open override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int", "com.android.proxy_self.infrastructure.services.ProxyVpnService.onStartCommand"]}, {"name": "open override fun onStartListening()", "description": "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.onStartListening", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/on-start-listening.html", "searchKeys": ["onStartListening", "open override fun onStartListening()", "com.android.proxy_self.presentation.quicksettings.ProxyQuickSettingsTile.onStartListening"]}, {"name": "open override val workManagerConfiguration: Configuration", "description": "com.android.proxy_self.ProxyApplication.workManagerConfiguration", "location": "-proxy -self -android -app/com.android.proxy_self/-proxy-application/work-manager-configuration.html", "searchKeys": ["workManagerConfiguration", "open override val workManagerConfiguration: Configuration", "com.android.proxy_self.ProxyApplication.workManagerConfiguration"]}, {"name": "open suspend fun enableConfig(id: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyDao.enableConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/enable-config.html", "searchKeys": ["enableConfig", "open suspend fun enableConfig(id: Long)", "com.android.proxy_self.data.datasources.local.ProxyDao.enableConfig"]}, {"name": "open suspend override fun deleteConfig(configId: Long): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.deleteConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/delete-config.html", "searchKeys": ["deleteConfig", "open suspend override fun deleteConfig(configId: Long): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.deleteConfig"]}, {"name": "open suspend override fun doWork(): ListenableWorker.Result", "description": "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker.doWork", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/do-work.html", "searchKeys": ["doWork", "open suspend override fun doWork(): ListenableWorker.Result", "com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker.doWork"]}, {"name": "open suspend override fun execute(): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.usecases.StopProxyUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/execute.html", "searchKeys": ["execute", "open suspend override fun execute(): Flow<Result<Boolean>>", "com.android.proxy_self.domain.usecases.StopProxyUseCase.execute"]}, {"name": "open suspend override fun execute(): Flow<Result<ProxyConfig>>", "description": "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/execute.html", "searchKeys": ["execute", "open suspend override fun execute(): Flow<Result<ProxyConfig>>", "com.android.proxy_self.domain.usecases.GetProxyConfigUseCase.execute"]}, {"name": "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/execute.html", "searchKeys": ["execute", "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase.execute"]}, {"name": "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.usecases.StartProxyUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/execute.html", "searchKeys": ["execute", "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.usecases.StartProxyUseCase.execute"]}, {"name": "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase.execute", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/execute.html", "searchKeys": ["execute", "open suspend override fun execute(params: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase.execute"]}, {"name": "open suspend override fun getAllConfigs(): Flow<Result<List<ProxyConfig>>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-all-configs.html", "searchKeys": ["getAllConfigs", "open suspend override fun getAllConfigs(): Flow<Result<List<ProxyConfig>>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getAllConfigs"]}, {"name": "open suspend override fun getProxyConfig(): Flow<Result<ProxyConfig>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-proxy-config.html", "searchKeys": ["getProxyConfig", "open suspend override fun getProxyConfig(): Flow<Result<ProxyConfig>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.getProxyConfig"]}, {"name": "open suspend override fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.saveProxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/save-proxy-config.html", "searchKeys": ["saveProxyConfig", "open suspend override fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.saveProxyConfig"]}, {"name": "open suspend override fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.startProxy", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/start-proxy.html", "searchKeys": ["startProxy", "open suspend override fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.startProxy"]}, {"name": "open suspend override fun stopProxy(): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.stopProxy", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/stop-proxy.html", "searchKeys": ["stopProxy", "open suspend override fun stopProxy(): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.stopProxy"]}, {"name": "open suspend override fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.testConnection", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/test-connection.html", "searchKeys": ["testConnection", "open suspend override fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.testConnection"]}, {"name": "open suspend override fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>>", "description": "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.updateConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/update-config.html", "searchKeys": ["updateConfig", "open suspend override fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>>", "com.android.proxy_self.data.repositories.ProxyRepositoryImpl.updateConfig"]}, {"name": "sealed class ConnectionTestResult", "description": "com.android.proxy_self.presentation.state.ConnectionTestResult", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/index.html", "searchKeys": ["ConnectionTestResult", "sealed class ConnectionTestResult", "com.android.proxy_self.presentation.state.ConnectionTestResult"]}, {"name": "sealed class ProxyValidationResult", "description": "com.android.proxy_self.infrastructure.managers.ProxyValidationResult", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/index.html", "searchKeys": ["ProxyValidationResult", "sealed class ProxyValidationResult", "com.android.proxy_self.infrastructure.managers.ProxyValidationResult"]}, {"name": "sealed class Screen", "description": "com.android.proxy_self.presentation.ui.navigation.Screen", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/index.html", "searchKeys": ["Screen", "sealed class Screen", "com.android.proxy_self.presentation.ui.navigation.Screen"]}, {"name": "sealed class SocksProxyResult", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/index.html", "searchKeys": ["SocksProxyResult", "sealed class SocksProxyResult", "com.android.proxy_self.infrastructure.managers.SocksProxyResult"]}, {"name": "sealed class ValidationResult<out T>", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/index.html", "searchKeys": ["ValidationResult", "sealed class ValidationResult<out T>", "com.android.proxy_self.infrastructure.utils.ValidationResult"]}, {"name": "suspend fun createSocksProxy(config: ProxyConfig): SocksProxyResult", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.createSocksProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/create-socks-proxy.html", "searchKeys": ["createSocksProxy", "suspend fun createSocksProxy(config: ProxyConfig): SocksProxyResult", "com.android.proxy_self.infrastructure.managers.ProxyManager.createSocksProxy"]}, {"name": "suspend fun deleteAllConfigs()", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-all-configs.html", "searchKeys": ["deleteAllConfigs", "suspend fun deleteAllConfigs()", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteAllConfigs"]}, {"name": "suspend fun deleteConfig(config: ProxyEntity)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-config.html", "searchKeys": ["deleteConfig", "suspend fun deleteConfig(config: ProxyEntity)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteConfig"]}, {"name": "suspend fun deleteConfigById(id: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteConfigById", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-config-by-id.html", "searchKeys": ["deleteConfigById", "suspend fun deleteConfigById(id: Long)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.deleteConfigById"]}, {"name": "suspend fun disableAllConfigs()", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.disableAllConfigs", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/disable-all-configs.html", "searchKeys": ["disableAllConfigs", "suspend fun disableAllConfigs()", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.disableAllConfigs"]}, {"name": "suspend fun enableConfig(id: Long)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.enableConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/enable-config.html", "searchKeys": ["enableConfig", "suspend fun enableConfig(id: Long)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.enableConfig"]}, {"name": "suspend fun getConfigById(id: Long): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getConfigById", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-config-by-id.html", "searchKeys": ["getConfigById", "suspend fun getConfigById(id: Long): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getConfigById"]}, {"name": "suspend fun getConfigCount(): Int", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getConfigCount", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-config-count.html", "searchKeys": ["getConfigCount", "suspend fun getConfigCount(): Int", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getConfigCount"]}, {"name": "suspend fun getConfigFromCloud(): String?", "description": "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.getConfigFromCloud", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/get-config-from-cloud.html", "searchKeys": ["getConfigFromCloud", "suspend fun getConfigFromCloud(): String?", "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.getConfigFromCloud"]}, {"name": "suspend fun getEnabledConfig(): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getEnabledConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-enabled-config.html", "searchKeys": ["getEnabledConfig", "suspend fun getEnabledConfig(): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getEnabledConfig"]}, {"name": "suspend fun getLatestConfig(): ProxyEntity?", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getLatestConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-latest-config.html", "searchKeys": ["getLatestConfig", "suspend fun getLatestConfig(): ProxyEntity?", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.getLatestConfig"]}, {"name": "suspend fun isHostReachable(host: String, port: Int, timeoutMs: Int = 5000): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkManager.isHostReachable", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/is-host-reachable.html", "searchKeys": ["isHostReachable", "suspend fun isHostReachable(host: String, port: Int, timeoutMs: Int = 5000): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkManager.isHostReachable"]}, {"name": "suspend fun isHostReachable(host: String, port: Int, timeoutMs: Int = 5000): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkUtils.isHostReachable", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-host-reachable.html", "searchKeys": ["isHostReachable", "suspend fun isHostReachable(host: String, port: Int, timeoutMs: Int = 5000): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkUtils.isHostReachable"]}, {"name": "suspend fun saveConfig(config: ProxyEntity): Long", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.saveConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/save-config.html", "searchKeys": ["saveConfig", "suspend fun saveConfig(config: ProxyEntity): Long", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.saveConfig"]}, {"name": "suspend fun stopSocksProxy(): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.stopSocksProxy", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/stop-socks-proxy.html", "searchKeys": ["stopSocksProxy", "suspend fun stopSocksProxy(): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.ProxyManager.stopSocksProxy"]}, {"name": "suspend fun syncConfigToCloud(configData: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.syncConfigToCloud", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/sync-config-to-cloud.html", "searchKeys": ["syncConfigToCloud", "suspend fun syncConfigToCloud(configData: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.syncConfigToCloud"]}, {"name": "suspend fun testProxyConnection(config: ProxyConfig): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.ProxyManager.testProxyConnection", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/test-proxy-connection.html", "searchKeys": ["testProxyConnection", "suspend fun testProxyConnection(config: ProxyConfig): <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.ProxyManager.testProxyConnection"]}, {"name": "suspend fun testProxyConnection(serverAddress: String, serverPort: Int, username: String, password: String, proxyType: String): <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.testProxyConnection", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/test-proxy-connection.html", "searchKeys": ["testProxyConnection", "suspend fun testProxyConnection(serverAddress: String, serverPort: Int, username: String, password: String, proxyType: String): <PERSON><PERSON><PERSON>", "com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource.testProxyConnection"]}, {"name": "suspend fun updateConfig(config: ProxyEntity)", "description": "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.updateConfig", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/update-config.html", "searchKeys": ["updateConfig", "suspend fun updateConfig(config: ProxyEntity)", "com.android.proxy_self.data.datasources.local.ProxyLocalDataSource.updateConfig"]}, {"name": "suspend operator fun invoke(): Flow<Result<T>>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.invoke", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/invoke.html", "searchKeys": ["invoke", "suspend operator fun invoke(): Flow<Result<T>>", "com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams.invoke"]}, {"name": "suspend operator fun invoke(params: P): Flow<Result<T>>", "description": "com.android.proxy_self.domain.usecases.base.BaseUseCase.invoke", "location": "-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/invoke.html", "searchKeys": ["invoke", "suspend operator fun invoke(params: P): Flow<Result<T>>", "com.android.proxy_self.domain.usecases.base.BaseUseCase.invoke"]}, {"name": "val OutlineDark: Color", "description": "com.android.proxy_self.presentation.ui.theme.OutlineDark", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-outline-dark.html", "searchKeys": ["OutlineDark", "val OutlineDark: Color", "com.android.proxy_self.presentation.ui.theme.OutlineDark"]}, {"name": "val OutlineLight: Color", "description": "com.android.proxy_self.presentation.ui.theme.OutlineLight", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-outline-light.html", "searchKeys": ["OutlineLight", "val OutlineLight: Color", "com.android.proxy_self.presentation.ui.theme.OutlineLight"]}, {"name": "val Pink40: Color", "description": "com.android.proxy_self.presentation.ui.theme.Pink40", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-pink40.html", "searchKeys": ["Pink40", "val Pink40: Color", "com.android.proxy_self.presentation.ui.theme.Pink40"]}, {"name": "val Pink80: Color", "description": "com.android.proxy_self.presentation.ui.theme.Pink80", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-pink80.html", "searchKeys": ["Pink80", "val Pink80: Color", "com.android.proxy_self.presentation.ui.theme.Pink80"]}, {"name": "val ProxyBlue: Color", "description": "com.android.proxy_self.presentation.ui.theme.ProxyBlue", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-blue.html", "searchKeys": ["ProxyBlue", "val ProxyBlue: Color", "com.android.proxy_self.presentation.ui.theme.ProxyBlue"]}, {"name": "val ProxyGreen: Color", "description": "com.android.proxy_self.presentation.ui.theme.ProxyGreen", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-green.html", "searchKeys": ["ProxyGreen", "val ProxyGreen: Color", "com.android.proxy_self.presentation.ui.theme.ProxyGreen"]}, {"name": "val ProxyOrange: Color", "description": "com.android.proxy_self.presentation.ui.theme.ProxyOrange", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-orange.html", "searchKeys": ["ProxyOrange", "val ProxyOrange: Color", "com.android.proxy_self.presentation.ui.theme.ProxyOrange"]}, {"name": "val ProxyRed: Color", "description": "com.android.proxy_self.presentation.ui.theme.ProxyRed", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-red.html", "searchKeys": ["ProxyRed", "val ProxyRed: Color", "com.android.proxy_self.presentation.ui.theme.ProxyRed"]}, {"name": "val Purple40: Color", "description": "com.android.proxy_self.presentation.ui.theme.Purple40", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple40.html", "searchKeys": ["Purple40", "val Purple40: Color", "com.android.proxy_self.presentation.ui.theme.Purple40"]}, {"name": "val Purple80: Color", "description": "com.android.proxy_self.presentation.ui.theme.Purple80", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple80.html", "searchKeys": ["Purple80", "val Purple80: Color", "com.android.proxy_self.presentation.ui.theme.Purple80"]}, {"name": "val PurpleGrey40: Color", "description": "com.android.proxy_self.presentation.ui.theme.PurpleGrey40", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple-grey40.html", "searchKeys": ["PurpleGrey40", "val PurpleGrey40: Color", "com.android.proxy_self.presentation.ui.theme.PurpleGrey40"]}, {"name": "val PurpleGrey80: Color", "description": "com.android.proxy_self.presentation.ui.theme.PurpleGrey80", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple-grey80.html", "searchKeys": ["PurpleGrey80", "val PurpleGrey80: Color", "com.android.proxy_self.presentation.ui.theme.PurpleGrey80"]}, {"name": "val StatusConnected: Color", "description": "com.android.proxy_self.presentation.ui.theme.StatusConnected", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-connected.html", "searchKeys": ["StatusConnected", "val StatusConnected: Color", "com.android.proxy_self.presentation.ui.theme.StatusConnected"]}, {"name": "val StatusConnecting: Color", "description": "com.android.proxy_self.presentation.ui.theme.StatusConnecting", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-connecting.html", "searchKeys": ["StatusConnecting", "val StatusConnecting: Color", "com.android.proxy_self.presentation.ui.theme.StatusConnecting"]}, {"name": "val StatusDisconnected: Color", "description": "com.android.proxy_self.presentation.ui.theme.StatusDisconnected", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-disconnected.html", "searchKeys": ["StatusDisconnected", "val StatusDisconnected: Color", "com.android.proxy_self.presentation.ui.theme.StatusDisconnected"]}, {"name": "val StatusError: Color", "description": "com.android.proxy_self.presentation.ui.theme.StatusError", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-error.html", "searchKeys": ["StatusError", "val StatusError: Color", "com.android.proxy_self.presentation.ui.theme.StatusError"]}, {"name": "val SurfaceVariantDark: Color", "description": "com.android.proxy_self.presentation.ui.theme.SurfaceVariantDark", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-surface-variant-dark.html", "searchKeys": ["SurfaceVariantDark", "val SurfaceVariantDark: Color", "com.android.proxy_self.presentation.ui.theme.SurfaceVariantDark"]}, {"name": "val SurfaceVariantLight: Color", "description": "com.android.proxy_self.presentation.ui.theme.SurfaceVariantLight", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-surface-variant-light.html", "searchKeys": ["SurfaceVariantLight", "val SurfaceVariantLight: Color", "com.android.proxy_self.presentation.ui.theme.SurfaceVariantLight"]}, {"name": "val Typography: Typography", "description": "com.android.proxy_self.presentation.ui.theme.Typography", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-typography.html", "searchKeys": ["Typography", "val Typography: Typography", "com.android.proxy_self.presentation.ui.theme.Typography"]}, {"name": "val activeConnections: Int = 0", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.activeConnections", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/active-connections.html", "searchKeys": ["activeConnections", "val activeConnections: Int = 0", "com.android.proxy_self.domain.entities.ProxyStatusInfo.activeConnections"]}, {"name": "val activeDomains: List<String>", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.activeDomains", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/active-domains.html", "searchKeys": ["activeDomains", "val activeDomains: List<String>", "com.android.proxy_self.domain.entities.ProxyStatusInfo.activeDomains"]}, {"name": "val appName: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.appName", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/app-name.html", "searchKeys": ["appName", "val appName: String", "com.android.proxy_self.presentation.state.AboutUiState.appName"]}, {"name": "val appVersion: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.appVersion", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/app-version.html", "searchKeys": ["appVersion", "val appVersion: String", "com.android.proxy_self.presentation.state.AboutUiState.appVersion"]}, {"name": "val appVersion: String", "description": "com.android.proxy_self.presentation.state.SettingsUiState.appVersion", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/app-version.html", "searchKeys": ["appVersion", "val appVersion: String", "com.android.proxy_self.presentation.state.SettingsUiState.appVersion"]}, {"name": "val buildDate: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.buildDate", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/build-date.html", "searchKeys": ["buildDate", "val buildDate: String", "com.android.proxy_self.presentation.state.AboutUiState.buildDate"]}, {"name": "val buildNumber: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.buildNumber", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/build-number.html", "searchKeys": ["buildNumber", "val buildNumber: String", "com.android.proxy_self.presentation.state.AboutUiState.buildNumber"]}, {"name": "val buildNumber: String", "description": "com.android.proxy_self.presentation.state.SettingsUiState.buildNumber", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/build-number.html", "searchKeys": ["buildNumber", "val buildNumber: String", "com.android.proxy_self.presentation.state.SettingsUiState.buildNumber"]}, {"name": "val bytesReceived: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.bytesReceived", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/bytes-received.html", "searchKeys": ["bytesReceived", "val bytesReceived: Long", "com.android.proxy_self.infrastructure.managers.NetworkStats.bytesReceived"]}, {"name": "val bytesSent: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.bytesSent", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/bytes-sent.html", "searchKeys": ["bytesSent", "val bytesSent: Long", "com.android.proxy_self.infrastructure.managers.NetworkStats.bytesSent"]}, {"name": "val bytesTransferred: Long = 0", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.bytesTransferred", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/bytes-transferred.html", "searchKeys": ["bytesTransferred", "val bytesTransferred: Long = 0", "com.android.proxy_self.domain.entities.ProxyStatusInfo.bytesTransferred"]}, {"name": "val connectionTestResult: ConnectionTestResult? = null", "description": "com.android.proxy_self.presentation.state.ProxyUiState.connectionTestResult", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/connection-test-result.html", "searchKeys": ["connectionTestResult", "val connectionTestResult: ConnectionTestResult? = null", "com.android.proxy_self.presentation.state.ProxyUiState.connectionTestResult"]}, {"name": "val connectionTime: Long? = null", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.connectionTime", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/connection-time.html", "searchKeys": ["connectionTime", "val connectionTime: Long? = null", "com.android.proxy_self.domain.entities.ProxyStatusInfo.connectionTime"]}, {"name": "val connectionType: String", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.connectionType", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/connection-type.html", "searchKeys": ["connectionType", "val connectionType: String", "com.android.proxy_self.infrastructure.managers.NetworkInfo.connectionType"]}, {"name": "val createdAt: Long", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.createdAt", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/created-at.html", "searchKeys": ["createdAt", "val createdAt: Long", "com.android.proxy_self.data.datasources.local.ProxyEntity.createdAt"]}, {"name": "val createdAt: Long", "description": "com.android.proxy_self.domain.entities.ProxyConfig.createdAt", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/created-at.html", "searchKeys": ["createdAt", "val createdAt: Long", "com.android.proxy_self.domain.entities.ProxyConfig.createdAt"]}, {"name": "val data: ByteArray", "description": "com.android.proxy_self.infrastructure.services.IpPacket.data", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/data.html", "searchKeys": ["data", "val data: ByteArray", "com.android.proxy_self.infrastructure.services.IpPacket.data"]}, {"name": "val data: T", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Success.data", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/data.html", "searchKeys": ["data", "val data: T", "com.android.proxy_self.infrastructure.utils.ValidationResult.Success.data"]}, {"name": "val destIp: ByteArray", "description": "com.android.proxy_self.infrastructure.services.IpPacket.destIp", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/dest-ip.html", "searchKeys": ["destIp", "val destIp: ByteArray", "com.android.proxy_self.infrastructure.services.IpPacket.destIp"]}, {"name": "val developerEmail: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.developerEmail", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/developer-email.html", "searchKeys": ["developerEmail", "val developerEmail: String", "com.android.proxy_self.presentation.state.AboutUiState.developerEmail"]}, {"name": "val developerName: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.developerName", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/developer-name.html", "searchKeys": ["developerName", "val developerName: String", "com.android.proxy_self.presentation.state.AboutUiState.developerName"]}, {"name": "val displayName: String", "description": "com.android.proxy_self.domain.entities.ProxyStatus.displayName", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/display-name.html", "searchKeys": ["displayName", "val displayName: String", "com.android.proxy_self.domain.entities.ProxyStatus.displayName"]}, {"name": "val displayName: String", "description": "com.android.proxy_self.domain.entities.ProxyType.displayName", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/display-name.html", "searchKeys": ["displayName", "val displayName: String", "com.android.proxy_self.domain.entities.ProxyType.displayName"]}, {"name": "val domainCount: Int", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.domainCount", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/domain-count.html", "searchKeys": ["domainCount", "val domainCount: Int", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.domainCount"]}, {"name": "val domains: List<String>", "description": "com.android.proxy_self.domain.entities.ProxyConfig.domains", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/domains.html", "searchKeys": ["domains", "val domains: List<String>", "com.android.proxy_self.domain.entities.ProxyConfig.domains"]}, {"name": "val domains: List<String>", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.domains", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/domains.html", "searchKeys": ["domains", "val domains: List<String>", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.domains"]}, {"name": "val domains: String", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.domains", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/domains.html", "searchKeys": ["domains", "val domains: String", "com.android.proxy_self.data.datasources.local.ProxyEntity.domains"]}, {"name": "val domains: String", "description": "com.android.proxy_self.presentation.state.ProxyUiState.domains", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/domains.html", "searchKeys": ["domains", "val domains: String", "com.android.proxy_self.presentation.state.ProxyUiState.domains"]}, {"name": "val encryptedPassword: String", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.encryptedPassword", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/encrypted-password.html", "searchKeys": ["encryptedPassword", "val encryptedPassword: String", "com.android.proxy_self.data.datasources.local.ProxyEntity.encryptedPassword"]}, {"name": "val entries: EnumEntries<ButtonVariant>", "description": "com.android.proxy_self.presentation.ui.components.ButtonVariant.entries", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<ButtonVariant>", "com.android.proxy_self.presentation.ui.components.ButtonVariant.entries"]}, {"name": "val entries: EnumEntries<NetworkState>", "description": "com.android.proxy_self.infrastructure.managers.NetworkState.entries", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<NetworkState>", "com.android.proxy_self.infrastructure.managers.NetworkState.entries"]}, {"name": "val entries: EnumEntries<ProxyStatus>", "description": "com.android.proxy_self.domain.entities.ProxyStatus.entries", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<ProxyStatus>", "com.android.proxy_self.domain.entities.ProxyStatus.entries"]}, {"name": "val entries: EnumEntries<ProxyType>", "description": "com.android.proxy_self.domain.entities.ProxyType.entries", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<ProxyType>", "com.android.proxy_self.domain.entities.ProxyType.entries"]}, {"name": "val error: String", "description": "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure.error", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/error.html", "searchKeys": ["error", "val error: String", "com.android.proxy_self.presentation.state.ConnectionTestResult.Failure.error"]}, {"name": "val error: String? = null", "description": "com.android.proxy_self.presentation.state.ProxyUiState.error", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/error.html", "searchKeys": ["error", "val error: String? = null", "com.android.proxy_self.presentation.state.ProxyUiState.error"]}, {"name": "val error: String? = null", "description": "com.android.proxy_self.presentation.state.SettingsUiState.error", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/error.html", "searchKeys": ["error", "val error: String? = null", "com.android.proxy_self.presentation.state.SettingsUiState.error"]}, {"name": "val errors: List<String>", "description": "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid.errors", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/errors.html", "searchKeys": ["errors", "val errors: List<String>", "com.android.proxy_self.infrastructure.managers.ProxyValidationResult.Invalid.errors"]}, {"name": "val githubUrl: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.githubUrl", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/github-url.html", "searchKeys": ["githubUrl", "val githubUrl: String", "com.android.proxy_self.presentation.state.AboutUiState.githubUrl"]}, {"name": "val id: Long = 0", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.id", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/id.html", "searchKeys": ["id", "val id: Long = 0", "com.android.proxy_self.data.datasources.local.ProxyEntity.id"]}, {"name": "val id: Long = 0", "description": "com.android.proxy_self.domain.entities.ProxyConfig.id", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/id.html", "searchKeys": ["id", "val id: Long = 0", "com.android.proxy_self.domain.entities.ProxyConfig.id"]}, {"name": "val isAutoStartEnabled: Boolean = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isAutoStartEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-auto-start-enabled.html", "searchKeys": ["isAutoStartEnabled", "val isAutoStartEnabled: Boolean = false", "com.android.proxy_self.presentation.state.SettingsUiState.isAutoStartEnabled"]}, {"name": "val isAvailable: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.isAvailable", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-available.html", "searchKeys": ["isAvailable", "val isAvailable: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkInfo.isAvailable"]}, {"name": "val isCellular: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.isCellular", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-cellular.html", "searchKeys": ["isCellular", "val isCellular: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkInfo.isCellular"]}, {"name": "val isCellular: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.isCellular", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-cellular.html", "searchKeys": ["isCellular", "val isCellular: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkInfo.isCellular"]}, {"name": "val isClearing: Bo<PERSON>an = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isClearing", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-clearing.html", "searchKeys": ["isClearing", "val isClearing: Bo<PERSON>an = false", "com.android.proxy_self.presentation.state.SettingsUiState.isClearing"]}, {"name": "val isConnected: Boolean", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.isConnected", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-connected.html", "searchKeys": ["isConnected", "val isConnected: Boolean", "com.android.proxy_self.infrastructure.managers.NetworkInfo.isConnected"]}, {"name": "val isDarkThemeEnabled: Boolean = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isDarkThemeEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-dark-theme-enabled.html", "searchKeys": ["isDarkThemeEnabled", "val isDarkThemeEnabled: Boolean = false", "com.android.proxy_self.presentation.state.SettingsUiState.isDarkThemeEnabled"]}, {"name": "val isEnabled: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.isEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/is-enabled.html", "searchKeys": ["isEnabled", "val isEnabled: <PERSON><PERSON><PERSON>", "com.android.proxy_self.data.datasources.local.ProxyEntity.isEnabled"]}, {"name": "val isEnabled: <PERSON><PERSON><PERSON> = false", "description": "com.android.proxy_self.domain.entities.ProxyConfig.isEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/is-enabled.html", "searchKeys": ["isEnabled", "val isEnabled: <PERSON><PERSON><PERSON> = false", "com.android.proxy_self.domain.entities.ProxyConfig.isEnabled"]}, {"name": "val isExporting: Boolean = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isExporting", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-exporting.html", "searchKeys": ["isExporting", "val isExporting: Boolean = false", "com.android.proxy_self.presentation.state.SettingsUiState.isExporting"]}, {"name": "val isImporting: Boolean = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isImporting", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-importing.html", "searchKeys": ["isImporting", "val isImporting: Boolean = false", "com.android.proxy_self.presentation.state.SettingsUiState.isImporting"]}, {"name": "val isLoading: <PERSON><PERSON><PERSON> = false", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isLoading", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-loading.html", "searchKeys": ["isLoading", "val isLoading: <PERSON><PERSON><PERSON> = false", "com.android.proxy_self.presentation.state.ProxyUiState.isLoading"]}, {"name": "val isLoading: <PERSON><PERSON><PERSON> = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isLoading", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-loading.html", "searchKeys": ["isLoading", "val isLoading: <PERSON><PERSON><PERSON> = false", "com.android.proxy_self.presentation.state.SettingsUiState.isLoading"]}, {"name": "val isMetered: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.isMetered", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-metered.html", "searchKeys": ["isMetered", "val isMetered: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkInfo.isMetered"]}, {"name": "val isMetered: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.isMetered", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-metered.html", "searchKeys": ["isMetered", "val isMetered: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkInfo.isMetered"]}, {"name": "val isNotificationsEnabled: Boolean = true", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isNotificationsEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-notifications-enabled.html", "searchKeys": ["isNotificationsEnabled", "val isNotificationsEnabled: Boolean = true", "com.android.proxy_self.presentation.state.SettingsUiState.isNotificationsEnabled"]}, {"name": "val isPasswordVisible: Boolean = false", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isPasswordVisible", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-password-visible.html", "searchKeys": ["isPasswordVisible", "val isPasswordVisible: Boolean = false", "com.android.proxy_self.presentation.state.ProxyUiState.isPasswordVisible"]}, {"name": "val isProxyEnabled: Boolean = false", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isProxyEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-proxy-enabled.html", "searchKeys": ["isProxyEnabled", "val isProxyEnabled: Boolean = false", "com.android.proxy_self.presentation.state.ProxyUiState.isProxyEnabled"]}, {"name": "val isProxyEnabled: StateFlow<Boolean>", "description": "com.android.proxy_self.infrastructure.services.ProxyService.isProxyEnabled", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/is-proxy-enabled.html", "searchKeys": ["isProxyEnabled", "val isProxyEnabled: StateFlow<Boolean>", "com.android.proxy_self.infrastructure.services.ProxyService.isProxyEnabled"]}, {"name": "val isSaving: Boolean = false", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isSaving", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-saving.html", "searchKeys": ["isSaving", "val isSaving: Boolean = false", "com.android.proxy_self.presentation.state.ProxyUiState.isSaving"]}, {"name": "val isSaving: Boolean = false", "description": "com.android.proxy_self.presentation.state.SettingsUiState.isSaving", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-saving.html", "searchKeys": ["isSaving", "val isSaving: Boolean = false", "com.android.proxy_self.presentation.state.SettingsUiState.isSaving"]}, {"name": "val isTestingConnection: Boolean = false", "description": "com.android.proxy_self.presentation.state.ProxyUiState.isTestingConnection", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-testing-connection.html", "searchKeys": ["isTestingConnection", "val isTestingConnection: Boolean = false", "com.android.proxy_self.presentation.state.ProxyUiState.isTestingConnection"]}, {"name": "val isWifi: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.isWifi", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-wifi.html", "searchKeys": ["isWifi", "val isWifi: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.managers.NetworkInfo.isWifi"]}, {"name": "val isWifi: <PERSON><PERSON><PERSON>", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.isWifi", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-wifi.html", "searchKeys": ["isWifi", "val isWifi: <PERSON><PERSON><PERSON>", "com.android.proxy_self.infrastructure.utils.NetworkInfo.isWifi"]}, {"name": "val lastError: String? = null", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.lastError", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/last-error.html", "searchKeys": ["lastError", "val lastError: String? = null", "com.android.proxy_self.domain.entities.ProxyStatusInfo.lastError"]}, {"name": "val license: String", "description": "com.android.proxy_self.presentation.state.LibraryInfo.license", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/license.html", "searchKeys": ["license", "val license: String", "com.android.proxy_self.presentation.state.LibraryInfo.license"]}, {"name": "val licenseUrl: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.licenseUrl", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/license-url.html", "searchKeys": ["licenseUrl", "val licenseUrl: String", "com.android.proxy_self.presentation.state.AboutUiState.licenseUrl"]}, {"name": "val message: String", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error.message", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/message.html", "searchKeys": ["message", "val message: String", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Error.message"]}, {"name": "val message: String", "description": "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success.message", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/message.html", "searchKeys": ["message", "val message: String", "com.android.proxy_self.infrastructure.managers.SocksProxyResult.Success.message"]}, {"name": "val message: String", "description": "com.android.proxy_self.infrastructure.utils.ValidationResult.Error.message", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/message.html", "searchKeys": ["message", "val message: String", "com.android.proxy_self.infrastructure.utils.ValidationResult.Error.message"]}, {"name": "val message: String? = null", "description": "com.android.proxy_self.presentation.state.SettingsUiState.message", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/message.html", "searchKeys": ["message", "val message: String? = null", "com.android.proxy_self.presentation.state.SettingsUiState.message"]}, {"name": "val name: String", "description": "com.android.proxy_self.presentation.state.LibraryInfo.name", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/name.html", "searchKeys": ["name", "val name: String", "com.android.proxy_self.presentation.state.LibraryInfo.name"]}, {"name": "val openSourceLibraries: List<LibraryInfo>", "description": "com.android.proxy_self.presentation.state.AboutUiState.openSourceLibraries", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/open-source-libraries.html", "searchKeys": ["openSourceLibraries", "val openSourceLibraries: List<LibraryInfo>", "com.android.proxy_self.presentation.state.AboutUiState.openSourceLibraries"]}, {"name": "val packetsReceived: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.packetsReceived", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/packets-received.html", "searchKeys": ["packetsReceived", "val packetsReceived: Long", "com.android.proxy_self.infrastructure.managers.NetworkStats.packetsReceived"]}, {"name": "val packetsSent: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.packetsSent", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/packets-sent.html", "searchKeys": ["packetsSent", "val packetsSent: Long", "com.android.proxy_self.infrastructure.managers.NetworkStats.packetsSent"]}, {"name": "val password: String", "description": "com.android.proxy_self.domain.entities.ProxyConfig.password", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/password.html", "searchKeys": ["password", "val password: String", "com.android.proxy_self.domain.entities.ProxyConfig.password"]}, {"name": "val password: String", "description": "com.android.proxy_self.presentation.state.ProxyUiState.password", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/password.html", "searchKeys": ["password", "val password: String", "com.android.proxy_self.presentation.state.ProxyUiState.password"]}, {"name": "val privacyPolicyUrl: String", "description": "com.android.proxy_self.presentation.state.AboutUiState.privacyPolicyUrl", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/privacy-policy-url.html", "searchKeys": ["privacyPolicyUrl", "val privacyPolicyUrl: String", "com.android.proxy_self.presentation.state.AboutUiState.privacyPolicyUrl"]}, {"name": "val protocol: Int", "description": "com.android.proxy_self.infrastructure.services.IpPacket.protocol", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/protocol.html", "searchKeys": ["protocol", "val protocol: Int", "com.android.proxy_self.infrastructure.services.IpPacket.protocol"]}, {"name": "val proxyConfig: ProxyConfig", "description": "com.android.proxy_self.presentation.state.ProxyUiState.proxyConfig", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/proxy-config.html", "searchKeys": ["proxyConfig", "val proxyConfig: ProxyConfig", "com.android.proxy_self.presentation.state.ProxyUiState.proxyConfig"]}, {"name": "val proxyStatus: ProxyStatusInfo? = null", "description": "com.android.proxy_self.presentation.state.ProxyUiState.proxyStatus", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/proxy-status.html", "searchKeys": ["proxyStatus", "val proxyStatus: ProxyStatusInfo? = null", "com.android.proxy_self.presentation.state.ProxyUiState.proxyStatus"]}, {"name": "val proxyStatus: StateFlow<ProxyStatusInfo>", "description": "com.android.proxy_self.infrastructure.services.ProxyService.proxyStatus", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/proxy-status.html", "searchKeys": ["proxyStatus", "val proxyStatus: StateFlow<ProxyStatusInfo>", "com.android.proxy_self.infrastructure.services.ProxyService.proxyStatus"]}, {"name": "val proxyType: ProxyType", "description": "com.android.proxy_self.domain.entities.ProxyConfig.proxyType", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/proxy-type.html", "searchKeys": ["proxyType", "val proxyType: ProxyType", "com.android.proxy_self.domain.entities.ProxyConfig.proxyType"]}, {"name": "val proxyType: String", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.proxyType", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/proxy-type.html", "searchKeys": ["proxyType", "val proxyType: String", "com.android.proxy_self.data.datasources.local.ProxyEntity.proxyType"]}, {"name": "val proxyType: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.proxyType", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/proxy-type.html", "searchKeys": ["proxyType", "val proxyType: String", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.proxyType"]}, {"name": "val route: String", "description": "com.android.proxy_self.presentation.ui.navigation.Screen.route", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/route.html", "searchKeys": ["route", "val route: String", "com.android.proxy_self.presentation.ui.navigation.Screen.route"]}, {"name": "val selectedProxyType: ProxyType", "description": "com.android.proxy_self.presentation.state.ProxyUiState.selectedProxyType", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/selected-proxy-type.html", "searchKeys": ["selectedProxyType", "val selectedProxyType: ProxyType", "com.android.proxy_self.presentation.state.ProxyUiState.selectedProxyType"]}, {"name": "val serverAddress: String", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.serverAddress", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/server-address.html", "searchKeys": ["serverAddress", "val serverAddress: String", "com.android.proxy_self.data.datasources.local.ProxyEntity.serverAddress"]}, {"name": "val serverAddress: String", "description": "com.android.proxy_self.domain.entities.ProxyConfig.serverAddress", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/server-address.html", "searchKeys": ["serverAddress", "val serverAddress: String", "com.android.proxy_self.domain.entities.ProxyConfig.serverAddress"]}, {"name": "val serverAddress: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.serverAddress", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/server-address.html", "searchKeys": ["serverAddress", "val serverAddress: String", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.serverAddress"]}, {"name": "val serverAddress: String", "description": "com.android.proxy_self.presentation.state.ProxyUiState.serverAddress", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/server-address.html", "searchKeys": ["serverAddress", "val serverAddress: String", "com.android.proxy_self.presentation.state.ProxyUiState.serverAddress"]}, {"name": "val serverPort: Int", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.serverPort", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/server-port.html", "searchKeys": ["serverPort", "val serverPort: Int", "com.android.proxy_self.data.datasources.local.ProxyEntity.serverPort"]}, {"name": "val serverPort: Int", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.serverPort", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/server-port.html", "searchKeys": ["serverPort", "val serverPort: Int", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.serverPort"]}, {"name": "val serverPort: Int = 0", "description": "com.android.proxy_self.domain.entities.ProxyConfig.serverPort", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/server-port.html", "searchKeys": ["serverPort", "val serverPort: Int = 0", "com.android.proxy_self.domain.entities.ProxyConfig.serverPort"]}, {"name": "val serverPort: String", "description": "com.android.proxy_self.presentation.state.ProxyUiState.serverPort", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/server-port.html", "searchKeys": ["serverPort", "val serverPort: String", "com.android.proxy_self.presentation.state.ProxyUiState.serverPort"]}, {"name": "val sourceIp: ByteArray", "description": "com.android.proxy_self.infrastructure.services.IpPacket.sourceIp", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/source-ip.html", "searchKeys": ["sourceIp", "val sourceIp: ByteArray", "com.android.proxy_self.infrastructure.services.IpPacket.sourceIp"]}, {"name": "val status: ProxyStatus", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.status", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/status.html", "searchKeys": ["status", "val status: ProxyStatus", "com.android.proxy_self.domain.entities.ProxyStatusInfo.status"]}, {"name": "val successMessage: String? = null", "description": "com.android.proxy_self.presentation.state.ProxyUiState.successMessage", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/success-message.html", "searchKeys": ["successMessage", "val successMessage: String? = null", "com.android.proxy_self.presentation.state.ProxyUiState.successMessage"]}, {"name": "val timestamp: Long", "description": "com.android.proxy_self.domain.entities.ProxyStatusInfo.timestamp", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/timestamp.html", "searchKeys": ["timestamp", "val timestamp: Long", "com.android.proxy_self.domain.entities.ProxyStatusInfo.timestamp"]}, {"name": "val timestamp: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkInfo.timestamp", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/timestamp.html", "searchKeys": ["timestamp", "val timestamp: Long", "com.android.proxy_self.infrastructure.managers.NetworkInfo.timestamp"]}, {"name": "val timestamp: Long", "description": "com.android.proxy_self.infrastructure.managers.NetworkStats.timestamp", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/timestamp.html", "searchKeys": ["timestamp", "val timestamp: Long", "com.android.proxy_self.infrastructure.managers.NetworkStats.timestamp"]}, {"name": "val type: String", "description": "com.android.proxy_self.infrastructure.utils.NetworkInfo.type", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/type.html", "searchKeys": ["type", "val type: String", "com.android.proxy_self.infrastructure.utils.NetworkInfo.type"]}, {"name": "val uiState: StateFlow<ProxyUiState>", "description": "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.uiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/ui-state.html", "searchKeys": ["uiState", "val uiState: StateFlow<ProxyUiState>", "com.android.proxy_self.presentation.viewmodel.ProxyViewModel.uiState"]}, {"name": "val uiState: StateFlow<SettingsUiState>", "description": "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.uiState", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/ui-state.html", "searchKeys": ["uiState", "val uiState: StateFlow<SettingsUiState>", "com.android.proxy_self.presentation.viewmodel.SettingsViewModel.uiState"]}, {"name": "val updatedAt: Long", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.updatedAt", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/updated-at.html", "searchKeys": ["updatedAt", "val updatedAt: Long", "com.android.proxy_self.data.datasources.local.ProxyEntity.updatedAt"]}, {"name": "val updatedAt: Long", "description": "com.android.proxy_self.domain.entities.ProxyConfig.updatedAt", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/updated-at.html", "searchKeys": ["updatedAt", "val updatedAt: Long", "com.android.proxy_self.domain.entities.ProxyConfig.updatedAt"]}, {"name": "val url: String", "description": "com.android.proxy_self.presentation.state.LibraryInfo.url", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/url.html", "searchKeys": ["url", "val url: String", "com.android.proxy_self.presentation.state.LibraryInfo.url"]}, {"name": "val username: String", "description": "com.android.proxy_self.data.datasources.local.ProxyEntity.username", "location": "-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/username.html", "searchKeys": ["username", "val username: String", "com.android.proxy_self.data.datasources.local.ProxyEntity.username"]}, {"name": "val username: String", "description": "com.android.proxy_self.domain.entities.ProxyConfig.username", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/username.html", "searchKeys": ["username", "val username: String", "com.android.proxy_self.domain.entities.ProxyConfig.username"]}, {"name": "val username: String", "description": "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.username", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/username.html", "searchKeys": ["username", "val username: String", "com.android.proxy_self.infrastructure.managers.ProxyConnectionInfo.username"]}, {"name": "val username: String", "description": "com.android.proxy_self.presentation.state.ProxyUiState.username", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/username.html", "searchKeys": ["username", "val username: String", "com.android.proxy_self.presentation.state.ProxyUiState.username"]}, {"name": "val validationErrors: Map<String, String>", "description": "com.android.proxy_self.presentation.state.ProxyUiState.validationErrors", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/validation-errors.html", "searchKeys": ["validationErrors", "val validationErrors: Map<String, String>", "com.android.proxy_self.presentation.state.ProxyUiState.validationErrors"]}, {"name": "val value: String", "description": "com.android.proxy_self.domain.entities.ProxyType.value", "location": "-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/value.html", "searchKeys": ["value", "val value: String", "com.android.proxy_self.domain.entities.ProxyType.value"]}, {"name": "val version: Int", "description": "com.android.proxy_self.infrastructure.services.IpPacket.version", "location": "-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/version.html", "searchKeys": ["version", "val version: Int", "com.android.proxy_self.infrastructure.services.IpPacket.version"]}, {"name": "val version: String", "description": "com.android.proxy_self.presentation.state.LibraryInfo.version", "location": "-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/version.html", "searchKeys": ["version", "val version: String", "com.android.proxy_self.presentation.state.LibraryInfo.version"]}]