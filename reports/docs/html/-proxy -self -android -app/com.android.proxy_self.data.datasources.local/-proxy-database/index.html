<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>ProxyDatabase</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
</head>
<body>
    <div class="root">
<nav class="navigation" id="navigation-wrapper">
    <div class="navigation--inner">
        <div class="navigation-title">
            <button class="menu-toggle" id="menu-toggle" type="button">toggle menu</button>
            <div class="library-name">
                    <a class="library-name--link" href="../../../index.html">
                            Proxy Self Android App
                    </a>
            </div>
            <div class="library-version">
1.0.0            </div>
        </div>
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":app:dokkaHtml/release">androidJvm</button>
        </div>
    </div>
    <div class="navigation-controls">
        <button class="navigation-controls--btn navigation-controls--theme" id="theme-toggle-button" type="button">switch theme</button>
        <div class="navigation-controls--btn navigation-controls--search" id="searchBar" role="button">search in API</div>
    </div>
</nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="sidebar--inner" id="sideMenu"></div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Proxy Self Android App::com.android.proxy_self.data.datasources.local/ProxyDatabase///PointingToDeclaration//492899073">
  <div class="breadcrumbs"><a href="../../../index.html">Proxy Self Android App</a><span class="delimiter">/</span><a href="../index.html">com.android.proxy_self.data.datasources.local</a><span class="delimiter">/</span><span class="current">ProxyDatabase</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Proxy</span><wbr></wbr><span><span>Database</span></span></h1>
    <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="index.html">ProxyDatabase</a> : <a href="https://developer.android.com/reference/kotlin/androidx/room/RoomDatabase.html">RoomDatabase</a></div><p class="paragraph">Room database for proxy application Follows Single Responsibility Principle</p></div></div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="CONSTRUCTOR">
        <h2 class="">Constructors</h2>
        <div class="table"><a data-name="881243997%2FConstructors%2F492899073" anchor-label="ProxyDatabase" id="881243997%2FConstructors%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-togglable="CONSTRUCTOR" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-proxy-database.html"><span>Proxy</span><wbr></wbr><span><span>Database</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="881243997%2FConstructors%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">constructor</span><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="1872297709%2FClasslikes%2F492899073" anchor-label="Companion" id="1872297709%2FClasslikes%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-companion/index.html"><span><span>Companion</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1872297709%2FClasslikes%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">object </span><a href="-companion/index.html">Companion</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="-990093491%2FProperties%2F492899073" anchor-label="invalidationTracker" id="-990093491%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-990093491%2FProperties%2F492899073"><span>invalidation</span><wbr></wbr><span><span>Tracker</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-990093491%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#-990093491%2FProperties%2F492899073">invalidationTracker</a><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/androidx/room/InvalidationTracker.html">InvalidationTracker</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-277138657%2FProperties%2F492899073" anchor-label="isOpen" id="-277138657%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-277138657%2FProperties%2F492899073"><span>is</span><wbr></wbr><span><span>Open</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-277138657%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#-277138657%2FProperties%2F492899073">isOpen</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="475302114%2FProperties%2F492899073" anchor-label="isOpenInternal" id="475302114%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#475302114%2FProperties%2F492899073"><span>is</span><wbr></wbr><span>Open</span><wbr></wbr><span><span>Internal</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="475302114%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#475302114%2FProperties%2F492899073">isOpenInternal</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1864821605%2FProperties%2F492899073" anchor-label="openHelper" id="-1864821605%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1864821605%2FProperties%2F492899073"><span>open</span><wbr></wbr><span><span>Helper</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1864821605%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#-1864821605%2FProperties%2F492899073">openHelper</a><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/androidx/sqlite/db/SupportSQLiteOpenHelper.html">SupportSQLiteOpenHelper</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-177284564%2FProperties%2F492899073" anchor-label="queryExecutor" id="-177284564%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-177284564%2FProperties%2F492899073"><span>query</span><wbr></wbr><span><span>Executor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-177284564%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#-177284564%2FProperties%2F492899073">queryExecutor</a><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/util/concurrent/Executor.html">Executor</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1027959380%2FProperties%2F492899073" anchor-label="suspendingTransactionId" id="1027959380%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1027959380%2FProperties%2F492899073"><span>suspending</span><wbr></wbr><span>Transaction</span><wbr></wbr><span><span>Id</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1027959380%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#1027959380%2FProperties%2F492899073">suspendingTransactionId</a><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/lang/ThreadLocal.html">ThreadLocal</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="722320214%2FProperties%2F492899073" anchor-label="transactionExecutor" id="722320214%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#722320214%2FProperties%2F492899073"><span>transaction</span><wbr></wbr><span><span>Executor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="722320214%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="index.html#722320214%2FProperties%2F492899073">transactionExecutor</a><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/util/concurrent/Executor.html">Executor</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-917214377%2FFunctions%2F492899073" anchor-label="assertNotMainThread" id="-917214377%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-917214377%2FFunctions%2F492899073"><span>assert</span><wbr></wbr><span>Not</span><wbr></wbr><span>Main</span><wbr></wbr><span><span>Thread</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-917214377%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-917214377%2FFunctions%2F492899073"><span class="token function">assertNotMainThread</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1166251624%2FFunctions%2F492899073" anchor-label="assertNotSuspendingTransaction" id="1166251624%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1166251624%2FFunctions%2F492899073"><span>assert</span><wbr></wbr><span>Not</span><wbr></wbr><span>Suspending</span><wbr></wbr><span><span>Transaction</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1166251624%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1166251624%2FFunctions%2F492899073"><span class="token function">assertNotSuspendingTransaction</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1020009182%2FFunctions%2F492899073" anchor-label="beginTransaction" id="1020009182%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1020009182%2FFunctions%2F492899073"><span>begin</span><wbr></wbr><span><span>Transaction</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1020009182%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1020009182%2FFunctions%2F492899073"><span class="token function"><strike>beginTransaction</strike></span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="404244410%2FFunctions%2F492899073" anchor-label="clearAllTables" id="404244410%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#404244410%2FFunctions%2F492899073"><span>clear</span><wbr></wbr><span>All</span><wbr></wbr><span><span>Tables</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="404244410%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#404244410%2FFunctions%2F492899073"><span class="token function">clearAllTables</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1674273423%2FFunctions%2F492899073" anchor-label="close" id="1674273423%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1674273423%2FFunctions%2F492899073"><span><span>close</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1674273423%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1674273423%2FFunctions%2F492899073"><span class="token function">close</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="162913197%2FFunctions%2F492899073" anchor-label="compileStatement" id="162913197%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#162913197%2FFunctions%2F492899073"><span>compile</span><wbr></wbr><span><span>Statement</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="162913197%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#162913197%2FFunctions%2F492899073"><span class="token function">compileStatement</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">sql<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/androidx/sqlite/db/SupportSQLiteStatement.html">SupportSQLiteStatement</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1420085996%2FFunctions%2F492899073" anchor-label="createAutoMigrations" id="1420085996%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1420085996%2FFunctions%2F492899073"><span>create</span><wbr></wbr><span>Auto</span><wbr></wbr><span><span>Migrations</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1420085996%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1420085996%2FFunctions%2F492899073"><span class="token function">createAutoMigrations</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">autoMigrationSpecs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-list/index.html">List</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/Migration.html">Migration</a><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="622722960%2FFunctions%2F492899073" anchor-label="endTransaction" id="622722960%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#622722960%2FFunctions%2F492899073"><span>end</span><wbr></wbr><span><span>Transaction</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="622722960%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#622722960%2FFunctions%2F492899073"><span class="token function"><strike>endTransaction</strike></span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="178130989%2FFunctions%2F492899073" anchor-label="getAutoMigrations" id="178130989%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#178130989%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Auto</span><wbr></wbr><span><span>Migrations</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="178130989%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#178130989%2FFunctions%2F492899073"><span class="token function"><strike>getAutoMigrations</strike></span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">autoMigrationSpecs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-list/index.html">List</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/Migration.html">Migration</a><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1278223499%2FFunctions%2F492899073" anchor-label="getCoroutineScope" id="-1278223499%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1278223499%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Coroutine</span><wbr></wbr><span><span>Scope</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1278223499%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-1278223499%2FFunctions%2F492899073"><span class="token function">getCoroutineScope</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="kotlinx.coroutines/CoroutineScope///PointingToDeclaration/">CoroutineScope</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-779149974%2FFunctions%2F492899073" anchor-label="getQueryContext" id="-779149974%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-779149974%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Query</span><wbr></wbr><span><span>Context</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-779149974%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-779149974%2FFunctions%2F492899073"><span class="token function">getQueryContext</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.coroutines/-coroutine-context/index.html">CoroutineContext</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-432702106%2FFunctions%2F492899073" anchor-label="getRequiredAutoMigrationSpecClasses" id="-432702106%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-432702106%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Required</span><wbr></wbr><span>Auto</span><wbr></wbr><span>Migration</span><wbr></wbr><span>Spec</span><wbr></wbr><span><span>Classes</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-432702106%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-432702106%2FFunctions%2F492899073"><span class="token function">getRequiredAutoMigrationSpecClasses</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-set/index.html">Set</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1623281881%2FFunctions%2F492899073" anchor-label="getRequiredAutoMigrationSpecs" id="1623281881%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1623281881%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Required</span><wbr></wbr><span>Auto</span><wbr></wbr><span>Migration</span><wbr></wbr><span><span>Specs</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1623281881%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1623281881%2FFunctions%2F492899073"><span class="token function"><strike>getRequiredAutoMigrationSpecs</strike></span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.collections/-set/index.html">Set</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://developer.android.com/reference/kotlin/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://developer.android.com/reference/kotlin/androidx/room/migration/AutoMigrationSpec.html">AutoMigrationSpec</a><span class="token operator">&gt;</span><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1828819717%2FFunctions%2F492899073" anchor-label="getTypeConverter" id="1828819717%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1472154772%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span>Type</span><wbr></wbr><span><span>Converter</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1828819717%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1472154772%2FFunctions%2F492899073">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="index.html#-1472154772%2FFunctions%2F492899073"><span class="token function"><strike>getTypeConverter</strike></span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">klass<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1472154772%2FFunctions%2F492899073">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#-1472154772%2FFunctions%2F492899073">T</a><span class="token operator">?</span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#2031305957%2FFunctions%2F492899073">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="index.html#2031305957%2FFunctions%2F492899073"><span class="token function">getTypeConverter</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">klass<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#2031305957%2FFunctions%2F492899073">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#2031305957%2FFunctions%2F492899073">T</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1039887154%2FFunctions%2F492899073" anchor-label="init" id="1039887154%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1039887154%2FFunctions%2F492899073"><span><span>init</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1039887154%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1039887154%2FFunctions%2F492899073"><span class="token function">init</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configuration<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/androidx/room/DatabaseConfiguration.html">DatabaseConfiguration</a></span></span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1889647314%2FFunctions%2F492899073" anchor-label="inTransaction" id="-1889647314%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1889647314%2FFunctions%2F492899073"><span>in</span><wbr></wbr><span><span>Transaction</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1889647314%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-1889647314%2FFunctions%2F492899073"><span class="token function">inTransaction</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1162808048%2FFunctions%2F492899073" anchor-label="proxyDao" id="1162808048%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="proxy-dao.html"><span>proxy</span><wbr></wbr><span><span>Dao</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1162808048%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="proxy-dao.html"><span class="token function">proxyDao</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-proxy-dao/index.html">ProxyDao</a></div><div class="brief "><p class="paragraph">Get ProxyDao instance</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="439512128%2FFunctions%2F492899073" anchor-label="query" id="439512128%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#604106995%2FFunctions%2F492899073"><span><span>query</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="439512128%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#604106995%2FFunctions%2F492899073"><span class="token function">query</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">query<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/androidx/sqlite/db/SupportSQLiteQuery.html">SupportSQLiteQuery</a><span class="token punctuation">, </span></span><span class="parameter ">signal<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/android/os/CancellationSignal.html">CancellationSignal</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/android/database/Cursor.html">Cursor</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-1376474873%2FFunctions%2F492899073"><span class="token function">query</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">query<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">args<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/android/database/Cursor.html">Cursor</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="919934728%2FFunctions%2F492899073" anchor-label="runInTransaction" id="919934728%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1063989044%2FFunctions%2F492899073"><span>run</span><wbr></wbr><span>In</span><wbr></wbr><span><span>Transaction</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="919934728%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#1063989044%2FFunctions%2F492899073"><span class="token function">runInTransaction</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">body<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/lang/Runnable.html">Runnable</a></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1842697888%2FFunctions%2F492899073">V</a><span class="token operator">&gt; </span><a href="index.html#-1842697888%2FFunctions%2F492899073"><span class="token function">runInTransaction</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">body<span class="token operator">: </span><a href="https://developer.android.com/reference/kotlin/java/util/concurrent/Callable.html">Callable</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1842697888%2FFunctions%2F492899073">V</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#-1842697888%2FFunctions%2F492899073">V</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="954356125%2FFunctions%2F492899073" anchor-label="setTransactionSuccessful" id="954356125%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#954356125%2FFunctions%2F492899073"><span>set</span><wbr></wbr><span>Transaction</span><wbr></wbr><span><span>Successful</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="954356125%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#954356125%2FFunctions%2F492899073"><span class="token function"><strike>setTransactionSuccessful</strike></span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>© 2025 Copyright</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
            </div>
        </div>
    </div>
</body>
</html>
