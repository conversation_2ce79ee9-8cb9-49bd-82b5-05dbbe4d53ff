# Software Requirements Specification (SRS)
## Android Proxy Application with Authentication
### Version 1.0
### Date: July 13, 2025

---

## 1. Giới Thiệu

### 1.1 M<PERSON><PERSON> Đích
Tài liệu này mô tả chi tiết các yêu cầu phần mềm cho ứng dụng Android Proxy với Authentication. Ứng dụng này được phát triển để giải quyết vấn đề thiết bị Android không hỗ trợ proxy authentication trong cài đặt WiFi mặc định, đồng thời cung cấp khả năng định tuyến có chọn lọc theo domain.

### 1.2 Phạm Vi Dự Án
Ứng dụng Android Proxy sẽ:
- Hỗ trợ proxy authentication cho SOCKS5 và HTTP
- Cho phép định tuyến theo domain cụ thể (whitelist)
- C<PERSON> cấp giao diện người dùng hiện đại với Jetpack Compose
- Triển khai theo Clean Architecture và SOLID principles
- Hỗ trợ Quick Settings Tile để điều khiển nhanh
- <PERSON><PERSON><PERSON> dịch vụ nền để duy trì kết nối proxy

### 1.3 Định Nghĩa và Từ Viết Tắt
- **SRS**: Software Requirements Specification
- **API**: Application Programming Interface
- **UI**: User Interface
- **UX**: User Experience
- **MVVM**: Model-View-ViewModel
- **DI**: Dependency Injection
- **SOCKS5**: Socket Secure version 5
- **HTTP**: Hypertext Transfer Protocol
- **VPN**: Virtual Private Network
- **SDK**: Software Development Kit

### 1.4 Tham Khảo
- Android Developer Documentation
- Jetpack Compose Documentation
- Material Design 3 Guidelines
- Clean Architecture by Robert C. Martin
- SOLID Principles Documentation

---

## 2. Mô Tả Tổng Quan

### 2.1 Bối Cảnh Sản Phẩm
Ứng dụng này được phát triển để khắc phục hạn chế của Android trong việc hỗ trợ proxy authentication. Nhiều thiết bị Android không thể cấu hình proxy với username/password trong cài đặt WiFi, dẫn đến nhu cầu một giải pháp độc lập.

### 2.2 Chức Năng Chính
- **Proxy Configuration**: Cấu hình proxy với authentication
- **Domain Whitelist**: Chỉ định các domain sử dụng proxy
- **Background Service**: Dịch vụ nền duy trì kết nối
- **Quick Settings**: Điều khiển nhanh từ notification panel
- **Secure Storage**: Lưu trữ thông tin xác thực an toàn
- **Connection Testing**: Kiểm tra kết nối proxy

### 2.3 Đặc Điểm Người Dùng
- **Người dùng chính**: Individuals cần sử dụng proxy với authentication
- **Kỹ năng**: Cơ bản đến trung bình về technology
- **Môi trường**: Sử dụng trên thiết bị Android cá nhân
- **Mục tiêu**: Truy cập internet qua proxy một cách dễ dàng

### 2.4 Ràng Buộc
- **Platform**: Android 10+ (API level 29+)
- **Architecture**: ARM và x86 architecture
- **Permissions**: Cần quyền VPN hoặc root access cho full functionality
- **Network**: Yêu cầu kết nối internet để test proxy

---

## 3. Yêu Cầu Hệ Thống

### 3.1 Yêu Cầu Chức Năng

#### 3.1.1 Quản Lý Cấu Hình Proxy
**FR-001: Cấu Hình Proxy Server**
- **Mô tả**: Người dùng có thể nhập thông tin proxy server
- **Input**: IP address, port, username, password, proxy type
- **Output**: Lưu cấu hình vào local storage
- **Validation**: 
  - IP address format validation
  - Port range validation (1-65535)
  - Username/password không trống
  - Proxy type selection (SOCKS5/HTTP)

**FR-002: Quản Lý Domain Whitelist**
- **Mô tả**: Người dùng có thể chỉ định domains sử dụng proxy
- **Input**: Danh sách domains phân tách bằng dấu phẩy
- **Output**: Lưu domain list vào cấu hình
- **Validation**:
  - Domain format validation
  - Comma-separated parsing
  - Whitespace trimming
  - Duplicate removal

**FR-003: Lưu Trữ Cấu Hình An Toàn**
- **Mô tả**: Mã hóa và lưu trữ thông tin xác thực
- **Security**: Sử dụng Android Security Crypto library
- **Storage**: Room Database hoặc EncryptedSharedPreferences
- **Encryption**: AES-256 cho password fields

#### 3.1.2 Điều Khiển Proxy
**FR-004: Khởi Động Proxy Service**
- **Mô tả**: Bắt đầu dịch vụ proxy với cấu hình đã lưu
- **Process**: Validate config → Start service → Update UI state
- **Error Handling**: Connection timeout, authentication failure
- **Notification**: Hiển thị status trong notification bar

**FR-005: Dừng Proxy Service**
- **Mô tả**: Dừng dịch vụ proxy và reset network
- **Process**: Stop service → Clear routes → Update UI
- **Cleanup**: Release resources và clear notifications

**FR-006: Kiểm Tra Kết Nối**
- **Mô tả**: Test kết nối proxy trước khi activate
- **Method**: Send test request qua proxy
- **Timeout**: 30 seconds maximum
- **Result**: Success/failure với error details

#### 3.1.3 Domain Routing Logic
**FR-007: Domain Matching**
- **Mô tả**: Kiểm tra domain có trong whitelist hay không
- **Algorithm**: Exact match và wildcard support
- **Performance**: Cached lookup cho frequent domains
- **Default**: Traffic không match đi direct

**FR-008: Traffic Routing**
- **Mô tả**: Route traffic dựa trên domain matching
- **Proxy Route**: Domains trong whitelist qua proxy
- **Direct Route**: Domains khác đi direct
- **Fallback**: Direct route nếu proxy fail

#### 3.1.4 Background Service
**FR-009: Foreground Service**
- **Mô tả**: Dịch vụ nền duy trì kết nối proxy
- **Type**: Foreground service với notification
- **Lifecycle**: Start/stop theo user action
- **Persistence**: Maintain connection qua app lifecycle

**FR-010: Boot Receiver**
- **Mô tả**: Tùy chọn auto-start proxy sau reboot
- **Trigger**: BOOT_COMPLETED broadcast
- **Condition**: User enabled auto-start option
- **Delay**: 30 seconds delay after boot

#### 3.1.5 Quick Settings Tile
**FR-011: Quick Settings Integration**
- **Mô tả**: Tile trong notification panel để toggle proxy
- **States**: ON/OFF với appropriate icons
- **Action**: Single tap để toggle state
- **Feedback**: Toast message cho state changes

### 3.2 Yêu Cầu Phi Chức Năng

#### 3.2.1 Performance Requirements
**NFR-001: Response Time**
- UI response time < 100ms cho user interactions
- Proxy connection establishment < 5 seconds
- Domain lookup < 10ms

**NFR-002: Throughput**
- Hỗ trợ concurrent connections
- Bandwidth overhead < 5% khi sử dụng proxy
- Memory usage < 50MB under normal operation

**NFR-003: Scalability**
- Hỗ trợ whitelist lên đến 1000 domains
- Handle 100+ concurrent connections
- Efficient caching cho domain lookups

#### 3.2.2 Security Requirements
**NFR-004: Data Protection**
- Encrypt stored passwords với AES-256
- Secure communication với proxy server
- No plaintext storage của sensitive data

**NFR-005: Authentication**
- Validate proxy credentials securely
- Support multiple authentication methods
- Timeout inactive sessions appropriately

#### 3.2.3 Reliability Requirements
**NFR-006: Availability**
- Service uptime > 99% khi được activate
- Auto-recovery từ connection failures
- Graceful degradation khi proxy unavailable

**NFR-007: Error Handling**
- Comprehensive error logging
- User-friendly error messages
- Automatic retry mechanisms

#### 3.2.4 Usability Requirements
**NFR-008: User Interface**
- Material Design 3 compliance
- Intuitive navigation và workflows
- Accessibility support (TalkBack, high contrast)

**NFR-009: User Experience**
- Onboarding guide cho new users
- Contextual help và tooltips
- Smooth animations và transitions

#### 3.2.5 Compatibility Requirements
**NFR-010: Platform Support**
- Android 10+ (API 29+)
- ARM và x86 architecture support
- Support cho foldable devices

**NFR-011: Network Compatibility**
- IPv4 và IPv6 support
- Various proxy server implementations
- Different network configurations

---

## 4. Giao Diện Hệ Thống

### 4.1 User Interfaces

#### 4.1.1 Main Configuration Screen
**UI-001: Proxy Configuration Form**
- **Components**:
  - OutlinedTextField cho IP address
  - OutlinedTextField cho Port (numeric input)
  - OutlinedTextField cho Username
  - OutlinedTextField cho Password (obscured input)
  - OutlinedTextField cho Domains (multiline)
  - DropdownMenu cho Proxy Type selection
  - Switch cho Enable/Disable proxy
  - Button cho Save Configuration
  - Button cho Test Connection

**UI-002: Status Display**
- **Components**:
  - Card hiển thị current proxy status
  - Connection status indicator
  - Last connection time
  - Data usage statistics

**UI-003: Error Display**
- **Components**:
  - SnackBar cho error messages
  - Validation error text under input fields
  - Loading indicators cho async operations

#### 4.1.2 Settings Screen
**UI-004: Application Settings**
- **Components**:
  - Switch cho Auto-start option
  - Switch cho Dark theme
  - Button cho Clear saved data
  - Button cho Export/Import configuration

#### 4.1.3 About Screen
**UI-005: Application Information**
- **Components**:
  - App version và build info
  - Developer contact information
  - License information
  - Third-party libraries credits

### 4.2 Hardware Interfaces
- **Network Interface**: WiFi và cellular data
- **Storage**: Internal storage cho app data
- **Notification**: LED, vibration, sound

### 4.3 Software Interfaces

#### 4.3.1 External Libraries
- **OkHttp**: HTTP client cho proxy connections
- **Room**: Database cho local storage
- **Hilt**: Dependency injection framework
- **Security Crypto**: Encryption cho sensitive data

#### 4.3.2 Android System APIs
- **VPN Service**: Cho traffic routing (nếu có permission)
- **Notification Manager**: Cho foreground service notifications
- **ConnectivityManager**: Cho network state monitoring
- **TileService**: Cho Quick Settings integration

### 4.4 Communication Interfaces
- **Proxy Protocol**: SOCKS5 và HTTP proxy protocols
- **Network Protocols**: TCP/IP, HTTP/HTTPS
- **Authentication**: Username/password authentication

---

## 5. Kiến Trúc Hệ Thống

### 5.1 Clean Architecture Implementation

#### 5.1.1 Presentation Layer
**Components**:
- `MainActivity`: Main entry point
- `ProxyConfigScreen`: Main configuration UI
- `SettingsScreen`: Application settings
- `ProxyViewModel`: State management
- `ProxyUiState`: UI state data class

**Responsibilities**:
- Handle user interactions
- Display data và UI state
- Navigate between screens
- Manage lifecycle events

#### 5.1.2 Domain Layer
**Components**:
- `ProxyConfig`: Entity với domains field
- `ProxyStatus`: Status enumeration
- `ProxyType`: Type enumeration
- `ProxyRepository`: Interface definition
- Use cases: `StartProxyUseCase`, `StopProxyUseCase`, etc.

**Responsibilities**:
- Define business logic
- Contain enterprise business rules
- Define data contracts
- Independent of external concerns

#### 5.1.3 Data Layer
**Components**:
- `ProxyRepositoryImpl`: Repository implementation
- `ProxyLocalDataSource`: Local data access
- `ProxyRemoteDataSource`: Remote data access
- `ProxyEntity`: Database entity
- `ProxyDao`: Database access object
- `ProxyMapper`: Data transformation

**Responsibilities**:
- Implement data access
- Handle data persistence
- Manage external data sources
- Transform data formats

#### 5.1.4 Infrastructure Layer
**Components**:
- `ProxyService`: Android foreground service
- `ProxyManager`: System-level operations
- `NetworkManager`: Network utilities
- `ValidationUtils`: Input validation
- `EncryptionUtils`: Data encryption

**Responsibilities**:
- Interact với Android system
- Handle system-level operations
- Provide utility functions
- Manage external dependencies

### 5.2 SOLID Principles Implementation

#### 5.2.1 Single Responsibility Principle (SRP)
- **ProxyViewModel**: Chỉ manage UI state
- **ProxyService**: Chỉ handle background service
- **ProxyManager**: Chỉ manage proxy connections
- **Each UseCase**: Chỉ handle một business operation

#### 5.2.2 Open/Closed Principle (OCP)
- **BaseUseCase**: Abstract class extensible cho new use cases
- **ProxyRepository**: Interface có thể extend
- **ProxyManager**: Interface có thể implement cho different types

#### 5.2.3 Liskov Substitution Principle (LSP)
- **Repository implementations**: Interchangeable
- **UseCase implementations**: Substitutable
- **DataSource implementations**: Replaceable

#### 5.2.4 Interface Segregation Principle (ISP)
- **Specific interfaces**: Separated by functionality
- **Small interfaces**: Focused on specific operations
- **No fat interfaces**: Avoid unnecessary methods

#### 5.2.5 Dependency Inversion Principle (DIP)
- **High-level modules**: Không depend on low-level modules
- **Abstractions**: Both depend on abstractions
- **Dependency Injection**: Sử dụng Hilt framework

---

## 6. Yêu Cầu Dữ Liệu

### 6.1 Logical Data Model

#### 6.1.1 ProxyConfig Entity
```kotlin
data class ProxyConfig(
    val id: Long = 0,
    val serverAddress: String,
    val serverPort: Int,
    val username: String,
    val password: String,
    val proxyType: ProxyType,
    val domains: List<String>,
    val isEnabled: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
```

#### 6.1.2 ProxyStatus Entity
```kotlin
data class ProxyStatus(
    val isConnected: Boolean,
    val connectionTime: Long?,
    val lastError: String?,
    val bytesTransferred: Long,
    val activeConnections: Int
)
```

#### 6.1.3 Domain Entity
```kotlin
data class DomainRule(
    val id: Long = 0,
    val domain: String,
    val isWildcard: Boolean = false,
    val isEnabled: Boolean = true,
    val configId: Long
)
```

### 6.2 Data Storage Requirements

#### 6.2.1 Database Schema
**ProxyConfig Table**:
- `id`: Primary key
- `server_address`: TEXT NOT NULL
- `server_port`: INTEGER NOT NULL
- `username`: TEXT NOT NULL
- `password`: TEXT NOT NULL (encrypted)
- `proxy_type`: TEXT NOT NULL
- `domains`: TEXT NOT NULL (comma-separated)
- `is_enabled`: INTEGER NOT NULL
- `created_at`: INTEGER NOT NULL
- `updated_at`: INTEGER NOT NULL

**ProxyStatus Table**:
- `id`: Primary key
- `config_id`: Foreign key
- `is_connected`: INTEGER NOT NULL
- `connection_time`: INTEGER
- `last_error`: TEXT
- `bytes_transferred`: INTEGER
- `active_connections`: INTEGER
- `timestamp`: INTEGER NOT NULL

#### 6.2.2 Data Validation Rules
- **IP Address**: Valid IPv4 or IPv6 format
- **Port**: Range 1-65535
- **Username**: Not empty, max 255 characters
- **Password**: Not empty, max 255 characters
- **Domains**: Valid domain format, comma-separated
- **Proxy Type**: Must be SOCKS5 or HTTP

#### 6.2.3 Data Security
- **Encryption**: AES-256 cho password fields
- **Key Management**: Android Keystore
- **Access Control**: App-specific storage only
- **Backup**: Exclude sensitive data from backup

---

## 7. Thiết Kế Giao Diện

### 7.1 Material Design 3 Implementation

#### 7.1.1 Design System
**Color Palette**:
- **Primary**: Dynamic color from system (Android 12+)
- **Secondary**: Complementary colors
- **Surface**: Background colors
- **Error**: Error indication colors
- **Support**: Dark and light theme variants

**Typography**:
- **Display**: Large headings
- **Headline**: Section headings
- **Title**: Card titles
- **Body**: Regular text
- **Label**: Form labels

**Components**:
- **OutlinedTextField**: Input fields
- **FilledButton**: Primary actions
- **OutlinedButton**: Secondary actions
- **Switch**: Toggle controls
- **Card**: Content grouping
- **SnackBar**: Feedback messages

#### 7.1.2 Layout Design
**Main Screen Layout**:
- **Top App Bar**: Title và navigation
- **Content Area**: Configuration form
- **FAB**: Quick enable/disable
- **Bottom Navigation**: Screen navigation

**Form Layout**:
- **Input Groups**: Logical grouping
- **Validation**: Inline error messages
- **Help Text**: Contextual information
- **Actions**: Save và test buttons

#### 7.1.3 Responsive Design
**Phone Portrait**:
- Single column layout
- Full-width components
- Vertical navigation

**Phone Landscape**:
- Optimized for horizontal space
- Adjusted component sizes
- Horizontal navigation

**Tablet**:
- Two-column layout
- Larger components
- Enhanced navigation

### 7.2 User Experience Design

#### 7.2.1 Navigation Flow
1. **Launch App** → Main Configuration Screen
2. **Configure Proxy** → Save Configuration
3. **Enable Proxy** → Background Service starts
4. **Quick Settings** → Toggle from notification panel
5. **Settings** → Application preferences

#### 7.2.2 Error Handling UX
- **Validation Errors**: Real-time field validation
- **Network Errors**: Clear error messages
- **Connection Failures**: Retry mechanisms
- **Timeout Errors**: Appropriate feedback

#### 7.2.3 Loading States
- **Form Submission**: Loading button states
- **Connection Testing**: Progress indicators
- **Data Loading**: Skeleton screens
- **Background Operations**: Notification updates

---

## 8. Yêu Cầu Hiệu Suất

### 8.1 Response Time Requirements
- **UI Interactions**: < 100ms response time
- **Form Validation**: < 50ms validation time
- **Proxy Connection**: < 5 seconds establishment
- **Domain Lookup**: < 10ms cache hit, < 100ms cache miss

### 8.2 Throughput Requirements
- **Concurrent Connections**: Support 100+ simultaneous
- **Data Transfer**: Minimal overhead < 5%
- **Memory Usage**: < 50MB under normal operation
- **Battery Usage**: Minimal impact on battery life

### 8.3 Scalability Requirements
- **Domain Whitelist**: Support up to 1000 domains
- **Configuration Storage**: Multiple proxy configs
- **Historical Data**: 30 days of connection logs
- **Cache Management**: Efficient memory usage

---

## 9. Yêu Cầu Bảo Mật

### 9.1 Authentication Security
- **Credential Storage**: AES-256 encryption
- **Key Management**: Android Keystore
- **Session Management**: Secure session handling
- **Password Policy**: Strong password recommendations

### 9.2 Network Security
- **Proxy Communication**: Encrypted tunnels
- **Certificate Validation**: Proper SSL/TLS validation
- **Man-in-the-Middle**: Protection mechanisms
- **DNS Security**: Secure DNS resolution

### 9.3 Application Security
- **Code Obfuscation**: R8 optimization
- **Root Detection**: Optional security measures
- **Debug Protection**: Release build security
- **Backup Security**: Exclude sensitive data

---

## 10. Yêu Cầu Kiểm Thử

### 10.1 Unit Testing Requirements
**Test Coverage**: Minimum 80% code coverage
**Test Categories**:
- **Domain Logic**: Use cases và business logic
- **Data Layer**: Repository implementations
- **Validation**: Input validation functions
- **Utilities**: Helper functions và extensions

### 10.2 Integration Testing Requirements
**Test Scenarios**:
- **Database Integration**: Room database operations
- **Network Integration**: Proxy connection testing
- **Service Integration**: Background service operations
- **UI Integration**: Compose UI interactions

### 10.3 System Testing Requirements
**Test Cases**:
- **End-to-End Flows**: Complete user workflows
- **Error Scenarios**: Network failures, invalid inputs
- **Performance Testing**: Load và stress testing
- **Security Testing**: Penetration testing

### 10.4 User Acceptance Testing
**Test Criteria**:
- **Functional Requirements**: All features working
- **Usability Testing**: User experience validation
- **Performance Acceptance**: Meeting performance targets
- **Security Validation**: Security requirements met

---

## 11. Deployment và Maintenance

### 11.1 Deployment Requirements
**Build Configuration**:
- **Target SDK**: 34 (Android 14)
- **Minimum SDK**: 29 (Android 10)
- **Build Tools**: Android Gradle Plugin 8.0+
- **Signing**: Release signing configuration

**Distribution**:
- **Google Play Store**: Primary distribution channel
- **APK Direct**: Direct APK installation
- **Enterprise Distribution**: Corporate deployment

### 11.2 Maintenance Requirements
**Updates**:
- **Security Patches**: Monthly security updates
- **Feature Updates**: Quarterly feature releases
- **Bug Fixes**: Immediate critical bug fixes
- **Dependencies**: Regular dependency updates

**Monitoring**:
- **Crash Reporting**: Firebase Crashlytics
- **Analytics**: Usage analytics và performance
- **User Feedback**: In-app feedback mechanism
- **Performance Monitoring**: APM tools

### 11.3 Support Requirements
**Documentation**:
- **User Manual**: Complete user guide
- **API Documentation**: Developer documentation
- **Troubleshooting**: Common issues guide
- **FAQ**: Frequently asked questions

**Support Channels**:
- **Email Support**: Technical support email
- **Community Forum**: User community
- **Knowledge Base**: Self-service documentation
- **Video Tutorials**: Visual learning resources

---

## 12. Phụ Lục

### 12.1 Glossary
- **Proxy**: Intermediate server between client and internet
- **SOCKS5**: Socket Secure protocol version 5
- **Authentication**: Process of verifying user credentials
- **Whitelist**: List of allowed domains
- **Clean Architecture**: Software architecture pattern
- **SOLID**: Set of design principles
- **Jetpack Compose**: Modern UI toolkit for Android

### 12.2 References
- [Android Developer Documentation](https://developer.android.com)
- [Material Design 3](https://m3.material.io)
- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [SOLID Principles](https://en.wikipedia.org/wiki/SOLID)

### 12.3 Revision History
| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0 | 2025-07-13 | Development Team | Initial SRS document |

---

**Document Status**: Draft  
**Next Review Date**: 2025-08-13  
**Approved By**: Product Manager  
**Distribution**: Development Team, QA Team, Stakeholders