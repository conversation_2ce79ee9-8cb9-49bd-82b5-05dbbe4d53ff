# API Documentation

## Domain Layer

### Entities

#### ProxyConfig
```kotlin
/**
 * Domain entity representing proxy configuration
 * Contains all necessary information for proxy setup including domain whitelist
 * 
 * @property id Unique identifier for the proxy configuration
 * @property name Human-readable name for the proxy configuration
 * @property type Type of proxy (HTTP, HTTPS, SOCKS)
 * @property serverHost Proxy server hostname or IP address
 * @property serverPort Proxy server port number (1-65535)
 * @property username Optional username for proxy authentication
 * @property password Optional password for proxy authentication
 * @property allowedDomains List of domains that should use this proxy (empty = all domains)
 * @property isEnabled Whether this proxy configuration is currently enabled
 * @property createdAt Timestamp when configuration was created
 * @property updatedAt Timestamp when configuration was last updated
 */
@Parcelize
data class ProxyConfig(...)
```

#### ProxyStatus
```kotlin
/**
 * Represents the current status of the proxy connection
 * 
 * @property CONNECTED Proxy is successfully connected and routing traffic
 * @property CONNECTING Proxy is in the process of establishing connection
 * @property DISCONNECTED Proxy is not active
 * @property ERROR Proxy encountered an error and cannot connect
 */
enum class ProxyStatus { CONNECTED, CONNECTING, DISCONNECTED, ERROR }
```

#### ProxyType
```kotlin
/**
 * Supported proxy protocol types
 * 
 * @property HTTP Standard HTTP proxy for web traffic
 * @property HTTPS Secure HTTPS proxy with SSL/TLS encryption
 * @property SOCKS SOCKS proxy for general TCP/UDP traffic
 */
enum class ProxyType { HTTP, HTTPS, SOCKS }
```

### Use Cases

#### StartProxyUseCase
```kotlin
/**
 * Use case for starting a proxy connection
 * Validates configuration and initiates proxy service
 * 
 * @param proxyRepository Repository for proxy operations
 * @param validateProxyConfigUseCase Use case for validating proxy configuration
 * 
 * @throws IllegalArgumentException if proxy configuration is invalid
 * @throws RuntimeException if proxy service fails to start
 * 
 * @return Flow<Boolean> indicating success/failure of proxy startup
 */
class StartProxyUseCase(...)
```

#### StopProxyUseCase
```kotlin
/**
 * Use case for stopping the active proxy connection
 * Gracefully shuts down proxy service and clears configuration
 * 
 * @param proxyRepository Repository for proxy operations
 * 
 * @return Flow<Boolean> indicating success/failure of proxy shutdown
 */
class StopProxyUseCase(...)
```

#### ValidateProxyConfigUseCase
```kotlin
/**
 * Use case for validating proxy configuration parameters
 * Ensures all required fields are present and valid
 * 
 * @param config ProxyConfig to validate
 * 
 * @return Boolean true if configuration is valid, false otherwise
 * 
 * Validation rules:
 * - serverHost must not be empty or blank
 * - serverPort must be between 1 and 65535
 * - credentials must be both empty or both non-empty
 * - allowedDomains must contain valid domain names
 */
class ValidateProxyConfigUseCase(...)
```

## Data Layer

### Database

#### ProxyConfigDao
```kotlin
/**
 * Data Access Object for proxy configuration database operations
 * Provides CRUD operations and queries for proxy configurations
 */
@Dao
interface ProxyConfigDao {
    
    /**
     * Get all proxy configurations
     * @return Flow<List<ProxyConfigEntity>> live list of all configurations
     */
    @Query("SELECT * FROM proxy_configs ORDER BY updatedAt DESC")
    fun getAll(): Flow<List<ProxyConfigEntity>>
    
    /**
     * Get proxy configuration by ID
     * @param id Configuration ID
     * @return ProxyConfigEntity? configuration or null if not found
     */
    @Query("SELECT * FROM proxy_configs WHERE id = :id")
    suspend fun getById(id: Long): ProxyConfigEntity?
    
    /**
     * Get all enabled proxy configurations
     * @return Flow<List<ProxyConfigEntity>> live list of enabled configurations
     */
    @Query("SELECT * FROM proxy_configs WHERE isEnabled = 1")
    fun getEnabled(): Flow<List<ProxyConfigEntity>>
    
    /**
     * Insert new proxy configuration
     * @param config Configuration to insert
     * @return Long ID of inserted configuration
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(config: ProxyConfigEntity): Long
    
    /**
     * Update existing proxy configuration
     * @param config Configuration to update
     */
    @Update
    suspend fun update(config: ProxyConfigEntity)
    
    /**
     * Delete proxy configuration
     * @param config Configuration to delete
     */
    @Delete
    suspend fun delete(config: ProxyConfigEntity)
    
    /**
     * Delete all proxy configurations
     */
    @Query("DELETE FROM proxy_configs")
    suspend fun deleteAll()
}
```

### Repositories

#### ProxyRepositoryImpl
```kotlin
/**
 * Implementation of ProxyRepository interface
 * Coordinates between data sources and business logic
 * 
 * @param proxyConfigDao Database access object for proxy configurations
 * @param proxyConfigMapper Mapper between domain and data entities
 * @param sharedPreferences Preferences for storing current configuration
 * @param encryptionUtils Utility for encrypting sensitive data
 */
class ProxyRepositoryImpl(...)
```

## Infrastructure Layer

### Services

#### ProxyService
```kotlin
/**
 * Foreground service that manages proxy connections
 * Handles VPN service lifecycle and network traffic routing
 * 
 * Key responsibilities:
 * - Establish and maintain proxy connections
 * - Route network traffic through proxy
 * - Display persistent notification
 * - Handle service lifecycle events
 * 
 * @param proxyManager Manager for proxy connection logic
 * @param notificationManager Manager for service notifications
 */
@AndroidEntryPoint
class ProxyService : VpnService()
```

#### ProxyVpnService
```kotlin
/**
 * VPN service implementation for intercepting network traffic
 * Creates virtual network interface and routes traffic through proxy
 * 
 * Key features:
 * - Packet capture and forwarding
 * - Domain-based filtering
 * - Traffic statistics
 * - Connection management
 */
@AndroidEntryPoint
class ProxyVpnService : VpnService()
```

### Managers

#### ProxyManager
```kotlin
/**
 * Core manager for proxy connection operations
 * Handles connection establishment, maintenance, and teardown
 * 
 * @param networkUtils Utility for network operations
 * 
 * Key methods:
 * - startProxy(config: ProxyConfig): Boolean
 * - stopProxy(): Boolean
 * - getConnectionStatus(): ProxyStatus
 * - testConnection(config: ProxyConfig): Boolean
 */
class ProxyManager(...)
```

#### NetworkManager
```kotlin
/**
 * Manager for network state monitoring and operations
 * Tracks network connectivity and handles network changes
 * 
 * @param context Application context
 * @param networkUtils Utility for network operations
 * 
 * Key features:
 * - Network connectivity monitoring
 * - Network type detection (WiFi, Mobile, etc.)
 * - Connection quality assessment
 * - Network change callbacks
 */
class NetworkManager(...)
```

#### ProxyNotificationManager
```kotlin
/**
 * Manager for handling notifications in the proxy application
 * Manages foreground service notifications and status updates
 * 
 * @param context Application context
 * 
 * Key methods:
 * - createServiceNotification(status, host, port): Notification
 * - updateNotification(status, host, port): Unit
 * - showStatusNotification(title, message): Unit
 * - cancelAllNotifications(): Unit
 */
class ProxyNotificationManager(...)
```

### Utilities

#### ValidationUtils
```kotlin
/**
 * Utility class for input validation operations
 * Provides validation methods for proxy configuration parameters
 * 
 * Key methods:
 * - isValidHost(host: String): Boolean
 * - isValidPort(port: Int): Boolean
 * - isValidDomain(domain: String): Boolean
 * - isValidCredentials(username: String, password: String): Boolean
 * - sanitizeInput(input: String): String
 */
class ValidationUtils
```

#### EncryptionUtils
```kotlin
/**
 * Utility class for encryption and decryption operations
 * Handles secure storage of sensitive data like passwords
 * 
 * @param context Application context for keystore access
 * 
 * Key methods:
 * - encrypt(data: String?): String?
 * - decrypt(encryptedData: String?): String?
 * - isEncrypted(data: String?): Boolean
 * - generateSecureKey(): String
 * - hashPassword(password: String): String
 */
class EncryptionUtils(...)
```

#### NetworkUtils
```kotlin
/**
 * Utility class for network-related operations
 * Provides network connectivity and configuration helpers
 * 
 * @param context Application context
 * 
 * Key methods:
 * - isNetworkAvailable(): Boolean
 * - getNetworkType(): NetworkType
 * - isWifiConnected(): Boolean
 * - isMobileConnected(): Boolean
 * - getConnectionQuality(): ConnectionQuality
 */
class NetworkUtils(...)
```

## Presentation Layer

### ViewModels

#### ProxyConfigViewModel
```kotlin
/**
 * ViewModel for proxy configuration screen
 * Manages UI state and handles user interactions
 * 
 * @param getProxyConfigUseCase Use case for retrieving proxy configuration
 * @param saveProxyConfigUseCase Use case for saving proxy configuration
 * @param startProxyUseCase Use case for starting proxy
 * @param stopProxyUseCase Use case for stopping proxy
 * @param validateProxyConfigUseCase Use case for validating configuration
 * 
 * Key state:
 * - uiState: StateFlow<ProxyUiState> current UI state
 * - isLoading: StateFlow<Boolean> loading indicator
 * - errorMessage: StateFlow<String?> error messages
 */
@HiltViewModel
class ProxyConfigViewModel(...)
```

### UI State

#### ProxyUiState
```kotlin
/**
 * UI state for proxy configuration screen
 * Represents current state of the proxy configuration interface
 * 
 * @property currentConfig Current proxy configuration
 * @property status Current proxy connection status
 * @property isLoading Whether operation is in progress
 * @property errorMessage Error message to display (null if no error)
 */
data class ProxyUiState(...)
```

## Error Handling

### Common Exceptions

- `IllegalArgumentException` - Invalid configuration parameters
- `SecurityException` - Missing permissions or security violations
- `NetworkException` - Network connectivity issues
- `ProxyException` - Proxy-specific errors
- `DatabaseException` - Database operation failures

### Error Codes

- `ERROR_INVALID_CONFIG` - Configuration validation failed
- `ERROR_NETWORK_UNAVAILABLE` - No network connectivity
- `ERROR_PERMISSION_DENIED` - Required permissions not granted
- `ERROR_PROXY_UNREACHABLE` - Cannot connect to proxy server
- `ERROR_AUTHENTICATION_FAILED` - Proxy authentication failed
- `ERROR_SERVICE_UNAVAILABLE` - Proxy service cannot start
