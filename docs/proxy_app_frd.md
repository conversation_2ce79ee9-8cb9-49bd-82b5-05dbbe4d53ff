# Tài Liệu Yêu <PERSON> (FRD)
## Ứng Dụng Android Proxy với Authentication và Domain-Based Routing

### Thông Tin Dự Án
- **Tên D<PERSON> Án**: Android Proxy Authentication App
- **Phiê<PERSON>**: 1.0.0
- **<PERSON><PERSON><PERSON>**: 13/07/2025
- **N<PERSON>n <PERSON>**: Android (Min SDK 29, Target SDK 34)
- **Công <PERSON>ệ**: <PERSON><PERSON><PERSON>, Jetpack Compose, Clean Architecture

---

## 1. TỔNG QUAN DỰ ÁN

### 1.1 Mục Tiêu Dự Án
Phát triển một ứng dụng Android có khả năng thiết lập proxy với authentication (SOCKS5 và HTTP) để giải quyết hạn chế của Android không hỗ trợ proxy authentication trong cài đặt WiFi mặc định. Ứng dụng sẽ chỉ route traffic qua proxy cho các domain cụ thể (domain whitelist) được người dùng định ng<PERSON>.

### 1.2 Phạm Vi Dự Án
- Giao diện người dùng hiện đại với Jetpack Compose
- Hỗ trợ SOCKS5 và HTTP proxy với authentication
- Domain-based routing (selective proxy)
- VPN Service để intercept và lọc traffic
- Quick Settings Tile integration
- Background service với notification
- Lưu trữ cấu hình an toàn
- Auto-start capability

### 1.3 Đối Tượng Người Dùng
- Người dùng Android cần sử dụng proxy với authentication
- Người dùng muốn chỉ route một số domain cụ thể qua proxy
- Người dùng trong môi trường doanh nghiệp với proxy server

---

## 2. YÊU CẦU CHỨC NĂNG

### 2.1 Yêu Cầu Chức Năng Chính

#### 2.1.1 Quản Lý Cấu Hình Proxy
**FR-001: Cấu Hình Proxy Server**
- **Mô tả**: Người dùng có thể nhập và lưu thông tin proxy server
- **Input**: IP Address, Port, Username, Password, Proxy Type (SOCKS5/HTTP)
- **Output**: Cấu hình được lưu trữ an toàn
- **Validation**: 
  - IP address format (IPv4/IPv6)
  - Port range (1-65535)
  - Username/Password không trống
  - Proxy type hợp lệ

**FR-002: Quản Lý Domain Whitelist**
- **Mô tả**: Người dùng có thể định nghĩa các domain sử dụng proxy
- **Input**: Danh sách domain phân tách bằng dấu phẩy (vd: example.com,google.com)
- **Output**: Domain list được parse và lưu trữ
- **Validation**:
  - Domain format hợp lệ (RFC 1035)
  - Không chứa khoảng trắng thừa
  - Phân tách bằng dấu phẩy
  - Không trống

**FR-003: Lưu Trữ Cấu Hình**
- **Mô tả**: Cấu hình proxy được lưu trữ bền vững và an toàn
- **Yêu cầu**:
  - Encrypt password trước khi lưu
  - Sử dụng Room Database
  - Auto-load khi khởi động app
  - Support backup/restore

#### 2.1.2 Điều Khiển Proxy

**FR-004: Bật/Tắt Proxy Service**
- **Mô tả**: Người dùng có thể bật/tắt proxy service
- **Yêu cầu**:
  - Toggle switch trong main screen
  - Visual feedback (loading states)
  - Error handling và thông báo
  - Persist state across app restarts

**FR-005: Test Kết Nối Proxy**
- **Mô tả**: Người dùng có thể test kết nối proxy trước khi kích hoạt
- **Yêu cầu**:
  - Test connection button
  - Progress indicator
  - Success/failure feedback
  - Detailed error messages

#### 2.1.3 VPN Service và Traffic Interception

**FR-006: VPN Service Implementation**
- **Mô tả**: Implement VPN service để intercept network traffic
- **Yêu cầu**:
  - Extend Android VpnService
  - Request VPN permission từ user
  - Handle VPN lifecycle properly
  - Maintain persistent connection

**FR-007: Domain-Based Traffic Routing**
- **Mô tả**: Chỉ route traffic của domain trong whitelist qua proxy
- **Yêu cầu**:
  - Parse SNI (Server Name Indication) từ TLS handshake
  - Extract Host header từ HTTP requests
  - Intercept DNS queries để resolve domain
  - Route decision based on domain match
  - Bypass proxy for non-matching domains

**FR-008: Packet Processing**
- **Mô tả**: Xử lý network packets để extract domain information
- **Yêu cầu**:
  - Parse IP packets
  - Extract TLS SNI information
  - Parse HTTP Host headers
  - Handle DNS resolution
  - Forward packets appropriately

#### 2.1.4 Background Service

**FR-009: Persistent Background Service**
- **Mô tả**: Service chạy nền để duy trì proxy connection
- **Yêu cầu**:
  - Foreground service với notification
  - Auto-restart capability
  - Battery optimization handling
  - Proper service lifecycle management

**FR-010: Service Notification**
- **Mô tả**: Hiển thị notification khi service đang chạy
- **Yêu cầu**:
  - Show proxy status (enabled/disabled)
  - Current configuration summary
  - Quick action buttons
  - Persistent notification

#### 2.1.5 Quick Settings Integration

**FR-011: Quick Settings Tile**
- **Mô tả**: Tile trong Quick Settings để toggle proxy
- **Yêu cầu**:
  - Extend TileService
  - Toggle proxy on/off
  - Show current status
  - Toast feedback

#### 2.1.6 Auto-Start Capability

**FR-012: Boot Receiver**
- **Mô tả**: Tự động khởi động proxy khi device boot (tùy chọn)
- **Yêu cầu**:
  - RECEIVE_BOOT_COMPLETED permission
  - User preference setting
  - Delayed start to avoid system load
  - Proper initialization sequence

### 2.2 Yêu Cầu Giao Diện Người Dùng

#### 2.2.1 Main Screen (Proxy Configuration)

**FR-013: Proxy Configuration Form**
- **Components**:
  - OutlinedTextField cho IP Address
  - OutlinedTextField cho Port
  - OutlinedTextField cho Username
  - OutlinedTextField cho Password (with visibility toggle)
  - OutlinedTextField cho Domains (comma-separated)
  - ExposedDropdownMenuBox cho Proxy Type
  - Switch để enable/disable proxy
  - Save Configuration Button
  - Test Connection Button
- **Validation**: Real-time validation với error messages
- **State Management**: Proper state hoisting và flow

**FR-014: Proxy Status Display**
- **Components**:
  - Status card hiển thị current state
  - Connection indicator
  - Active domain count
  - Traffic statistics (nếu có)
- **Updates**: Real-time status updates

#### 2.2.2 Settings Screen

**FR-015: Application Settings**
- **Components**:
  - Auto-start on boot toggle
  - Notification preferences
  - Theme selection (Light/Dark/Auto)
  - Export/Import configuration
  - Clear all data option
- **Persistence**: All settings saved to preferences

#### 2.2.3 About Screen

**FR-016: About Information**
- **Components**:
  - App version information
  - Developer information
  - License information
  - Privacy policy link
  - Open source libraries used

### 2.3 Yêu Cầu Bảo Mật

#### 2.3.1 Data Protection

**FR-017: Password Encryption**
- **Mô tả**: Encrypt passwords trước khi lưu trữ
- **Yêu cầu**:
  - Sử dụng Android Security Crypto
  - AES-256 encryption
  - Proper key management
  - Secure key storage

**FR-018: Input Validation**
- **Mô tả**: Validate tất cả user inputs
- **Yêu cầu**:
  - Sanitize all text inputs
  - Prevent injection attacks
  - Validate domain formats
  - Rate limiting for requests

#### 2.3.2 Network Security

**FR-019: Secure Communication**
- **Mô tả**: Đảm bảo communication với proxy server được bảo mật
- **Yêu cầu**:
  - Support TLS cho proxy connections
  - Certificate validation
  - Proper timeout handling
  - Connection retry logic

**FR-020: Privacy Protection**
- **Mô tả**: Bảo vệ privacy của user
- **Yêu cầu**:
  - Không log sensitive data
  - Minimal data collection
  - Secure DNS resolution
  - No analytics tracking

---

## 3. YÊU CẦU CÔNG NGHỆ

### 3.1 Kiến Trúc Hệ Thống

#### 3.1.1 Clean Architecture Implementation

**FR-021: Architecture Layers**
- **Presentation Layer**: Activities, Composables, ViewModels
- **Domain Layer**: Entities, Use Cases, Repository Interfaces
- **Data Layer**: Repository Implementations, Data Sources
- **Infrastructure Layer**: Services, Managers, Utils

**FR-022: SOLID Principles**
- **Single Responsibility**: Mỗi class có một responsibility duy nhất
- **Open/Closed**: Extensible design patterns
- **Liskov Substitution**: Proper inheritance hierarchy
- **Interface Segregation**: Focused interfaces
- **Dependency Inversion**: DI implementation với Hilt

#### 3.1.2 Dependency Injection

**FR-023: Hilt Configuration**
- **Modules**:
  - DatabaseModule: Room database configuration
  - NetworkModule: Network components
  - RepositoryModule: Repository bindings
  - UseCaseModule: Use case providers
- **Scopes**: Proper scope management (Singleton, ViewModelScoped)

### 3.2 Data Layer

#### 3.2.1 Local Database

**FR-024: Room Database Schema**
```kotlin
@Entity(tableName = "proxy_configs")
data class ProxyEntity(
    @PrimaryKey val id: Long = 0,
    val ipAddress: String,
    val port: Int,
    val username: String,
    val encryptedPassword: String,
    val proxyType: String,
    val domains: String, // comma-separated
    val isEnabled: Boolean,
    val createdAt: Long,
    val updatedAt: Long
)
```

**FR-025: DAO Operations**
- Insert/Update/Delete proxy configurations
- Query active configuration
- Backup/restore operations
- Migration support

#### 3.2.2 Data Sources

**FR-026: Local Data Source**
- **ProxyLocalDataSource**: Room database operations
- **Caching**: In-memory caching for frequently accessed data
- **Offline Support**: Full offline functionality

**FR-027: Remote Data Source** (Future expansion)
- **ProxyRemoteDataSource**: API calls for remote configurations
- **Sync**: Configuration synchronization
- **Cloud Backup**: Remote backup capability

### 3.3 Domain Layer

#### 3.3.1 Entities

**FR-028: Domain Entities**
```kotlin
data class ProxyConfig(
    val id: Long = 0,
    val ipAddress: String,
    val port: Int,
    val username: String,
    val password: String,
    val proxyType: ProxyType,
    val domains: List<String>,
    val isEnabled: Boolean
)

enum class ProxyType { SOCKS5, HTTP }

enum class ProxyStatus { CONNECTED, DISCONNECTED, CONNECTING, ERROR }
```

#### 3.3.2 Use Cases

**FR-029: Use Case Implementation**
- **StartProxyUseCase**: Start proxy service với domain filtering
- **StopProxyUseCase**: Stop proxy service
- **SaveProxyConfigUseCase**: Save configuration
- **GetProxyConfigUseCase**: Retrieve configuration
- **TestProxyConnectionUseCase**: Test proxy connectivity
- **ValidateConfigUseCase**: Validate configuration inputs

### 3.4 Presentation Layer

#### 3.4.1 Jetpack Compose UI

**FR-030: Compose Implementation**
- **State Management**: StateFlow/State cho reactive UI
- **Composable Design**: Stateless và reusable composables
- **Material 3**: Modern design system
- **Navigation**: Compose Navigation
- **Theming**: Dynamic color support

**FR-031: UI State Management**
```kotlin
data class ProxyUiState(
    val proxyConfig: ProxyConfig = ProxyConfig(),
    val isProxyEnabled: Boolean = false,
    val isLoading: Boolean = false,
    val isTestingConnection: Boolean = false,
    val error: String? = null,
    val validationErrors: Map<String, String> = emptyMap()
)
```

#### 3.4.2 ViewModels

**FR-032: ViewModel Implementation**
- **ProxyViewModel**: Main configuration screen
- **SettingsViewModel**: Settings management
- **State Management**: StateFlow cho UI state
- **Error Handling**: Comprehensive error handling

---

## 4. YÊU CẦU HIỆU SUẤT

### 4.1 Performance Requirements

**FR-033: UI Performance**
- **Target**: 60 FPS UI rendering
- **Compose**: Optimized recomposition
- **Memory**: Efficient memory usage
- **Battery**: Minimal battery impact

**FR-034: Network Performance**
- **Latency**: Minimal proxy latency overhead
- **Throughput**: Support high-speed connections
- **Packet Processing**: Efficient packet filtering
- **DNS Resolution**: Fast domain resolution

### 4.2 Resource Management

**FR-035: Memory Management**
- **Heap Size**: Efficient memory allocation
- **Garbage Collection**: Minimal GC pressure
- **Caching**: Intelligent caching strategies
- **Cleanup**: Proper resource cleanup

**FR-036: Battery Optimization**
- **Background**: Efficient background processing
- **Doze Mode**: Proper doze mode handling
- **Wake Locks**: Minimal wake lock usage
- **Network**: Efficient network usage

---

## 5. YÊU CẦU KIỂM THỬ

### 5.1 Unit Testing

**FR-037: Unit Test Coverage**
- **ViewModels**: Test all ViewModel functions
- **Use Cases**: Test business logic
- **Repositories**: Test data operations
- **Utilities**: Test validation và utility functions
- **Target Coverage**: >90% code coverage

### 5.2 Integration Testing

**FR-038: Integration Tests**
- **Database**: Room database operations
- **Network**: Proxy connection tests
- **VPN Service**: VPN functionality tests
- **UI**: Compose UI tests

### 5.3 Test Infrastructure

**FR-039: Testing Framework**
- **Unit Tests**: JUnit, Mockito, Coroutines Test
- **UI Tests**: Compose Test, Espresso
- **Mock Data**: Comprehensive mock data sets
- **Test Utilities**: Reusable test utilities

---

## 6. YÊU CẦU TRIỂN KHAI

### 6.1 Build Configuration

**FR-040: Build Setup**
- **Target SDK**: 34 (Android 14)
- **Min SDK**: 29 (Android 10)
- **Build Tools**: Gradle 8.x
- **Proguard**: R8 optimization
- **Signing**: Release signing configuration

### 6.2 Permissions

**FR-041: Required Permissions**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
<uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
```

### 6.3 Dependencies

**FR-042: Core Dependencies**
- **Compose BOM**: 2025.06.00
- **Hilt**: 2.51.1
- **Room**: 2.6.1
- **Coroutines**: 1.8.1
- **OkHttp**: 4.12.0
- **Security Crypto**: 1.1.0-alpha06

---

## 7. YÊU CẦU BẢO TRÌ

### 7.1 Code Quality

**FR-043: Code Standards**
- **Kotlin Conventions**: Idiomatic Kotlin code
- **Documentation**: KDoc cho all public APIs
- **Linting**: Ktlint và detekt integration
- **Code Review**: Mandatory code reviews

### 7.2 Logging và Debugging

**FR-044: Logging System**
- **Structured Logging**: Proper log levels
- **Debug Information**: Comprehensive debug logs
- **Error Tracking**: Error reporting system
- **Performance Monitoring**: Performance metrics

### 7.3 Configuration Management

**FR-045: Configuration**
- **Build Variants**: Debug/Release configurations
- **Feature Flags**: Runtime feature toggling
- **Environment**: Multiple environment support
- **Versioning**: Semantic versioning

---

## 8. YÊU CẦU TƯƠNG LAI

### 8.1 Extensibility

**FR-046: Future Enhancements**
- **Multiple Proxy Support**: Support multiple proxy servers
- **Profile Management**: Multiple configuration profiles
- **Advanced Filtering**: Content-based filtering
- **Statistics**: Detailed usage statistics

### 8.2 Platform Support

**FR-047: Platform Expansion**
- **Android Auto**: Android Auto integration
- **Wear OS**: Wear OS companion app
- **Android TV**: Android TV support
- **Foldable**: Foldable device optimization

---

## 9. ĐỊNH NGHĨA THUẬT NGỮ

**Domain Whitelist**: Danh sách các domain được phép sử dụng proxy

**SNI (Server Name Indication)**: Extension của TLS protocol cho phép client chỉ định hostname

**VPN Service**: Android service cho phép ứng dụng tạo VPN connection

**Clean Architecture**: Kiến trúc phần mềm tách biệt concerns thành các layer

**SOLID Principles**: Năm nguyên tắc thiết kế object-oriented programming

**Jetpack Compose**: Modern UI toolkit cho Android development

**Material 3**: Google's design system cho Android applications

---

## 10. PHỤ LỤC

### 10.1 Tài Liệu Tham Khảo

1. Android VPN Service Documentation
2. Jetpack Compose Documentation
3. Clean Architecture Guidelines
4. SOLID Principles Best Practices
5. Kotlin Coding Conventions
6. Material 3 Design Guidelines

### 10.2 Constraints và Limitations

1. **VPN Permission**: Requires user approval cho VPN access
2. **Battery Optimization**: May be affected by battery optimization settings
3. **Network Performance**: Proxy routing có thể ảnh hưởng performance
4. **Device Compatibility**: Một số devices có thể có restrictions
5. **Root Access**: Không require root access, sử dụng VPN API

### 10.3 Risk Assessment

1. **Technical Risk**: VPN implementation complexity
2. **Security Risk**: Credential storage và transmission
3. **Performance Risk**: Network latency và battery usage
4. **Compatibility Risk**: Device và Android version compatibility
5. **User Experience Risk**: VPN permission request có thể confuse users

---

**Ngày cập nhật cuối**: 13/07/2025  
**Phiên bản tài liệu**: 1.0.0  
**Trạng thái**: Approved for Development