# Mô tả chi tiết để tạo ứng dụng Android Proxy với Authentication (Kotlin + Jetpack Compose)

## Mô tả dự án
Tôi cần tạo một ứng dụng Android có thể thiết lập proxy với authentication (SOCKS5 và HTTP) vì thiết bị Android của tôi không hỗ trợ proxy authentication trong cài đặt WiFi mặc định. Ứng dụng chỉ sử dụng proxy cho một số domain cụ thể (domain whitelist), cho phép người dùng định nghĩa các domain này để route traffic qua proxy, trong khi traffic khác đi trực tiếp.

**<PERSON><PERSON><PERSON> c<PERSON><PERSON> kỹ thuật:**
- **Ngôn ngữ:** Kotlin
- **UI Framework:** Jetpack Compose
- **Architecture:** Clean Architecture + MVVM
- **Principles:** SOLID Principles

## <PERSON><PERSON>u c<PERSON>u chức năng chính

### 1. <PERSON><PERSON><PERSON> di<PERSON>n người dùng (Jetpack Compose)
- **Main Screen Composables:**
  - ProxyConfigScreen với Material 3 Design
  - OutlinedTextField cho IP address
  - OutlinedTextField cho Port
  - OutlinedTextField cho Username
  - OutlinedTextField cho Password (with password visibility toggle)
  - OutlinedTextField cho Domains (comma-separated, ví dụ: example.com,another.com - để chỉ định các domain cụ thể sử dụng proxy)
  - DropdownMenu/ExposedDropdownMenuBox để chọn loại proxy (SOCKS5, HTTP)
  - Switch để bật/tắt proxy
  - Button để lưu cấu hình với loading state
  - Button để test kết nối với progress indicator
- **UI State Management:**
  - ProxyUiState data class
  - Error handling với SnackBar
  - Loading states với CircularProgressIndicator
  - Form validation với real-time feedback (bao gồm validation cho domains: không trống, format hợp lệ, phân tách bằng dấu phẩy)

### 2. Chức năng proxy
- Hỗ trợ SOCKS5 proxy với authentication
- Hỗ trợ HTTP proxy với authentication
- Chỉ route traffic qua proxy cho các domain cụ thể (whitelist) được người dùng nhập, phân tách bằng dấu phẩy; traffic khác đi trực tiếp
- Lưu trữ cấu hình proxy an toàn (SharedPreferences hoặc Room Database), bao gồm list domains
- Validate input (IP format, port range, domains format, etc.)

### 3. Quick Settings Tile
- Tạo Quick Settings Tile để bật/tắt proxy nhanh từ notification panel
- Hiển thị trạng thái proxy hiện tại
- Chuyển đổi nhanh giữa bật/tắt

### 4. Dịch vụ nền (Background Service)
- Service để duy trì kết nối proxy
- Notification hiển thị trạng thái proxy
- Auto-start khi device boot (tùy chọn)
- Logic để kiểm tra domain trước khi route qua proxy (sử dụng domain matching để quyết định có sử dụng proxy hay không)

## Cấu trúc dự án yêu cầu - Clean Architecture (Kotlin)

```
app/
├── src/main/kotlin/com/yourpackage/proxyapp/
│   ├── presentation/
│   │   ├── ui/
│   │   │   ├── MainActivity.kt
│   │   │   ├── screens/
│   │   │   │   ├── ProxyConfigScreen.kt
│   │   │   │   ├── SettingsScreen.kt
│   │   │   │   └── AboutScreen.kt
│   │   │   ├── components/
│   │   │   │   ├── ProxyConfigForm.kt
│   │   │   │   ├── ProxyStatusCard.kt
│   │   │   │   └── LoadingButton.kt
│   │   │   ├── theme/
│   │   │   │   ├── Theme.kt
│   │   │   │   ├── Color.kt
│   │   │   │   └── Typography.kt
│   │   │   └── navigation/
│   │   │       └── ProxyNavigation.kt
│   │   ├── viewmodel/
│   │   │   ├── ProxyViewModel.kt
│   │   │   └── SettingsViewModel.kt
│   │   ├── state/
│   │   │   ├── ProxyUiState.kt
│   │   │   └── SettingsUiState.kt
│   │   └── quicksettings/
│   │       └── ProxyQuickSettingsTile.kt
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── ProxyConfig.kt  // Thêm field cho list<String> domains
│   │   │   ├── ProxyStatus.kt
│   │   │   └── ProxyType.kt
│   │   ├── usecases/
│   │   │   ├── StartProxyUseCase.kt
│   │   │   ├── StopProxyUseCase.kt
│   │   │   ├── SaveProxyConfigUseCase.kt
│   │   │   ├── GetProxyConfigUseCase.kt
│   │   │   ├── TestProxyConnectionUseCase.kt
│   │   │   └── base/
│   │   │       └── BaseUseCase.kt
│   │   └── repositories/
│   │       └── ProxyRepository.kt
│   ├── data/
│   │   ├── repositories/
│   │   │   └── ProxyRepositoryImpl.kt
│   │   ├── datasources/
│   │   │   ├── local/
│   │   │   │   ├── ProxyLocalDataSource.kt
│   │   │   │   ├── ProxyDatabase.kt
│   │   │   │   ├── ProxyDao.kt
│   │   │   │   └── ProxyEntity.kt  // Thêm field cho domains (lưu dưới dạng string comma-separated)
│   │   │   └── ProxyRemoteDataSource.kt
│   │   │   └── remote/
│   │   │   │       └── ProxyRemoteDataSource.kt
│   │   └── mappers/
│   │       └── ProxyMapper.kt  // Cập nhật mapping cho domains
│   ├── infrastructure/
│   │   ├── services/
│   │   │   └── ProxyService.kt  // Thêm logic để kiểm tra domain
│   │   ├── managers/
│   │   │   ├── ProxyManager.kt  // Thêm method để check domain match
│   │   │   │   └── NetworkManager.kt
│   │   └── utils/
│   │   │   ├── ValidationUtils.kt  // Thêm validation cho domains
│   │   │   ├── EncryptionUtils.kt
│   │   │   └── NetworkUtils.kt
│   └── di/
│       ├── DatabaseModule.kt
│       ├── NetworkModule.kt
│       ├── RepositoryModule.kt
│       └── UseCaseModule.kt
├── res/
│   ├── values/
│   │   ├── strings.xml
│   │   └── colors.xml
│   └── drawable/
│       └── ic_proxy.xml
└── AndroidManifest.xml
```

## Yêu cầu kỹ thuật

### Target Platform:
- **Min SDK: 29 (Android 10)**
- **Target SDK: 34 (Android 14)**
- **Compile SDK: 34**

### Permissions cần thiết:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
```

### Dependencies (build.gradle.kts):
```kotlin
// Core Android
implementation("androidx.core:core-ktx:1.13.1")
implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.4")
implementation("androidx.activity:activity-compose:1.9.1")

// Compose BOM - sử dụng BOM để quản lý tất cả các phiên bản Compose
implementation(platform("androidx.compose:compose-bom:2025.06.00"))
implementation("androidx.compose.ui:ui")
implementation("androidx.compose.ui:ui-tooling-preview")
implementation("androidx.compose.material3:material3")
implementation("androidx.compose.material:material-icons-extended")

// Lifecycle & ViewModel
implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.8.4")
implementation("androidx.lifecycle:lifecycle-runtime-compose:2.8.4")

// Navigation
implementation("androidx.navigation:navigation-compose:2.7.7")

// Room Database
implementation("androidx.room:room-runtime:2.6.1")
implementation("androidx.room:room-ktx:2.6.1")
kapt("androidx.room:room-compiler:2.6.1")

// Hilt Dependency Injection
implementation("com.google.dagger:hilt-android:2.51.1")
kapt("com.google.dagger:hilt-compiler:2.51.1")
implementation("androidx.hilt:hilt-navigation-compose:1.2.0")

// Coroutines
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1")

// Networking (cho proxy functionality)
implementation("com.squareup.okhttp3:okhttp:4.12.0")
implementation("com.squareup.retrofit2:retrofit:2.11.0")
implementation("com.squareup.retrofit2:converter-gson:2.11.0")

// Security & Encryption
implementation("androidx.security:security-crypto:1.1.0-alpha06")

// WorkManager (cho background tasks)
implementation("androidx.work:work-runtime-ktx:2.9.1")
implementation("androidx.hilt:hilt-work:1.2.0")

// Testing
testImplementation("junit:junit:4.13.2")
testImplementation("androidx.test.ext:junit:1.2.1")
testImplementation("androidx.test.espresso:espresso-core:3.6.1")
testImplementation("androidx.compose.ui:ui-test-junit4")
testImplementation("org.mockito:mockito-core:5.12.0")
testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.1")
testImplementation("app.cash.turbine:turbine:1.1.0")
```

## Nguyên lý SOLID Implementation

### 1. Single Responsibility Principle (SRP)
- **ProxyViewModel**: Chỉ quản lý UI state và communication với domain layer
- **ProxyService**: Chỉ quản lý background proxy service (bao gồm domain checking)
- **ProxyManager**: Chỉ quản lý proxy connection logic và domain matching
- **ProxyRepository**: Chỉ quản lý data access abstraction
- **UseCase classes**: Mỗi use case chỉ thực hiện một business logic cụ thể

### 2. Open/Closed Principle (OCP)
- **BaseUseCase**: Abstract class cho tất cả use cases, extensible cho new use cases
- **ProxyRepository**: Interface có thể extend cho different data sources
- **ProxyManager**: Interface có thể implement cho different proxy types và domain logic

### 3. Liskov Substitution Principle (LSP)
- **ProxyRepository implementations**: ProxyRepositoryImpl có thể thay thế interface
- **BaseUseCase implementations**: Tất cả use cases có thể thay thế base class
- **DataSource implementations**: Local/Remote data sources interchangeable

### 4. Interface Segregation Principle (ISP)
- **ProxyRepository**: Chia thành smaller, specific interfaces
- **ProxyLocalDataSource**: Chỉ local data operations
- **ProxyRemoteDataSource**: Chỉ remote data operations
- **ProxyManager**: Separate interfaces cho different proxy operations và domain matching

### 5. Dependency Inversion Principle (DIP)
- **Dependency Injection**: Sử dụng Hilt cho DI
- **Repository Pattern**: Domain layer không depend vào data layer implementations
- **Use Cases**: Depend vào abstractions, not concrete implementations

## Clean Architecture Layers

### 1. Presentation Layer
- **Activities/Fragments**: UI components
- **ViewModels**: Handle UI logic và state management
- **Adapters**: RecyclerView adapters nếu cần

### 2. Domain Layer (Business Logic)
- **Entities**: Core business objects (ProxyConfig với thêm domains: List<String>, ProxyStatus)
- **Use Cases**: Business logic operations
- **Repository Interfaces**: Data access contracts

### 3. Data Layer
- **Repository Implementations**: Concrete data access implementations
- **Data Sources**: Local (Room) và Remote data sources
- **Mappers**: Convert between data models và domain entities (xử lý domains comma-separated to list)

### 4. Infrastructure Layer
- **Services**: Android services (ProxyService với domain checking)
- **Managers**: System-level operations (ProxyManager với domain matching logic)
- **Utils**: Utility classes (ValidationUtils với domain validation)

## Logic xử lý chi tiết

### 1. Use Cases Implementation (Kotlin)
```kotlin
// BaseUseCase.kt
abstract class BaseUseCase<T, P> {
    abstract suspend fun execute(params: P): Flow<Result<T>>
    
    operator fun invoke(params: P): Flow<Result<T>> = execute(params)
}

// StartProxyUseCase.kt
@Singleton
class StartProxyUseCase @Inject constructor(
    private val repository: ProxyRepository
) : BaseUseCase<Boolean, ProxyConfig>() {
    override suspend fun execute(params: ProxyConfig): Flow<Result<Boolean>> = 
        repository.startProxy(params)  // Params bao gồm domains
}
```

### 2. Repository Pattern (Kotlin)
```kotlin
// ProxyRepository.kt (Domain Interface)
interface ProxyRepository {
    suspend fun getProxyConfig(): Flow<Result<ProxyConfig>>
    suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>
    suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>
    suspend fun stopProxy(): Flow<Result<Boolean>>
    suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>
}

// ProxyRepositoryImpl.kt (Data Implementation)
@Singleton
class ProxyRepositoryImpl @Inject constructor(
    private val localDataSource: ProxyLocalDataSource,
    private val remoteDataSource: ProxyRemoteDataSource,
    private val mapper: ProxyMapper
) : ProxyRepository {
    override suspend fun getProxyConfig(): Flow<Result<ProxyConfig>> = 
        localDataSource.getProxyConfig()
            .map { entity -> mapper.mapToDomain(entity) }  // Map domains từ string to list
            .map { Result.success(it) }
            .catch { emit(Result.failure(it)) }
}
```

### 3. ViewModel với Compose State (Kotlin)
```kotlin
@HiltViewModel
class ProxyViewModel @Inject constructor(
    private val startProxyUseCase: StartProxyUseCase,
    private val stopProxyUseCase: StopProxyUseCase,
    private val saveProxyConfigUseCase: SaveProxyConfigUseCase,
    private val getProxyConfigUseCase: GetProxyConfigUseCase,
    private val testProxyConnectionUseCase: TestProxyConnectionUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ProxyUiState())
    val uiState: StateFlow<ProxyUiState> = _uiState.asStateFlow()
    
    fun updateConfig(updatedConfig: ProxyConfig) {
        _uiState.value = _uiState.value.copy(proxyConfig = updatedConfig)  // Cập nhật bao gồm domains
    }
    
    fun startProxy() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            startProxyUseCase(_uiState.value.proxyConfig)  // Config có domains
                .collect { result ->
                    result.fold(
                        onSuccess = { success ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                isProxyEnabled = success
                            )
                        },
                        onFailure = { error ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = error.message
                            )
                        }
                    )
                }
        }
    }
}
```

### 4. Jetpack Compose UI State
```kotlin
data class ProxyUiState(
    val proxyConfig: ProxyConfig = ProxyConfig(domains = emptyList()),
    val isProxyEnabled: Boolean = false,
    val isLoading: Boolean = false,
    val isTestingConnection: Boolean = false,
    val error: String? = null,
    val validationErrors: Map<String, String> = emptyMap()
)
```
- ProxyConfig sẽ có field `domains: List<String>` để lưu các domain từ input comma-separated.

### 5. Dependency Injection với Hilt (Kotlin)
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object UseCaseModule {
    @Provides
    @Singleton
    fun provideStartProxyUseCase(repository: ProxyRepository): StartProxyUseCase =
        StartProxyUseCase(repository)
    
    @Provides
    @Singleton
    fun provideStopProxyUseCase(repository: ProxyRepository): StopProxyUseCase =
        StopProxyUseCase(repository)
}

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    abstract fun bindProxyRepository(
        proxyRepositoryImpl: ProxyRepositoryImpl
    ): ProxyRepository
}
```

### 6. Validation
- Validate IP address format
- Validate port range (1-65535)
- Validate username/password không trống
- Validate domains: không trống, format hợp lệ (domain names), phân tách bằng dấu phẩy, không có khoảng trắng thừa
- Hiển thị error messages phù hợp

### 7. Data persistence
- Lưu cấu hình proxy vào Room Database (domains lưu dưới dạng string comma-separated)
- Encrypt password trước khi lưu bằng Security Crypto
- Auto-load cấu hình khi mở app, parse domains từ string to list

### 8. Quick Settings Tile implementation
- Extend TileService
- Override onClick() để toggle proxy
- Update tile state và icon
- Hiển thị toast message khi thay đổi trạng thái

## UI/UX yêu cầu - Jetpack Compose + Material 3

### Compose UI Implementation:
```kotlin
@Composable
fun ProxyConfigScreen(
    viewModel: ProxyViewModel = hiltViewModel(),
    onNavigateToSettings: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            // Show snackbar
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        ProxyConfigForm(
            config = uiState.proxyConfig,
            onConfigChange = viewModel::updateConfig,  // Update bao gồm domains
            validationErrors = uiState.validationErrors
        )
        
        ProxyControlButtons(
            isLoading = uiState.isLoading,
            isProxyEnabled = uiState.isProxyEnabled,
            onStartProxy = viewModel::startProxy,
            onStopProxy = viewModel::stopProxy,
            onTestConnection = viewModel::testConnection
        )
    }
}
```
- Trong ProxyConfigForm, thêm OutlinedTextField cho "Domains (comma-separated)" và xử lý onChange để parse thành list.

### Material 3 Design System:
- **Color Scheme**: Dynamic color support cho Android 12+
- **Typography**: Material 3 typography scale
- **Components**: Material 3 components với proper theming
- **Dark Theme**: Automatic dark/light theme switching
- **Accessibility**: Proper contrast ratios và content descriptions

### Responsive Design:
- **Adaptive Layout**: Support cho tablets và foldable devices
- **Window Size Classes**: Different layouts cho different screen sizes
- **Orientation Changes**: Proper handling của orientation changes

### Animation & Transitions:
- **Enter/Exit Animations**: Smooth transitions between screens
- **State Changes**: Animated state changes cho UI components
- **Loading States**: Proper loading animations
- **Gesture Animations**: Smooth gesture-based interactions

## Testing yêu cầu

### Test cases cần implement:
1. Test validate input fields (bao gồm domains)
2. Test proxy connection với credentials hợp lệ và domains cụ thể
3. Test proxy connection với credentials không hợp lệ hoặc domains không hợp lệ
4. Test Quick Settings Tile functionality
5. Test data persistence (bao gồm domains)
6. Test WorkManager background tasks
7. Test domain matching logic (route chỉ cho domains trong list)

## Deployment và build

### Build configuration:
- **Target SDK: 34 (Android 14)**
- **Min SDK: 29 (Android 10)**
- **Compile SDK: 34**
- Proguard rules cho release build
- R8 optimization enabled

### APK requirements:
- Signed APK cho distribution
- Multiple APK support cho different architectures

## Bảo mật
- Encrypt stored passwords bằng Security Crypto
- Validate all user inputs (bao gồm domains)
- Handle network timeouts properly
- Secure communication với proxy server

## Lưu ý đặc biệt
- Ứng dụng này cần quyền root hoặc VPN permission để thực sự route traffic qua proxy (và xử lý domain-specific routing)
- Nếu không có root, có thể cần implement VPN service với logic domain matching
- Test trên nhiều thiết bị Android khác nhau
- Xử lý các trường hợp lỗi network

Hãy tạo ứng dụng Android hoàn chỉnh theo các yêu cầu trên với:

## Coding Standards (Kotlin):
- **SOLID Principles**: Strict adherence to all 5 principles
- **Clean Architecture**: Clear separation of concerns across layers
- **Design Patterns**: Repository, MVVM, Observer, Factory patterns
- **Dependency Injection**: Proper DI implementation với Hilt
- **Coroutines & Flow**: Kotlin Coroutines cho asynchronous operations
- **Compose State Management**: Proper state management với StateFlow/State
- **Error Handling**: Comprehensive error handling với Result wrapper
- **Unit Testing**: Testable code structure với proper mocking
- **Code Documentation**: Proper KDoc documentation
- **Kotlin Conventions**: Idiomatic Kotlin code with proper naming

## Architecture Requirements:
- **Separation of Concerns**: Each layer has distinct responsibilities
- **Dependency Rule**: Dependencies point inward (toward domain)
- **Unidirectional Data Flow**: UI events flow down, state flows up
- **Reactive Programming**: Use Flow for reactive data streams
- **Compose Best Practices**: Proper composable design và reusability
- **State Hoisting**: Proper state management in Compose
- **Performance**: Optimized recomposition và memory usage

## Jetpack Compose Specific:
- **Composable Functions**: Stateless và reusable composables
- **State Management**: Proper use of remember, mutableStateOf, StateFlow
- **Side Effects**: Proper use of LaunchedEffect, DisposableEffect
- **Navigation**: Compose Navigation với type-safe arguments
- **Theming**: Material 3 theming với custom colors
- **Preview Functions**: Comprehensive preview functions for all composables