# CI/CD Documentation - Android Proxy Project

## 🚀 Tổng Quan

GitHub Actions workflow của project này được thiết kế để tự động hóa quá trình kiểm tra chất lượng code, testing, documentation và deployment. Workflow bao gồm 5 jobs chính chạy song song để đảm bảo code quality và tự động hóa các tác vụ lặp đi lặp lại.

## 🔄 GitHub Actions Workflow

### 📋 Trigger Events
```yaml
on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
```

**Khi nào workflow chạy:**
- ✅ Push code lên `main`, `develop`, hoặc bất kỳ `feature/*` branch
- ✅ Tạo Pull Request vào `main` hoặc `develop` branch
- ✅ Update Pull Request đã tồn tại

### 🛠️ Jobs Chi Tiết

#### 1. 🎨 **Code Quality & Linting** (`lint`)
**<PERSON><PERSON><PERSON> đích:** Kiểm tra và tự động sửa code formatting, style issues

**<PERSON><PERSON><PERSON> bước thực hiện:**
- 🔍 **Ktlint Format**: Tự động fix Kotlin code formatting
- 🤖 **Auto-commit**: Commit formatting changes (chỉ trên push events)
- 📊 **Android Lint**: Kiểm tra static analysis cho Android code
- 📤 **Upload Reports**: Upload lint reports làm artifacts

**Outputs:**
- `app/build/reports/ktlint/` - Ktlint formatting reports
- `app/build/reports/lint-results-debug.html` - Android Lint report

#### 2. 🧪 **Unit Tests** (`test`)
**Mục đích:** Chạy unit tests và tạo coverage reports

**Các bước thực hiện:**
- ▶️ **Run Tests**: Chạy tất cả unit tests
- 📊 **Coverage Report**: Tạo Jacoco test coverage report
- 📤 **Upload Results**: Upload test results và coverage reports

**Outputs:**
- `app/build/reports/tests/testDebugUnitTest/` - Unit test results
- `app/build/reports/jacoco/jacocoTestReport/` - Test coverage reports

#### 3. 📚 **Documentation** (`documentation`)
**Mục đích:** Tạo và deploy API documentation

**Các bước thực hiện:**
- 📖 **Generate KDoc**: Tạo API documentation từ code comments
- 🤖 **Auto-commit**: Commit documentation updates
- 🌐 **Deploy to GitHub Pages**: Deploy docs lên GitHub Pages (main branch only)

**Outputs:**
- `app/build/dokka/html/` - KDoc HTML documentation
- GitHub Pages: `https://username.github.io/repo-name/docs/`

#### 4. 🏗️ **Build Verification** (`build`)
**Mục đích:** Verify code compiles successfully

**Các bước thực hiện:**
- ✅ **Dry Run Build**: Verify compilation without creating artifacts
- 🚀 **Quick Validation**: Fast build verification

#### 5. 🔒 **Security Scan** (`security`)
**Mục đích:** Scan for security vulnerabilities

**Các bước thực hiện:**
- 🛡️ **Trivy Scan**: Scan filesystem for vulnerabilities
- 📤 **Upload SARIF**: Upload results to GitHub Security tab

#### 6. 📊 **Archive Reports** (`archive-reports`)
**Mục đích:** Archive all reports to repository and cleanup artifacts

**Các bước thực hiện:**
- 🗑️ **Clear Old Reports**: Remove previous reports from `reports/` folder
- 📋 **Generate Reports**: Run all quality checks and generate fresh reports
- 📁 **Copy Reports**: Copy all reports to `reports/` folder structure
- 📖 **Create Index**: Generate README.md with links to all reports
- 🤖 **Auto-commit**: Commit reports with Reports Bot account
- 🗑️ **Delete Artifacts**: Remove GitHub Actions artifacts after archiving

**Outputs:**
- `reports/lint/` - Ktlint and Android Lint reports
- `reports/test/` - Unit test results
- `reports/coverage/` - Test coverage reports
- `reports/docs/` - KDoc API documentation
- `reports/README.md` - Index with direct links

**Artifact Cleanup:**
- Automatically deletes `lint-reports` and `test-results` artifacts
- Uses GitHub CLI to clean up storage
- Reports remain accessible in repository

## 📊 Cách Xem Reports và Documentation

### 🎯 **Trong GitHub Actions**

#### 1. **Workflow Results**
```
GitHub Repository → Actions tab → Select workflow run
```

#### 2. **Artifacts Download**
```
Workflow run → Artifacts section → Download:
- lint-reports.zip
- test-results.zip
```

### 📖 **KDoc Documentation**

#### **Xem Online (GitHub Pages)**
```
https://username.github.io/repository-name/docs/
```

#### **Xem từ Reports Repository**
```
https://github.com/username/repository/tree/main/reports/docs/html/
```

#### **Xem Local**
```bash
# Sau khi chạy workflow hoặc local build
open app/build/dokka/html/index.html

# Hoặc từ reports folder
open reports/docs/html/index.html
```

#### **Generate Local**
```bash
./gradlew dokkaHtml
```

### 📊 **Jacoco Coverage Reports**

#### **Xem Local**
```bash
# Generate coverage report
./gradlew jacocoTestReport

# Mở report
open app/build/reports/jacoco/jacocoTestReport/html/index.html
```

#### **Thông tin trong report:**
- 📈 **Line Coverage**: % dòng code được test
- 🌿 **Branch Coverage**: % nhánh logic được test
- 📦 **Class Coverage**: % classes được test
- 🎯 **Method Coverage**: % methods được test

### 🔍 **Android Lint Reports**

#### **Xem Local**
```bash
# Generate lint report
./gradlew lintDebug

# Mở report
open app/build/reports/lint-results-debug.html
```

#### **Thông tin trong report:**
- ❌ **Errors**: Lỗi nghiêm trọng cần fix
- ⚠️ **Warnings**: Cảnh báo nên xem xét
- ℹ️ **Info**: Thông tin tham khảo
- 💡 **Suggestions**: Đề xuất cải thiện

### 🧪 **Unit Test Reports**

#### **Xem Local**
```bash
# Run tests
./gradlew testDebugUnitTest

# Mở report
open app/build/reports/tests/testDebugUnitTest/index.html
```

## 🛠️ Scripts và Công Dụng

### 📁 **Thư mục `scripts/`**

#### 1. 🎨 **`format-code.sh`**
**Công dụng:** Auto-format Kotlin code và optional auto-commit

**Cách sử dụng:**
```bash
# Chỉ format code
./scripts/format-code.sh

# Format và auto-commit (khi được hỏi)
./scripts/format-code.sh
# → Nhập 'y' khi được hỏi "Auto-commit formatting changes?"
```

**Chức năng:**
- 🔧 Chạy `ktlintFormat` để fix formatting issues
- ✅ Verify formatting với `ktlintCheck`
- 📊 Hiển thị git status và changes
- 🤖 Option để auto-commit changes

#### 2. 🤖 **`auto-commit.sh`**
**Công dụng:** Intelligent auto-commit cho formatting và documentation changes

**Cách sử dụng:**
```bash
./scripts/auto-commit.sh
```

**Chức năng:**
- 🔍 **Detect Changes**: Tự động phát hiện loại changes
- 🎨 **Ktlint Commits**: Auto-commit formatting changes với `Android Proxy Bot`
- 📚 **Docs Commits**: Auto-commit documentation với `Android Proxy Docs Bot`
- 🚫 **Skip CI**: Sử dụng `[skip ci]` để tránh infinite loops

**Bot Accounts:**
- `<EMAIL>` - Cho code formatting
- `<EMAIL>` - Cho documentation updates

#### 3. 🚀 **`quality-check.sh`**
**Công dụng:** Comprehensive quality check script - chạy tất cả checks locally

**Cách sử dụng:**
```bash
./scripts/quality-check.sh
```

**Chức năng:**
- 🧹 **Clean**: Clean previous builds
- 🎨 **Ktlint**: Format + check code style
- 🔍 **Android Lint**: Static analysis
- 🧪 **Unit Tests**: Run all tests
- 📊 **Coverage**: Generate coverage reports
- ✅ **Build**: Verify compilation
- 📖 **KDoc**: Generate documentation
- 🌐 **Browser**: Option để mở reports trong browser

**Output Reports:**
```
📊 Reports Generated:
• Ktlint: app/build/reports/ktlint/
• Android Lint: app/build/reports/lint-results-debug.html
• Unit Tests: app/build/reports/tests/testDebugUnitTest/index.html
• Test Coverage: app/build/reports/jacoco/jacocoTestReport/html/index.html
• KDoc: app/build/dokka/html/index.html
```

#### 4. 📊 **`archive-reports.sh`**
**Công dụng:** Archive all reports to repository for permanent access

**Cách sử dụng:**
```bash
./scripts/archive-reports.sh
```

**Chức năng:**
- 🗑️ **Clear Old**: Remove previous reports from `reports/` folder
- 📋 **Generate All**: Run all quality checks and generate fresh reports
- 📁 **Copy Reports**: Organize reports into structured folders
- 📖 **Create Index**: Generate README.md with direct links
- 🤖 **Auto-commit**: Commit with Reports Bot account
- 🚀 **Optional Push**: Ask user to push to remote

**Repository Structure:**
```
reports/
├── README.md                    # Index with all links
├── lint/                        # Ktlint + Android Lint
│   ├── lint-results-debug.html
│   └── ktlint*.txt
├── test/                        # Unit test results
│   └── testDebugUnitTest/
├── coverage/                    # Test coverage
│   └── jacocoTestReport/html/
└── docs/                        # API documentation
    └── html/
```

## 🎯 **Workflow Features**

### ✨ **Auto-Fix & Auto-Commit**
- 🔧 Tự động fix Ktlint formatting issues
- 🤖 Auto-commit changes với meaningful commit messages
- 🚫 Sử dụng `[skip ci]` để tránh infinite loops
- 👥 Separate bot accounts cho different types of commits

### 📦 **Artifact Management**
- 📤 Upload essential reports only (không upload APK để tiết kiệm storage)
- 🗂️ Organized artifact names: `lint-reports`, `test-results`
- ⏰ Artifacts available for download sau mỗi workflow run
- 🗑️ **Auto-cleanup**: Artifacts automatically deleted after archiving to repository
- 💾 **Storage Optimization**: No redundant storage between artifacts and repository

### 🔒 **Security & Permissions**
- 🛡️ `contents: write` - Để auto-commit changes
- 🔍 `pull-requests: write` - Để comment trên PRs
- 🔐 `security-events: write` - Để upload security scan results
- 🔐 Trivy security scanning với SARIF upload

### 🚀 **Performance Optimizations**
- ⚡ Gradle caching để speed up builds
- 🏃 Parallel job execution
- 🎯 Conditional steps (auto-commit chỉ trên push events)

## �️ **Recent Fixes & Improvements**

### ✅ **Lint Errors Fixed**

#### **1. TileService Deprecation (API 34+)**
**Issue**: `startActivityAndCollapse(Intent)` deprecated for Android 14+
**Solution**:
```kotlin
@SuppressLint("StartActivityAndCollapseDeprecated")
private fun openMainApp() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        // API 34+ - Use PendingIntent
        val pendingIntent = PendingIntent.getActivity(...)
        startActivityAndCollapse(pendingIntent)
    } else {
        // API 29-33 - Use Intent (deprecated but still works)
        startActivityAndCollapse(intent)
    }
}
```

#### **2. Protected Permissions**
**Issue**: Lint warnings về VPN và Quick Settings permissions
**Solution**: Added `tools:ignore="ProtectedPermissions"` cho:
- `android.permission.BIND_VPN_SERVICE`
- `android.permission.BIND_QUICK_SETTINGS_TILE`

#### **3. WorkManager Initialization**
**Issue**: Conflict với custom WorkManager configuration
**Solution**: Disabled default initialization trong AndroidManifest.xml:
```xml
<provider
    android:name="androidx.startup.InitializationProvider"
    android:authorities="${applicationId}.androidx-startup"
    android:exported="false"
    tools:node="merge">
    <meta-data
        android:name="androidx.work.WorkManagerInitializer"
        android:value="androidx.startup"
        tools:node="remove" />
</provider>
```

### 🔐 **Security Scan Permissions**
**Issue**: "Resource not accessible by integration" error
**Solution**: Added `security-events: write` permission to workflow:
```yaml
permissions:
  contents: write
  pull-requests: write
  security-events: write  # Added for SARIF upload
```

## �🔧 **Local Development Workflow**

### 📝 **Before Committing**
```bash
# 1. Run quality checks
./scripts/quality-check.sh

# 2. Fix any issues found
# 3. Optional: Auto-commit formatting
./scripts/format-code.sh

# 4. Review changes
git status
git diff

# 5. Commit manually hoặc let auto-commit handle it
```

### 🚀 **CI/CD Integration**
```bash
# Push code → GitHub Actions sẽ:
# 1. Auto-fix formatting issues
# 2. Run all quality checks
# 3. Generate reports
# 4. Auto-commit documentation updates
# 5. Deploy docs to GitHub Pages (nếu main branch)
```

## 🎯 **Best Practices**

### ✅ **Do's**
- 🔍 Review auto-committed changes trước khi merge
- 📊 Check reports sau mỗi workflow run
- 🧪 Write tests cho new features
- 📖 Add KDoc comments cho public APIs
- 🎨 Let auto-formatting handle style issues

### ❌ **Don'ts**
- 🚫 Không manually edit formatting sau khi auto-fix
- 🚫 Không ignore lint warnings without good reason
- 🚫 Không commit without running quality checks locally
- 🚫 Không push directly to main without PR review

## 🔍 **Troubleshooting**

### ❌ **Common Issues**

#### **Workflow Failures**
```bash
# Check workflow logs in GitHub Actions tab
# Common fixes:
1. Update deprecated actions
2. Fix lint errors manually
3. Resolve test failures
4. Check permissions
```

#### **Auto-commit Not Working**
```bash
# Verify permissions
# Check branch protection rules
# Ensure [skip ci] is working
```

#### **Reports Not Generated**
```bash
# Run locally to debug
./scripts/quality-check.sh

# Check Gradle build logs
./gradlew build --info
```

## 📱 **Quick Access Guide**

### 🔗 **Direct Links (Local Development)**

#### **After Running `./scripts/quality-check.sh`:**
```bash
# Open all reports at once
open app/build/reports/lint-results-debug.html
open app/build/reports/tests/testDebugUnitTest/index.html
open app/build/reports/jacoco/jacocoTestReport/html/index.html
open app/build/dokka/html/index.html
```

#### **Individual Commands:**
```bash
# Ktlint Report
find app/build/reports/ktlint -name "*.html" -exec open {} \;

# Android Lint
open app/build/reports/lint-results-debug.html

# Unit Tests
open app/build/reports/tests/testDebugUnitTest/index.html

# Coverage Report
open app/build/reports/jacoco/jacocoTestReport/html/index.html

# API Documentation
open app/build/dokka/html/index.html
```

### 🌐 **GitHub Integration**

#### **GitHub Actions Artifacts:**
1. Go to: `Repository → Actions → Select Workflow Run`
2. Scroll down to **Artifacts** section
3. Download: `lint-reports.zip` hoặc `test-results.zip`
4. Extract và open HTML files

#### **GitHub Pages (Documentation):**
- **URL Pattern**: `https://[username].github.io/[repository]/docs/`
- **Auto-deployed**: Khi push lên `main` branch
- **Content**: Latest KDoc API documentation

#### **Security Reports:**
- Go to: `Repository → Security → Code scanning alerts`
- View: Trivy vulnerability scan results
- Format: SARIF (Security Analysis Results Interchange Format)

### 📊 **Report Contents Explained**

#### **🎯 Jacoco Coverage Report**
```
📈 Coverage Metrics:
├── Instructions: Bytecode instruction coverage
├── Branches: Conditional branch coverage
├── Lines: Source line coverage
├── Methods: Method coverage
└── Classes: Class coverage

🎨 Color Coding:
🟢 Green: Well covered (>80%)
🟡 Yellow: Partially covered (50-80%)
🔴 Red: Poorly covered (<50%)
```

#### **🔍 Android Lint Report**
```
📋 Issue Categories:
├── 🔴 Error: Must fix before release
├── ⚠️ Warning: Should review and fix
├── ℹ️ Informational: Good to know
└── 💡 Suggestion: Performance/style improvements

📍 Issue Details:
├── File location and line number
├── Issue description and explanation
├── Suggested fixes
└── Related documentation links
```

#### **🧪 Unit Test Report**
```
📊 Test Results:
├── Total tests run
├── Passed/Failed/Skipped counts
├── Execution time
└── Test class breakdown

📝 Test Details:
├── Test method names
├── Execution duration
├── Failure reasons (if any)
└── Stack traces for failures
```

#### **📖 KDoc Documentation**
```
📚 Documentation Structure:
├── Package overview
├── Class hierarchy
├── Method signatures
├── Parameter descriptions
├── Return value documentation
├── Usage examples
└── Related links
```

### 🛠️ **Advanced Usage**

#### **Custom Report Generation:**
```bash
# Generate specific reports only
./gradlew ktlintCheck                    # Ktlint only
./gradlew lintDebug                      # Android Lint only
./gradlew testDebugUnitTest              # Tests only
./gradlew jacocoTestReport               # Coverage only
./gradlew dokkaHtml                      # Documentation only

# Generate all reports
./gradlew ktlintCheck lintDebug testDebugUnitTest jacocoTestReport dokkaHtml
```

#### **CI/CD Report Access:**
```bash
# Download artifacts using GitHub CLI
gh run download [run-id] --name lint-reports
gh run download [run-id] --name test-results

# View latest workflow status
gh run list --limit 5
gh run view [run-id]
```

### 🎯 **Integration with IDEs**

#### **Android Studio:**
- **Lint**: `Analyze → Inspect Code`
- **Tests**: `Run → Run All Tests`
- **Coverage**: `Run → Run with Coverage`

#### **IntelliJ IDEA:**
- **Reports**: Available in `Build` tool window
- **Coverage**: Integrated coverage highlighting
- **Documentation**: `Tools → Generate JavaDoc`

### 📱 **Mobile-Friendly Access**

#### **GitHub Mobile App:**
- View workflow status
- Download artifacts (limited)
- Check security alerts

#### **Web Browser (Mobile):**
- Full access to all reports
- GitHub Pages documentation
- Workflow logs and artifacts

---

**📚 Related Documentation:**
- [CI Workflow Details](CI_WORKFLOW.md)
- [Bot Accounts](BOT_ACCOUNTS.md)
- [GitHub Actions Updates](GITHUB_ACTIONS_UPDATES.md)

**🔗 External Resources:**
- [Ktlint Documentation](https://ktlint.github.io/)
- [Jacoco Documentation](https://www.jacoco.org/jacoco/trunk/doc/)
- [Android Lint Guide](https://developer.android.com/studio/write/lint)
- [KDoc Documentation](https://kotlinlang.org/docs/kotlin-doc.html)
