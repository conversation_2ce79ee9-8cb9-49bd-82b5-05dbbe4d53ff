# CI/CD Workflow Documentation

## 🚀 Overview

This project uses an automated CI/CD workflow that automatically fixes code formatting issues and updates documentation. The workflow is designed to maintain code quality while reducing manual intervention.

## 🔄 Workflow Features

### ✨ Auto-Fix & Auto-Commit

The CI workflow automatically:

1. **Ktlint Auto-Fix**: Runs `ktlintFormat` to fix code formatting issues
2. **Auto-Commit**: Commits formatting changes back to the repository
3. **KDoc Updates**: Generates and commits documentation updates
4. **Skip CI Loops**: Uses `[skip ci]` to prevent infinite workflow triggers

### 🛡️ Quality Gates

- **Code Linting**: Ktlint for Kotlin code style
- **Android Lint**: Static analysis for Android-specific issues
- **Unit Tests**: Automated testing with coverage reports
- **Build Verification**: Ensures code compiles successfully
- **Security Scanning**: Trivy vulnerability scanner

## 📋 Workflow Jobs

### 1. Code Quality & Linting
- **Auto-fixes** formatting issues with Ktlint
- **Commits** changes automatically (push events only)
- Runs Android Lint for static analysis
- Uploads lint reports as artifacts

### 2. Unit Tests
- Runs all unit tests
- Generates test coverage reports
- Uploads test results and coverage reports

### 3. Documentation
- Generates KDoc documentation
- **Auto-commits** documentation updates
- Deploys to GitHub Pages (main branch only)

### 4. Build Verification
- Verifies that code compiles successfully
- Uses `--dry-run` to avoid creating unnecessary artifacts

### 5. Security Scan
- Runs Trivy vulnerability scanner
- Uploads results to GitHub Security tab

## 🔧 Local Development Scripts

### `./scripts/quality-check.sh`
Comprehensive quality check script that runs:
- Ktlint format + check
- Android Lint
- Unit tests with coverage
- Build verification
- KDoc generation

### `./scripts/format-code.sh`
Auto-formats code and optionally commits changes:
- Runs Ktlint format
- Shows git diff
- Option to auto-commit changes

### `./scripts/auto-commit.sh`
Automatically commits formatting and documentation changes:
- Detects Kotlin file changes (ktlint)
- Detects documentation changes (KDoc)
- Creates appropriate commit messages
- Uses `[skip ci]` to prevent workflow loops

## 🚦 Workflow Triggers

### Push Events
- **Branches**: `main`, `develop`, `feature/*`
- **Auto-commit**: Enabled (formatting & docs)
- **Deployment**: GitHub Pages (main branch only)

### Pull Request Events
- **Branches**: `main`, `develop`
- **Auto-commit**: Disabled (read-only)
- **Quality checks**: Full validation

## 📊 Artifacts & Reports

### Generated Reports
- **Ktlint**: `app/build/reports/ktlint/`
- **Android Lint**: `app/build/reports/lint-results-debug.html`
- **Unit Tests**: `app/build/reports/tests/testDebugUnitTest/`
- **Coverage**: `app/build/reports/jacoco/jacocoTestReport/`
- **KDoc**: `app/build/dokka/html/`

### Uploaded Artifacts
- Lint reports (always uploaded)
- Test results and coverage (always uploaded)
- Security scan results (uploaded to GitHub Security)

### Removed Artifacts
- ❌ Debug APK (no longer uploaded to save storage)
- ❌ Build artifacts (verification only)

## 🔒 Permissions

The workflow requires:
- `contents: write` - To commit auto-fixes and documentation
- `pull-requests: write` - To comment on PRs

## 🚨 Important Notes

### Auto-Commit Behavior
- **Only on push events** (not PRs)
- **Uses `[skip ci]`** to prevent infinite loops
- **GitHub Actions bot** as commit author
- **Separate commits** for formatting vs documentation

### Preventing Infinite Loops
1. Commits include `[skip ci]` in message
2. Only triggers on actual code changes
3. Auto-commit only on push events, not PRs

### Branch Protection
- Auto-commit works on all configured branches
- Consider branch protection rules for production branches
- Review auto-commits in pull requests

## 🛠️ Customization

### Disable Auto-Commit
To disable auto-commit, remove or comment out the commit steps in `.github/workflows/ci.yml`:

```yaml
# - name: Commit Ktlint fixes
#   if: steps.ktlint-changes.outputs.changes == 'true' && github.event_name == 'push'
#   run: |
#     git config --local user.email "<EMAIL>"
#     git config --local user.name "GitHub Action"
#     git add .
#     git commit -m "🔧 Auto-fix Ktlint formatting issues [skip ci]"
#     git push
```

### Modify Commit Messages
Edit the commit messages in the workflow file to match your preferences:

```yaml
git commit -m "🔧 Auto-fix Ktlint formatting issues [skip ci]"
git commit -m "📚 Auto-update KDoc documentation [skip ci]"
```

### Add More Auto-Fixes
Extend the workflow to auto-fix other tools:
- Spotless
- Detekt
- Custom formatters

## 📈 Benefits

1. **Reduced Manual Work**: No more manual formatting fixes
2. **Consistent Code Style**: Automatic enforcement
3. **Up-to-date Docs**: Always current documentation
4. **Clean History**: Separate commits for different types of changes
5. **Fast Feedback**: Quick quality checks on every push
6. **Storage Efficient**: No unnecessary artifacts

## 🔍 Troubleshooting

### Workflow Not Triggering
- Check branch names match trigger patterns
- Ensure permissions are correctly set
- Verify GitHub Actions are enabled

### Auto-Commit Not Working
- Check if `contents: write` permission is granted
- Verify the workflow is running on push events
- Check for branch protection rules

### Infinite Loops
- Ensure `[skip ci]` is in commit messages
- Check that auto-commit only runs on push events
- Verify conditional logic in workflow steps

## 📚 References

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Ktlint Documentation](https://ktlint.github.io/)
- [Dokka Documentation](https://kotlinlang.org/docs/dokka-introduction.html)
- [Android Lint Documentation](https://developer.android.com/studio/write/lint)
