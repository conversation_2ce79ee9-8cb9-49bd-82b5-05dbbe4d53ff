#!/bin/bash

# Quality Check Script for Proxy Self Android App
# This script runs all quality checks locally before pushing to repository

set -e  # Exit on any error

echo "🚀 Starting Quality Checks for Proxy Self Android App"
echo "=================================================="
echo "📋 Requirements: JDK 21, Android SDK 35"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gradlew exists
if [ ! -f "./gradlew" ]; then
    print_error "gradlew not found. Please run this script from the project root directory."
    exit 1
fi

# Make gradlew executable
chmod +x ./gradlew

# Clean build
print_status "Cleaning previous build..."
./gradlew clean

# 1. Ktlint Format & Check
print_status "Running Ktlint Format (Auto-fix)..."
./gradlew ktlintFormat

print_status "Running Ktlint Check..."
if ./gradlew ktlintCheck; then
    print_success "Ktlint check passed ✅"
else
    print_error "Ktlint check failed ❌"
    exit 1
fi

# 2. Android Lint
print_status "Running Android Lint..."
if ./gradlew lintDebug; then
    print_success "Android Lint check passed ✅"
else
    print_warning "Android Lint found issues ⚠️"
    print_warning "Check app/build/reports/lint-results-debug.html for details"
fi

# 3. Unit Tests
print_status "Running Unit Tests..."
if ./gradlew testDebugUnitTest; then
    print_success "Unit tests passed ✅"
else
    print_error "Unit tests failed ❌"
    print_warning "Check app/build/reports/tests/testDebugUnitTest/index.html for details"
    exit 1
fi

# 4. Test Coverage
print_status "Generating Test Coverage Report..."
if ./gradlew jacocoTestReport; then
    print_success "Test coverage report generated ✅"
    print_warning "Check app/build/reports/jacoco/jacocoTestReport/html/index.html for coverage details"
else
    print_warning "Test coverage report generation failed ⚠️"
fi

# 5. Verify Build Compiles
print_status "Verifying build compiles..."
if ./gradlew assembleDebug --dry-run; then
    print_success "Build verification passed ✅"
else
    print_error "Build verification failed ❌"
    exit 1
fi

# 6. Generate Documentation
print_status "Generating KDoc Documentation..."
if ./gradlew dokkaHtml; then
    print_success "KDoc documentation generated ✅"
    print_warning "Documentation location: app/build/dokka/html/index.html"
else
    print_warning "KDoc documentation generation failed ⚠️"
fi

# Summary
echo ""
echo "=================================================="
print_success "🎉 All Quality Checks Completed Successfully!"
echo "=================================================="
echo ""
echo "📊 Reports Generated:"
echo "  • Ktlint: app/build/reports/ktlint/"
echo "  • Android Lint: app/build/reports/lint-results-debug.html"
echo "  • Unit Tests: app/build/reports/tests/testDebugUnitTest/index.html"
echo "  • Test Coverage: app/build/reports/jacoco/jacocoTestReport/html/index.html"
echo "  • KDoc: app/build/dokka/html/index.html"
echo ""
print_success "✅ Ready to commit and push!"
echo ""

# Optional: Open reports in browser (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    read -p "Open test coverage report in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open app/build/reports/jacoco/jacocoTestReport/html/index.html
    fi
    
    read -p "Open KDoc documentation in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open app/build/dokka/html/index.html
    fi
fi
