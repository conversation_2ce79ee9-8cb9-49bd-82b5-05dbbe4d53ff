#!/bin/bash

# Archive Reports Script
# Generates all reports and commits them to reports/ folder

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "📊 ARCHIVE REPORTS TO REPOSITORY"
    echo "=================================================="
    echo -e "${NC}"
}

print_header

# Check if we're in the right directory
if [[ ! -f "gradlew" ]]; then
    print_error "gradlew not found. Please run this script from the project root directory."
    exit 1
fi

print_status "Generating all reports..."

# Generate all reports (continue even if some fail)
./gradlew ktlintCheck lintDebug testDebugUnitTest jacocoTestReport dokkaHtml --continue || true

print_status "Clearing old reports from repository..."

# Clear old reports
rm -rf reports/
mkdir -p reports/{lint,test,coverage,docs}

print_status "Copying reports to repository folder..."

# Copy Ktlint reports
if [ -d "app/build/reports/ktlint" ]; then
    cp -r app/build/reports/ktlint/* reports/lint/ 2>/dev/null || true
    print_success "Ktlint reports copied"
else
    print_warning "Ktlint reports not found"
fi

# Copy Android Lint reports
if [ -f "app/build/reports/lint-results-debug.html" ]; then
    cp app/build/reports/lint-results-debug.html reports/lint/
    print_success "Android Lint HTML report copied"
else
    print_warning "Android Lint HTML report not found"
fi

if [ -f "app/build/reports/lint-results-debug.xml" ]; then
    cp app/build/reports/lint-results-debug.xml reports/lint/
    print_success "Android Lint XML report copied"
else
    print_warning "Android Lint XML report not found"
fi

# Copy Test reports
if [ -d "app/build/reports/tests" ]; then
    cp -r app/build/reports/tests/* reports/test/ 2>/dev/null || true
    print_success "Test reports copied"
else
    print_warning "Test reports not found"
fi

# Copy Coverage reports
if [ -d "app/build/reports/jacoco" ]; then
    cp -r app/build/reports/jacoco/* reports/coverage/ 2>/dev/null || true
    print_success "Coverage reports copied"
else
    print_warning "Coverage reports not found"
fi

# Copy Documentation
if [ -d "app/build/dokka" ]; then
    cp -r app/build/dokka/* reports/docs/ 2>/dev/null || true
    print_success "Documentation copied"
else
    print_warning "Documentation not found"
fi

print_status "Creating reports index..."

# Create reports index
cat > reports/README.md << EOF
# 📊 Latest Build Reports

Generated on: $(date)
Commit: $(git rev-parse HEAD)
Branch: $(git branch --show-current)

## 📁 Available Reports

### 🎨 Code Quality & Linting
- **Android Lint**: [lint-results-debug.html](lint/lint-results-debug.html)
- **Ktlint Reports**: [lint/](lint/)

### 🧪 Testing
- **Unit Tests**: [test/testDebugUnitTest/](test/testDebugUnitTest/)
- **Test Coverage**: [coverage/jacocoTestReport/html/](coverage/jacocoTestReport/html/)

### 📚 Documentation
- **API Documentation**: [docs/html/](docs/html/)

## 🔗 Quick Links

| Report Type | Direct Link |
|-------------|-------------|
| Android Lint | [View Report](lint/lint-results-debug.html) |
| Unit Tests | [View Report](test/testDebugUnitTest/index.html) |
| Test Coverage | [View Report](coverage/jacocoTestReport/html/index.html) |
| API Docs | [View Report](docs/html/index.html) |

## 📊 Report Summary

### Files Generated:
$(find reports/ -type f | wc -l) files total

### Directory Structure:
\`\`\`
$(tree reports/ 2>/dev/null || find reports/ -type d | sed 's|[^/]*/|  |g')
\`\`\`

---
*Reports are automatically updated on every push to main, develop, or feature branches.*
EOF

print_success "Reports index created"

# Check if there are changes to commit
if [[ -n $(git status --porcelain reports/) ]]; then
    print_status "Committing updated reports..."
    
    # Configure git user for reports commits
    git config --local user.email "<EMAIL>"
    git config --local user.name "Android Proxy Reports Bot"
    
    git add reports/
    
    git commit -m "📊 Auto-update build reports

- Updated lint reports (Ktlint + Android Lint)
- Updated test results and coverage reports  
- Updated API documentation (KDoc)
- Generated from commit: $(git rev-parse HEAD)
- Branch: $(git branch --show-current)

[skip ci]"
    
    print_success "Reports committed successfully!"
    
    # Ask if user wants to push
    read -p "🚀 Push reports to remote repository? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git push
        print_success "Reports pushed to remote repository!"
    else
        print_warning "Reports committed locally but not pushed"
    fi
else
    print_warning "No changes in reports to commit"
fi

print_status "Opening reports folder..."
if command -v open >/dev/null 2>&1; then
    open reports/
elif command -v xdg-open >/dev/null 2>&1; then
    xdg-open reports/
else
    print_warning "Cannot open reports folder automatically"
fi

echo -e "${GREEN}"
echo "=================================================="
echo "✅ ARCHIVE REPORTS COMPLETED!"
echo "=================================================="
echo -e "${NC}"
echo "📁 Reports are available in: reports/"
echo "📖 View index: reports/README.md"
echo "🌐 GitHub Pages: https://username.github.io/repo-name/reports/"
echo ""
echo "💡 Note: If you have GitHub Actions artifacts, they can be manually"
echo "   deleted from the Actions tab since reports are now in repository"
