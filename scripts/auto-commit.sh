#!/bin/bash

# Auto-commit script for formatting and documentation changes
# This script checks for changes and commits them automatically

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository"
    exit 1
fi

# Check if there are any changes
if [[ -z $(git status --porcelain) ]]; then
    print_status "No changes to commit"
    exit 0
fi

echo "🔧 Auto-commit Script for Proxy Self Android App"
echo "================================================"
echo ""

# Show what changes we have
print_status "Checking for changes..."
git status --short

# Check for ktlint formatting changes
if git diff --name-only | grep -E '\.(kt|kts)$' > /dev/null; then
    print_status "Found Kotlin file changes - likely from ktlint formatting"

    # Configure git user for ktlint commits
    git config --local user.email "<EMAIL>"
    git config --local user.name "Android Proxy Bot"

    # Stage all Kotlin files
    git add *.kt *.kts **/*.kt **/*.kts 2>/dev/null || true

    # Check if we have staged changes
    if [[ -n $(git diff --cached --name-only) ]]; then
        print_status "Committing ktlint formatting changes..."
        git commit -m "🔧 Auto-fix Ktlint formatting issues

- Applied Kotlin code style formatting
- Fixed indentation and spacing
- Ensured consistent code formatting across project

[skip ci]"
        print_success "Ktlint changes committed ✅"
    fi
fi

# Check for documentation changes
if git diff --name-only | grep -E '(dokka|docs|\.md)' > /dev/null || \
   [[ -d "app/build/dokka" && -n $(find app/build/dokka -name "*.html" -newer .git/COMMIT_EDITMSG 2>/dev/null) ]]; then
    print_status "Found documentation changes"

    # Configure git user for documentation commits
    git config --local user.email "<EMAIL>"
    git config --local user.name "Android Proxy Docs Bot"

    # Stage documentation files
    git add docs/ app/build/dokka/ *.md **/*.md 2>/dev/null || true

    # Check if we have staged changes
    if [[ -n $(git diff --cached --name-only) ]]; then
        print_status "Committing documentation changes..."
        git commit -m "📚 Auto-update KDoc documentation

- Generated latest API documentation
- Updated class and method documentation
- Refreshed code examples and usage guides
- Synchronized documentation with code changes

[skip ci]"
        print_success "Documentation changes committed ✅"
    fi
fi

# Check for any remaining changes
if [[ -n $(git status --porcelain) ]]; then
    print_warning "There are still uncommitted changes:"
    git status --short
    print_warning "Please review and commit these changes manually"
else
    print_success "All changes have been committed ✅"
fi

echo ""
print_success "Auto-commit process completed!"
