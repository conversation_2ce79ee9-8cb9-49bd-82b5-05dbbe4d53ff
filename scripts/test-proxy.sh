#!/bin/bash

# Test script for proxy functionality
# This script helps test the proxy app with various scenarios

set -e

echo "🧪 Proxy App Testing Script"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if device is connected
check_device() {
    print_status "Checking for connected Android device..."
    
    if ! command -v adb &> /dev/null; then
        print_error "ADB not found. Please install Android SDK platform-tools."
        exit 1
    fi
    
    DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device$" || true)
    
    if [ "$DEVICE_COUNT" -eq 0 ]; then
        print_error "No Android device connected. Please connect a device and enable USB debugging."
        exit 1
    elif [ "$DEVICE_COUNT" -gt 1 ]; then
        print_warning "Multiple devices connected. Using the first one."
    fi
    
    DEVICE_MODEL=$(adb shell getprop ro.product.model 2>/dev/null || echo "Unknown")
    print_success "Device connected: $DEVICE_MODEL"
}

# Build and install app
build_and_install() {
    print_status "Building and installing app..."
    
    # Build the app
    ./gradlew assembleDebug
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
    
    # Install the app
    adb install -r app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        print_success "App installed successfully"
    else
        print_error "App installation failed"
        exit 1
    fi
}

# Start app and check logs
start_app_and_monitor() {
    print_status "Starting app and monitoring logs..."
    
    # Clear logcat
    adb logcat -c
    
    # Start the app
    adb shell am start -n com.android.proxy_self/.MainActivity
    
    print_status "App started. Monitoring logs for 30 seconds..."
    print_status "Look for proxy-related log messages..."
    
    # Monitor logs for proxy-related messages
    timeout 30s adb logcat | grep -E "(ProxyVpnService|ProxyService|ProxyManager|Socks5Client|DomainExtractor)" || true
}

# Test proxy connection
test_proxy_connection() {
    print_status "Testing proxy connection functionality..."
    
    print_warning "Manual testing required:"
    echo "1. Open the app on your device"
    echo "2. Enter SOCKS5 proxy details:"
    echo "   - Server: Your SOCKS5 proxy IP"
    echo "   - Port: Your SOCKS5 proxy port (usually 1080)"
    echo "   - Username/Password: Your credentials"
    echo "   - Domains: Add test domains like 'httpbin.org,google.com'"
    echo "3. Tap 'Test Connection' button"
    echo "4. If successful, tap 'Start Proxy'"
    echo "5. Check if VPN icon appears in status bar"
    echo "6. Try browsing to test domains"
    
    read -p "Press Enter when you've completed the manual testing..."
}

# Check VPN permissions
check_vpn_permissions() {
    print_status "Checking VPN permissions..."
    
    # Check if VPN permission is granted
    VPN_PERMISSION=$(adb shell dumpsys appops com.android.proxy_self | grep "ACTIVATE_VPN" || echo "Not found")
    
    if [[ "$VPN_PERMISSION" == *"allow"* ]]; then
        print_success "VPN permission granted"
    else
        print_warning "VPN permission may not be granted"
        print_warning "The app will prompt for VPN permission when starting proxy"
    fi
}

# Collect debug information
collect_debug_info() {
    print_status "Collecting debug information..."
    
    # Create debug directory
    mkdir -p debug_logs
    
    # Collect app logs
    adb logcat -d | grep -E "(ProxyVpnService|ProxyService|ProxyManager|Socks5Client|DomainExtractor)" > debug_logs/proxy_logs.txt
    
    # Collect system info
    adb shell dumpsys package com.android.proxy_self > debug_logs/package_info.txt
    
    # Collect network info
    adb shell dumpsys connectivity > debug_logs/network_info.txt
    
    print_success "Debug information collected in debug_logs/ directory"
}

# Main execution
main() {
    echo "Starting proxy app testing..."
    echo
    
    check_device
    echo
    
    build_and_install
    echo
    
    check_vpn_permissions
    echo
    
    start_app_and_monitor
    echo
    
    test_proxy_connection
    echo
    
    collect_debug_info
    echo
    
    print_success "Testing completed!"
    print_status "Check debug_logs/ directory for detailed logs"
    print_status "If proxy is not working, check the logs for error messages"
}

# Run main function
main "$@"
