#!/bin/bash

# Code Formatting Script for Proxy Self Android App
# This script auto-fixes code formatting issues using Ktlint

set -e  # Exit on any error

echo "🎨 Auto-formatting Kotlin Code"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gradlew exists
if [ ! -f "./gradlew" ]; then
    print_error "gradlew not found. Please run this script from the project root directory."
    exit 1
fi

# Make gradlew executable
chmod +x ./gradlew

# Run Ktlint format
print_status "Running Ktlint auto-format..."
if ./gradlew ktlintFormat; then
    print_success "Code formatting completed ✅"
else
    print_error "Code formatting failed ❌"
    exit 1
fi

# Verify formatting
print_status "Verifying code formatting..."
if ./gradlew ktlintCheck; then
    print_success "All formatting issues fixed ✅"
else
    print_warning "Some formatting issues remain ⚠️"
    print_warning "Please review and fix manually"
fi

echo ""
print_success "🎉 Code formatting completed!"
echo ""
print_warning "📝 Don't forget to review the changes before committing"
echo ""

# Show git status if in a git repository
if git rev-parse --git-dir > /dev/null 2>&1; then
    echo "📊 Git Status:"
    git status --short
    echo ""

    # Check if there are changes to commit
    if [[ -n $(git status --porcelain) ]]; then
        read -p "Auto-commit formatting changes? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Running auto-commit script..."
            ./scripts/auto-commit.sh
        else
            read -p "Show detailed diff? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                git diff --color=always | head -50
                echo ""
                print_warning "Use 'git diff' to see full changes"
            fi
        fi
    else
        print_success "No changes to commit"
    fi
fi
