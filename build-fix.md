# Build Fix Guide

## Current Issue

The project has a compatibility issue between Hilt 2.44 and Kotlin 2.0.21:

```
Error: [Hilt] Unsupported metadata version. Check that your Kotlin version is >= 1.0
```

## Solution Options

### Option 1: Downgrade Kotlin (Recommended)

Update `gradle/libs.versions.toml`:

```toml
[versions]
kotlin = "1.9.24"  # Change from 2.0.21
```

### Option 2: Upgrade Hilt

Update `gradle/libs.versions.toml`:

```toml
[versions]
hilt = "2.52"  # Change from 2.44
```

### Option 3: Remove Hilt Temporarily

If you need to build immediately:

1. Comment out Hilt plugin in `app/build.gradle.kts`:
```kotlin
plugins {
    // alias(libs.plugins.hilt)
    // kotlin("kapt")
}
```

2. Comment out Hilt dependencies:
```kotlin
dependencies {
    // implementation(libs.hilt.android)
    // kapt(libs.hilt.compiler)
}
```

3. Remove `@AndroidEntryPoint` and `@Inject` annotations
4. Use manual dependency injection

## Performance Optimizations

### 1. ProGuard/R8 Configuration

Add to `app/proguard-rules.pro`:

```proguard
# Keep proxy-related classes
-keep class com.android.proxy_self.domain.entities.** { *; }
-keep class com.android.proxy_self.data.database.entities.** { *; }

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# Room
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# Hilt
-dontwarn dagger.hilt.**
-keep class dagger.hilt.** { *; }
```

### 2. Memory Optimization

In `ProxyService.kt`, add memory management:

```kotlin
override fun onLowMemory() {
    super.onLowMemory()
    // Clear caches
    proxyManager.clearCache()
    System.gc()
}
```

### 3. Network Optimization

In `NetworkUtils.kt`, add connection pooling:

```kotlin
private val connectionPool = ConnectionPool(
    maxIdleConnections = 5,
    keepAliveDuration = 5,
    timeUnit = TimeUnit.MINUTES
)
```

### 4. Database Optimization

In `ProxyDatabase.kt`, add indices:

```kotlin
@Entity(
    tableName = "proxy_configs",
    indices = [
        Index(value = ["isEnabled"]),
        Index(value = ["type"]),
        Index(value = ["serverHost"])
    ]
)
```

## Edge Cases Handling

### 1. Network State Changes

```kotlin
// In NetworkManager.kt
private fun handleNetworkChange() {
    when (networkUtils.getNetworkType()) {
        NetworkType.WIFI -> {
            // Optimize for WiFi
            proxyManager.setConnectionTimeout(30_000)
        }
        NetworkType.MOBILE -> {
            // Optimize for mobile
            proxyManager.setConnectionTimeout(15_000)
        }
        NetworkType.NONE -> {
            // Handle offline state
            proxyManager.pauseConnections()
        }
    }
}
```

### 2. Low Battery Optimization

```kotlin
// In ProxyService.kt
private fun handleBatteryOptimization() {
    val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
    if (powerManager.isPowerSaveMode) {
        // Reduce background activity
        proxyManager.enablePowerSaveMode()
    }
}
```

### 3. VPN Permission Handling

```kotlin
// In ProxyViewModel.kt
private fun requestVpnPermission() {
    val intent = VpnService.prepare(context)
    if (intent != null) {
        // Request permission
        vpnPermissionLauncher.launch(intent)
    } else {
        // Permission already granted
        startVpnService()
    }
}
```

## Testing Fixes

### 1. Mock Hilt for Tests

Create `TestApplication.kt`:

```kotlin
@HiltAndroidApp
class TestApplication : Application()
```

### 2. Test Database

Use in-memory database for tests:

```kotlin
@Before
fun setUp() {
    database = Room.inMemoryDatabaseBuilder(
        ApplicationProvider.getApplicationContext(),
        ProxyDatabase::class.java
    ).allowMainThreadQueries().build()
}
```

### 3. Mock Network Calls

```kotlin
@Mock
private lateinit var mockWebServer: MockWebServer

@Before
fun setUp() {
    mockWebServer = MockWebServer()
    mockWebServer.start()
}
```

## Debugging Tips

### 1. Enable Logging

In `build.gradle.kts`:

```kotlin
android {
    buildTypes {
        debug {
            buildConfigField("boolean", "DEBUG_LOGGING", "true")
        }
        release {
            buildConfigField("boolean", "DEBUG_LOGGING", "false")
        }
    }
}
```

### 2. Network Debugging

```kotlin
if (BuildConfig.DEBUG_LOGGING) {
    val logging = HttpLoggingInterceptor()
    logging.setLevel(HttpLoggingInterceptor.Level.BODY)
    okHttpClient.addInterceptor(logging)
}
```

### 3. Database Debugging

```kotlin
if (BuildConfig.DEBUG) {
    Room.databaseBuilder(context, ProxyDatabase::class.java, "proxy_db")
        .setQueryCallback({ sqlQuery, bindArgs ->
            Log.d("RoomQuery", "SQL: $sqlQuery, Args: $bindArgs")
        }, Executors.newSingleThreadExecutor())
        .build()
}
```

## Build Commands

After applying fixes:

```bash
# Clean build
./gradlew clean

# Build debug
./gradlew assembleDebug

# Run tests
./gradlew test

# Run with specific JVM args
./gradlew assembleDebug -Dorg.gradle.jvmargs="-Xmx4g -XX:MaxMetaspaceSize=1g"
```

## Verification Steps

1. ✅ Build completes without errors
2. ✅ All tests pass
3. ✅ App installs and launches
4. ✅ Proxy connection works
5. ✅ Quick Settings tile functions
6. ✅ Auto-start works after reboot
7. ✅ Notifications display correctly
8. ✅ No memory leaks detected
