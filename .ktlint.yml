# Ktlint configuration file
# See: https://pinterest.github.io/ktlint/latest/rules/configuration-ktlint/

# Disable specific rules
disabled_rules:
  # Allow wildcard imports for certain packages
  - no-wildcard-imports
  # Allow unused imports (sometimes needed for annotations)
  - no-unused-imports
  # Allow package names with underscores (Android convention)
  - package-name

# Rule configurations
ktlint_code_style: android_studio

# Import ordering
ij_kotlin_imports_layout: |
  *
  |
  java.**
  |
  javax.**
  |
  kotlin.**
  |
  kotlinx.**
  |
  android.**
  |
  androidx.**
  |
  com.android.**
  |
  com.google.**
  |
  dagger.**
  |
  javax.inject.**

# Indentation
indent_style: space
indent_size: 4
continuation_indent_size: 4

# Line length
max_line_length: 120

# Trailing comma
ij_kotlin_allow_trailing_comma: true
ij_kotlin_allow_trailing_comma_on_call_site: true

# Function signature
ktlint_function_signature_body_expression_wrapping: default
ktlint_function_signature_rule_force_multiline_when_parameter_count_greater_or_equal_than: 3

# Chain wrapping
ktlint_chain_wrapping_ignore_when_parameter_count_greater_or_equal_than: 4

# Annotation formatting
ktlint_standard_annotation: enabled

# String template formatting
ktlint_standard_string-template: enabled

# Experimental rules
ktlint_experimental: enabled
