plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
    alias(libs.plugins.ktlint)
    alias(libs.plugins.dokka)
    alias(libs.plugins.jacoco)
}

android {
    namespace = "com.android.proxy_self"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.android.proxy_self"
        minSdk = 29
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            buildConfigField("boolean", "DEBUG_LOGGING", "true")
            buildConfigField("String", "LOG_TAG", "\"ProxyApp\"")
            isDebuggable = true
        }
        release {
            buildConfigField("boolean", "DEBUG_LOGGING", "false")
            buildConfigField("String", "LOG_TAG", "\"ProxyApp\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
            signingConfig = signingConfigs.getByName("debug") // Use debug signing for now
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
}

dependencies {
    // Core Android
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)

    // Compose BOM - sử dụng BOM để quản lý tất cả các phiên bản Compose
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.material.icons.extended)

    // Lifecycle & ViewModel
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.lifecycle.runtime.compose)

    // Navigation
    implementation(libs.androidx.navigation.compose)

    // Room Database
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // Hilt Dependency Injection
    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)
    implementation(libs.androidx.hilt.navigation.compose)

    // Coroutines
    implementation(libs.kotlinx.coroutines.android)

    // Serialization
    implementation(libs.kotlinx.serialization.json)

    // Networking (cho proxy functionality)
    implementation(libs.okhttp)
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)

    // Security & Encryption
    implementation(libs.androidx.security.crypto)

    // WorkManager (cho background tasks)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.androidx.hilt.work)

    // Testing
    testImplementation(libs.junit)
    testImplementation(libs.androidx.junit)
    testImplementation(libs.androidx.espresso.core)
    testImplementation(libs.androidx.ui.test.junit4)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.turbine)

    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)

    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}

// Ktlint configuration
ktlint {
    version.set("1.0.1")
    debug.set(true)
    verbose.set(true)
    android.set(true)
    outputToConsole.set(true)
    outputColorName.set("RED")
    ignoreFailures.set(false)

    filter {
        exclude("**/generated/**")
        include("**/kotlin/**")
    }

    // Create baseline to ignore existing violations
    baseline.set(file("ktlint-baseline.xml"))
}

// Dokka configuration
tasks.dokkaHtml.configure {
    outputDirectory.set(layout.buildDirectory.dir("dokka/html"))

    dokkaSourceSets {
        named("main") {
            moduleName.set("Proxy Self Android App")
            moduleVersion.set("1.0.0")

            // Include source code
            sourceLink {
                localDirectory.set(file("src/main/java"))
                remoteUrl.set(uri("https://github.com/DaoThiHuong2111/proxy_self/tree/main/app/src/main/java").toURL())
                remoteLineSuffix.set("#L")
            }

            // Package documentation
            perPackageOption {
                matchingRegex.set(".*\\.internal.*")
                suppress.set(true)
            }

            // External documentation links
            externalDocumentationLink {
                url.set(uri("https://developer.android.com/reference/").toURL())
            }

            externalDocumentationLink {
                url.set(uri("https://kotlinlang.org/api/latest/jvm/stdlib/").toURL())
            }
        }
    }
}

// Jacoco configuration for test coverage
jacoco {
    toolVersion = "0.8.10"
}

tasks.register<JacocoReport>("jacocoTestReport") {
    dependsOn("testDebugUnitTest")

    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }

    val fileFilter =
        listOf(
            "**/R.class",
            "**/R$*.class",
            "**/BuildConfig.*",
            "**/Manifest*.*",
            "**/*Test*.*",
            "android/**/*.*",
            "**/di/**",
            "**/hilt_aggregated_deps/**",
            "**/*_HiltModules*",
            "**/*_Factory*",
            "**/*_MembersInjector*",
            "**/Hilt_*",
        )

    val debugTree =
        fileTree("${layout.buildDirectory.get()}/intermediates/javac/debug/classes") {
            exclude(fileFilter)
        }

    val kotlinDebugTree =
        fileTree("${layout.buildDirectory.get()}/tmp/kotlin-classes/debug") {
            exclude(fileFilter)
        }

    classDirectories.setFrom(files(debugTree, kotlinDebugTree))
    executionData.setFrom(
        fileTree(layout.buildDirectory.get()) {
            include("**/*.exec", "**/*.ec")
        },
    )
    sourceDirectories.setFrom(files("${project.projectDir}/src/main/java"))
}

// Custom task to run all quality checks
tasks.register("qualityCheck") {
    dependsOn("ktlintCheck", "lintDebug", "testDebugUnitTest", "jacocoTestReport")
    description = "Run all quality checks including linting, tests, and coverage"
    group = "verification"
}
