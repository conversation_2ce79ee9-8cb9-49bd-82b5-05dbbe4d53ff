<resources>
    <string name="app_name">Proxy Self</string>
    <string name="tile_label">Proxy</string>

    <!-- Navigation -->
    <string name="nav_proxy_config">Proxy Config</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_about">About</string>

    <!-- Proxy Configuration Screen -->
    <string name="proxy_config_title">Proxy Configuration</string>
    <string name="proxy_status_connected">Connected</string>
    <string name="proxy_status_connecting">Connecting</string>
    <string name="proxy_status_disconnected">Disconnected</string>
    <string name="proxy_status_error">Error</string>
    <string name="proxy_type_label">Proxy Type</string>
    <string name="proxy_type_http">HTTP</string>
    <string name="proxy_type_https">HTTPS</string>
    <string name="proxy_type_socks">SOCKS</string>
    <string name="server_host_label">Server Host</string>
    <string name="server_host_hint">Enter server address</string>
    <string name="server_port_label">Server Port</string>
    <string name="server_port_hint">Enter port number</string>
    <string name="username_label">Username (Optional)</string>
    <string name="username_hint">Enter username</string>
    <string name="password_label">Password (Optional)</string>
    <string name="password_hint">Enter password</string>
    <string name="domains_label">Allowed Domains</string>
    <string name="domains_hint">Enter domains (one per line)</string>
    <string name="domains_help">Leave empty to allow all domains</string>
    <string name="btn_connect">Connect</string>
    <string name="btn_disconnect">Disconnect</string>
    <string name="btn_save_config">Save Configuration</string>
    <string name="btn_test_connection">Test Connection</string>

    <!-- Settings Screen -->
    <string name="settings_title">Settings</string>
    <string name="settings_general_title">General</string>
    <string name="settings_auto_start_title">Auto-start on Boot</string>
    <string name="settings_auto_start_summary">Automatically start proxy when device boots</string>
    <string name="settings_persistent_notification_title">Persistent Notification</string>
    <string name="settings_persistent_notification_summary">Show notification when proxy is active</string>
    <string name="settings_security_title">Security</string>
    <string name="settings_encrypt_credentials_title">Encrypt Credentials</string>
    <string name="settings_encrypt_credentials_summary">Store username and password securely</string>
    <string name="settings_advanced_title">Advanced</string>
    <string name="settings_connection_timeout_title">Connection Timeout</string>
    <string name="settings_connection_timeout_summary">Timeout for proxy connections (seconds)</string>
    <string name="settings_retry_attempts_title">Retry Attempts</string>
    <string name="settings_retry_attempts_summary">Number of connection retry attempts</string>
    <string name="settings_log_level_title">Log Level</string>
    <string name="settings_log_level_summary">Verbosity of application logs</string>
    <string name="settings_export_title">Export Configuration</string>
    <string name="settings_export_summary">Export proxy settings to file</string>
    <string name="settings_import_title">Import Configuration</string>
    <string name="settings_import_summary">Import proxy settings from file</string>
    <string name="settings_reset_title">Reset to Defaults</string>
    <string name="settings_reset_summary">Reset all settings to default values</string>

    <!-- About Screen -->
    <string name="about_title">About</string>
    <string name="about_app_name">Proxy Self</string>
    <string name="about_version">Version %s</string>
    <string name="about_description">A secure and efficient proxy application for Android devices. Built with Clean Architecture and modern Android development practices.</string>
    <string name="about_features_title">Features</string>
    <string name="about_feature_1">• HTTP/HTTPS/SOCKS proxy support</string>
    <string name="about_feature_2">• Domain-based filtering</string>
    <string name="about_feature_3">• Auto-start on boot</string>
    <string name="about_feature_4">• Quick Settings tile</string>
    <string name="about_feature_5">• Secure credential storage</string>
    <string name="about_developer_title">Developer</string>
    <string name="about_developer_name">Android Proxy Team</string>
    <string name="about_contact_title">Contact</string>
    <string name="about_contact_email"><EMAIL></string>
    <string name="about_privacy_title">Privacy Policy</string>
    <string name="about_privacy_description">We respect your privacy. This app does not collect or share personal data.</string>
    <string name="about_open_source_title">Open Source</string>
    <string name="about_open_source_description">This project is open source and available on GitHub.</string>
    <string name="about_license_title">License</string>
    <string name="about_license_description">Licensed under MIT License</string>

    <!-- Notifications -->
    <string name="notification_channel_name">Proxy Service</string>
    <string name="notification_channel_description">Notifications for proxy service status</string>
    <string name="notification_title_connected">Proxy Connected</string>
    <string name="notification_title_connecting">Connecting to Proxy</string>
    <string name="notification_title_reconnecting">Reconnecting to Proxy</string>
    <string name="notification_title_disconnected">Proxy Disconnected</string>
    <string name="notification_title_error">Proxy Error</string>
    <string name="notification_content_connected">Connected to %1$s:%2$d</string>
    <string name="notification_content_connected_simple">Proxy is active</string>
    <string name="notification_content_connecting">Establishing connection…</string>
    <string name="notification_content_reconnecting">Attempting to reconnect…</string>
    <string name="notification_content_disconnected">Proxy service stopped</string>
    <string name="notification_content_error">Connection failed</string>
    <string name="notification_action_stop">Stop</string>
    <string name="notification_action_open">Open</string>

    <!-- Messages and Errors -->
    <string name="error_invalid_host">Please enter a valid server host</string>
    <string name="error_invalid_port">Please enter a valid port number (1-65535)</string>
    <string name="error_connection_failed">Failed to connect to proxy server</string>
    <string name="error_authentication_failed">Authentication failed</string>
    <string name="error_network_unavailable">Network is not available</string>
    <string name="success_connection_established">Proxy connection established</string>
    <string name="success_configuration_saved">Configuration saved successfully</string>
    <string name="success_settings_exported">Settings exported successfully</string>
    <string name="success_settings_imported">Settings imported successfully</string>
    <string name="confirm_disconnect_title">Disconnect Proxy</string>
    <string name="confirm_disconnect_message">Are you sure you want to disconnect the proxy?</string>
    <string name="confirm_reset_title">Reset Settings</string>
    <string name="confirm_reset_message">This will reset all settings to default values. Continue?</string>
    <string name="btn_ok">OK</string>
    <string name="btn_cancel">Cancel</string>
    <string name="btn_yes">Yes</string>
    <string name="btn_no">No</string>
</resources>