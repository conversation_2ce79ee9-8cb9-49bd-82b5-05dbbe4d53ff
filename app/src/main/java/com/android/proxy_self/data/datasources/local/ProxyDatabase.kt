package com.android.proxy_self.data.datasources.local

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

/**
 * Room database for proxy application
 * Follows Single Responsibility Principle
 */
@Database(
    entities = [ProxyEntity::class],
    version = 1,
    exportSchema = false,
)
abstract class ProxyDatabase : RoomDatabase() {
    /**
     * Get ProxyDao instance
     */
    abstract fun proxyDao(): ProxyDao

    companion object {
        private const val DATABASE_NAME = "proxy_database"

        @Volatile
        private var INSTANCE: ProxyDatabase? = null

        /**
         * Get database instance using singleton pattern
         */
        fun getDatabase(context: Context): ProxyDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance =
                    Room.databaseBuilder(
                        context.applicationContext,
                        ProxyDatabase::class.java,
                        DATABASE_NAME,
                    )
                        .fallbackToDestructiveMigration() // For development, use proper migrations in production
                        .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
