package com.android.proxy_self.data.datasources.remote

import com.android.proxy_self.infrastructure.utils.DebugLogger
import com.android.proxy_self.infrastructure.utils.Socks5Client
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Authenticator
import okhttp3.Credentials
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Remote data source for proxy operations (future expansion)
 * Currently placeholder for potential cloud sync functionality
 * Implements Interface Segregation Principle
 */
@Singleton
class ProxyRemoteDataSource
    @Inject
    constructor() {
        /**
         * Test proxy connection remotely
         * @param serverAddress Proxy server address
         * @param serverPort Proxy server port
         * @param username Username for authentication
         * @param password Password for authentication
         * @param proxyType Type of proxy (SOCKS5/HTTP)
         * @return Boolean indicating connection success
         */
        suspend fun testProxyConnection(
            serverAddress: String,
            serverPort: Int,
            username: String,
            password: String,
            proxyType: String,
        ): Boolean = withContext(Dispatchers.IO) {
            DebugLogger.methodEntry(DebugLogger.Tags.NETWORK, "testProxyConnection",
                mapOf("serverAddress" to serverAddress, "serverPort" to serverPort, "proxyType" to proxyType))

            try {
                // Basic validation first
                if (serverAddress.isBlank() || serverPort !in 1..65535) {
                    DebugLogger.w(DebugLogger.Tags.NETWORK, "Invalid proxy parameters")
                    return@withContext false
                }

                when (proxyType.uppercase()) {
                    "SOCKS5" -> {
                        // Test SOCKS5 connection
                        val socks5Client = Socks5Client(
                            proxyHost = serverAddress,
                            proxyPort = serverPort,
                            username = username.takeIf { it.isNotBlank() },
                            password = password.takeIf { it.isNotBlank() }
                        )

                        val testSocket = socks5Client.connect("httpbin.org", 80)
                        val result = testSocket != null
                        testSocket?.close()

                        DebugLogger.d(DebugLogger.Tags.NETWORK, "SOCKS5 test result: $result")
                        result
                    }
                    "HTTP" -> {
                        // Test HTTP proxy connection
                        val proxy = Proxy(Proxy.Type.HTTP, InetSocketAddress(serverAddress, serverPort))
                        val client = OkHttpClient.Builder()
                            .proxy(proxy)
                            .authenticator(object : Authenticator {
                                override fun authenticate(route: Route?, response: Response): Request? {
                                    if (response.code == 407 && username.isNotBlank() && password.isNotBlank()) {
                                        val credential = Credentials.basic(username, password)
                                        return response.request.newBuilder()
                                            .header("Proxy-Authorization", credential)
                                            .build()
                                    }
                                    return null
                                }
                            })
                            .connectTimeout(10, TimeUnit.SECONDS)
                            .readTimeout(10, TimeUnit.SECONDS)
                            .build()

                        val request = Request.Builder()
                            .url("http://httpbin.org/ip")
                            .build()

                        val response = client.newCall(request).execute()
                        val result = response.isSuccessful
                        response.close()

                        DebugLogger.d(DebugLogger.Tags.NETWORK, "HTTP proxy test result: $result")
                        result
                    }
                    else -> {
                        DebugLogger.w(DebugLogger.Tags.NETWORK, "Unsupported proxy type: $proxyType")
                        false
                    }
                }
            } catch (e: Exception) {
                DebugLogger.e(DebugLogger.Tags.NETWORK, "Proxy connection test failed", e)
                false
            }
        }

        /**
         * Sync configuration to cloud (future feature)
         */
        suspend fun syncConfigToCloud(configData: String): Boolean {
            // TODO: Implement cloud sync functionality
            return false
        }

        /**
         * Get configuration from cloud (future feature)
         */
        suspend fun getConfigFromCloud(): String? {
            // TODO: Implement cloud sync functionality
            return null
        }
    }
