package com.android.proxy_self.data.datasources.local

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Local data source for proxy configuration operations
 * Implements Interface Segregation Principle
 */
@Singleton
class ProxyLocalDataSource
    @Inject
    constructor(
        private val proxyDao: ProxyDao,
    ) {
        /**
         * Get all proxy configurations
         */
        fun getAllConfigs(): Flow<List<ProxyEntity>> {
            return proxyDao.getAllConfigs()
        }

        /**
         * Get proxy configuration by ID
         */
        suspend fun getConfigById(id: Long): ProxyEntity? {
            return proxyDao.getConfigById(id)
        }

        /**
         * Get the latest proxy configuration
         */
        suspend fun getLatestConfig(): ProxyEntity? {
            return proxyDao.getLatestConfig()
        }

        /**
         * Get currently enabled configuration
         */
        suspend fun getEnabledConfig(): ProxyEntity? {
            return proxyDao.getEnabledConfig()
        }

        /**
         * Save proxy configuration
         */
        suspend fun saveConfig(config: ProxyEntity): Long {
            return proxyDao.insertConfig(config)
        }

        /**
         * Update proxy configuration
         */
        suspend fun updateConfig(config: ProxyEntity) {
            proxyDao.updateConfig(config)
        }

        /**
         * Delete proxy configuration
         */
        suspend fun deleteConfig(config: ProxyEntity) {
            proxyDao.deleteConfig(config)
        }

        /**
         * Delete proxy configuration by ID
         */
        suspend fun deleteConfigById(id: Long) {
            proxyDao.deleteConfigById(id)
        }

        /**
         * Delete all configurations
         */
        suspend fun deleteAllConfigs() {
            proxyDao.deleteAllConfigs()
        }

        /**
         * Enable specific configuration
         */
        suspend fun enableConfig(id: Long) {
            proxyDao.enableConfig(id)
        }

        /**
         * Disable all configurations
         */
        suspend fun disableAllConfigs() {
            proxyDao.disableAllConfigs()
        }

        /**
         * Get configuration count
         */
        suspend fun getConfigCount(): Int {
            return proxyDao.getConfigCount()
        }
    }
