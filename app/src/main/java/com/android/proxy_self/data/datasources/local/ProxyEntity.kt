package com.android.proxy_self.data.datasources.local

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Room entity for proxy configuration storage
 * Stores domains as comma-separated string for simplicity
 */
@Entity(tableName = "proxy_configs")
data class ProxyEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "server_address")
    val serverAddress: String,
    @ColumnInfo(name = "server_port")
    val serverPort: Int,
    @ColumnInfo(name = "username")
    val username: String,
    @ColumnInfo(name = "encrypted_password")
    val encryptedPassword: String, // Encrypted using Security Crypto
    @ColumnInfo(name = "proxy_type")
    val proxyType: String,
    @ColumnInfo(name = "domains")
    val domains: String, // Comma-separated domain list
    @ColumnInfo(name = "is_enabled")
    val isEnabled: <PERSON><PERSON><PERSON>,
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long,
)
