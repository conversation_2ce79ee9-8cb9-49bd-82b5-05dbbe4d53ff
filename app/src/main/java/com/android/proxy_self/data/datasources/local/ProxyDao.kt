package com.android.proxy_self.data.datasources.local

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for proxy configuration operations
 * Follows Repository pattern for data access abstraction
 */
@Dao
interface ProxyDao {
    /**
     * Get all proxy configurations
     */
    @Query("SELECT * FROM proxy_configs ORDER BY updated_at DESC")
    fun getAllConfigs(): Flow<List<ProxyEntity>>

    /**
     * Get proxy configuration by ID
     */
    @Query("SELECT * FROM proxy_configs WHERE id = :id")
    suspend fun getConfigById(id: Long): ProxyEntity?

    /**
     * Get the most recently updated configuration
     */
    @Query("SELECT * FROM proxy_configs ORDER BY updated_at DESC LIMIT 1")
    suspend fun getLatestConfig(): ProxyEntity?

    /**
     * Get currently enabled configuration
     */
    @Query("SELECT * FROM proxy_configs WHERE is_enabled = 1 LIMIT 1")
    suspend fun getEnabledConfig(): ProxyEntity?

    /**
     * Insert new proxy configuration
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConfig(config: ProxyEntity): Long

    /**
     * Update existing proxy configuration
     */
    @Update
    suspend fun updateConfig(config: ProxyEntity)

    /**
     * Delete proxy configuration
     */
    @Delete
    suspend fun deleteConfig(config: ProxyEntity)

    /**
     * Delete proxy configuration by ID
     */
    @Query("DELETE FROM proxy_configs WHERE id = :id")
    suspend fun deleteConfigById(id: Long)

    /**
     * Delete all proxy configurations
     */
    @Query("DELETE FROM proxy_configs")
    suspend fun deleteAllConfigs()

    /**
     * Disable all configurations
     */
    @Query("UPDATE proxy_configs SET is_enabled = 0")
    suspend fun disableAllConfigs()

    /**
     * Enable specific configuration and disable others
     */
    @Transaction
    suspend fun enableConfig(id: Long) {
        disableAllConfigs()
        enableConfigById(id)
    }

    /**
     * Enable configuration by ID
     */
    @Query("UPDATE proxy_configs SET is_enabled = 1, updated_at = :timestamp WHERE id = :id")
    suspend fun enableConfigById(
        id: Long,
        timestamp: Long = System.currentTimeMillis(),
    )

    /**
     * Get count of configurations
     */
    @Query("SELECT COUNT(*) FROM proxy_configs")
    suspend fun getConfigCount(): Int
}
