package com.android.proxy_self.data.repositories

import com.android.proxy_self.data.datasources.local.ProxyLocalDataSource
import com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource
import com.android.proxy_self.data.mappers.ProxyMapper
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.infrastructure.utils.EncryptionUtils
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of ProxyRepository
 * Follows Dependency Inversion Principle - implements domain interface
 */
@Singleton
class ProxyRepositoryImpl
    @Inject
    constructor(
        private val localDataSource: ProxyLocalDataSource,
        private val remoteDataSource: ProxyRemoteDataSource,
        private val mapper: ProxyMapper,
        private val encryptionUtils: EncryptionUtils,
    ) : ProxyRepository {
        // State flows for reactive data
        private val _proxyStatus =
            MutableStateFlow(
                ProxyStatusInfo(status = ProxyStatus.DISCONNECTED),
            )
        private val _isProxyEnabled = MutableStateFlow(false)

        override suspend fun getProxyConfig(): Flow<Result<ProxyConfig>> =
            flow {
                try {
                    val entity = localDataSource.getLatestConfig()
                    if (entity != null) {
                        val decryptedPassword = encryptionUtils.decrypt(entity.encryptedPassword)
                        val config = mapper.mapToDomain(entity, decryptedPassword)
                        emit(Result.success(config))
                    } else {
                        // Return default empty config if none exists
                        emit(Result.success(ProxyConfig()))
                    }
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val encryptedPassword = encryptionUtils.encrypt(config.password)
                    val entity = mapper.mapToEntity(config, encryptedPassword)

                    val savedId = localDataSource.saveConfig(entity)
                    emit(Result.success(savedId > 0))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    // TODO: Implement actual proxy service start logic
                    // This would involve starting the VPN service with the given configuration

                    // For now, simulate the operation
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.CONNECTING,
                            activeDomains = config.domains,
                        )

                    // Simulate connection delay
                    kotlinx.coroutines.delay(1000)

                    // Update status to connected
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.CONNECTED,
                            connectionTime = System.currentTimeMillis(),
                            activeDomains = config.domains,
                        )

                    _isProxyEnabled.value = true

                    // Enable the configuration in database
                    if (config.id > 0) {
                        localDataSource.enableConfig(config.id)
                    }

                    emit(Result.success(true))
                } catch (e: Exception) {
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.ERROR,
                            lastError = e.message,
                        )
                    emit(Result.failure(e))
                }
            }

        override suspend fun stopProxy(): Flow<Result<Boolean>> =
            flow {
                try {
                    // TODO: Implement actual proxy service stop logic

                    _proxyStatus.value = ProxyStatusInfo(status = ProxyStatus.DISCONNECTED)
                    _isProxyEnabled.value = false

                    // Disable all configurations
                    localDataSource.disableAllConfigs()

                    emit(Result.success(true))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val result =
                        remoteDataSource.testProxyConnection(
                            serverAddress = config.serverAddress,
                            serverPort = config.serverPort,
                            username = config.username,
                            password = config.password,
                            proxyType = config.proxyType.value,
                        )
                    emit(Result.success(result))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override fun getProxyStatus(): Flow<ProxyStatusInfo> {
            return _proxyStatus.asStateFlow()
        }

        override fun isProxyEnabled(): Flow<Boolean> {
            return _isProxyEnabled.asStateFlow()
        }

        override suspend fun getAllConfigs(): Flow<Result<List<ProxyConfig>>> {
            return localDataSource.getAllConfigs()
                .map { entities ->
                    try {
                        val configs =
                            mapper.mapToDomainList(entities) { encryptedPassword ->
                                encryptionUtils.decrypt(encryptedPassword)
                            }
                        Result.success(configs)
                    } catch (e: Exception) {
                        Result.failure(e)
                    }
                }
                .catch { e ->
                    emit(Result.failure(e))
                }
        }

        override suspend fun deleteConfig(configId: Long): Flow<Result<Boolean>> =
            flow {
                try {
                    localDataSource.deleteConfigById(configId)
                    emit(Result.success(true))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val encryptedPassword = encryptionUtils.encrypt(config.password)
                    val entity = mapper.mapToEntity(config, encryptedPassword)
                    val updatedEntity = mapper.updateTimestamp(entity)

                    localDataSource.updateConfig(updatedEntity)
                    emit(Result.success(true))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }
    }
