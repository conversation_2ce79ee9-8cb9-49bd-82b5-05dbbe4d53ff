package com.android.proxy_self.data.repositories

import android.content.Context
import android.content.Intent
import com.android.proxy_self.data.datasources.local.ProxyLocalDataSource
import com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource
import com.android.proxy_self.data.mappers.ProxyMapper
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.infrastructure.services.ProxyService
import com.android.proxy_self.infrastructure.utils.DebugLogger
import com.android.proxy_self.infrastructure.utils.EncryptionUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of ProxyRepository
 * Follows Dependency Inversion Principle - implements domain interface
 */
@Singleton
class ProxyRepositoryImpl
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val localDataSource: ProxyLocalDataSource,
        private val remoteDataSource: ProxyRemoteDataSource,
        private val mapper: ProxyMapper,
        private val encryptionUtils: EncryptionUtils,
    ) : ProxyRepository {
        // State flows for reactive data
        private val _proxyStatus =
            MutableStateFlow(
                ProxyStatusInfo(status = ProxyStatus.DISCONNECTED),
            )
        private val _isProxyEnabled = MutableStateFlow(false)

        override suspend fun getProxyConfig(): Flow<Result<ProxyConfig>> =
            flow {
                try {
                    val entity = localDataSource.getLatestConfig()
                    if (entity != null) {
                        val decryptedPassword = encryptionUtils.decrypt(entity.encryptedPassword)
                        val config = mapper.mapToDomain(entity, decryptedPassword)
                        emit(Result.success(config))
                    } else {
                        // Return default empty config if none exists
                        emit(Result.success(ProxyConfig()))
                    }
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val encryptedPassword = encryptionUtils.encrypt(config.password)
                    val entity = mapper.mapToEntity(config, encryptedPassword)

                    val savedId = localDataSource.saveConfig(entity)
                    emit(Result.success(savedId > 0))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    DebugLogger.methodEntry(DebugLogger.Tags.REPOSITORY, "startProxy",
                        mapOf("serverAddress" to config.serverAddress, "serverPort" to config.serverPort, "domains" to config.domains.size))

                    // Update status to connecting
                    DebugLogger.stateChange(DebugLogger.Tags.REPOSITORY, ProxyStatus.DISCONNECTED, ProxyStatus.CONNECTING, "startProxy")
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.CONNECTING,
                            activeDomains = config.domains,
                        )

                    // Start the actual proxy service
                    DebugLogger.d(DebugLogger.Tags.REPOSITORY, "Starting ProxyService with intent")
                    val intent = Intent(context, ProxyService::class.java).apply {
                        action = ProxyService.ACTION_START_PROXY
                        putExtra(ProxyService.EXTRA_PROXY_CONFIG, config)
                    }
                    context.startForegroundService(intent)

                    // Give service time to start
                    DebugLogger.d(DebugLogger.Tags.REPOSITORY, "Waiting for service to start...")
                    kotlinx.coroutines.delay(2000)

                    // Update status to connected (service will update this via its own state)
                    DebugLogger.stateChange(DebugLogger.Tags.REPOSITORY, ProxyStatus.CONNECTING, ProxyStatus.CONNECTED, "startProxy")
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.CONNECTED,
                            connectionTime = System.currentTimeMillis(),
                            activeDomains = config.domains,
                        )

                    _isProxyEnabled.value = true

                    // Enable the configuration in database
                    if (config.id > 0) {
                        DebugLogger.databaseOperation(DebugLogger.Tags.REPOSITORY, "UPDATE", "proxy_config", "enable config id=${config.id}")
                        localDataSource.enableConfig(config.id)
                    }

                    DebugLogger.methodExit(DebugLogger.Tags.REPOSITORY, "startProxy", "success")
                    emit(Result.success(true))
                } catch (e: Exception) {
                    DebugLogger.e(DebugLogger.Tags.REPOSITORY, "Failed to start proxy service", e)
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.ERROR,
                            lastError = e.message,
                        )
                    emit(Result.failure(e))
                }
            }

        override suspend fun stopProxy(): Flow<Result<Boolean>> =
            flow {
                try {
                    android.util.Log.d("ProxyRepositoryImpl", "Stopping proxy service")

                    // Stop the actual proxy service
                    val intent = Intent(context, ProxyService::class.java).apply {
                        action = ProxyService.ACTION_STOP_PROXY
                    }
                    context.startService(intent)

                    _proxyStatus.value = ProxyStatusInfo(status = ProxyStatus.DISCONNECTED)
                    _isProxyEnabled.value = false

                    // Disable all configurations
                    localDataSource.disableAllConfigs()

                    android.util.Log.d("ProxyRepositoryImpl", "Proxy service stopped successfully")
                    emit(Result.success(true))
                } catch (e: Exception) {
                    android.util.Log.e("ProxyRepositoryImpl", "Failed to stop proxy service", e)
                    emit(Result.failure(e))
                }
            }

        override suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val result =
                        remoteDataSource.testProxyConnection(
                            serverAddress = config.serverAddress,
                            serverPort = config.serverPort,
                            username = config.username,
                            password = config.password,
                            proxyType = config.proxyType.value,
                        )
                    emit(Result.success(result))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override fun getProxyStatus(): Flow<ProxyStatusInfo> {
            return _proxyStatus.asStateFlow()
        }

        override fun isProxyEnabled(): Flow<Boolean> {
            return _isProxyEnabled.asStateFlow()
        }

        override suspend fun getAllConfigs(): Flow<Result<List<ProxyConfig>>> {
            return localDataSource.getAllConfigs()
                .map { entities ->
                    try {
                        val configs =
                            mapper.mapToDomainList(entities) { encryptedPassword ->
                                encryptionUtils.decrypt(encryptedPassword)
                            }
                        Result.success(configs)
                    } catch (e: Exception) {
                        Result.failure(e)
                    }
                }
                .catch { e ->
                    emit(Result.failure(e))
                }
        }

        override suspend fun deleteConfig(configId: Long): Flow<Result<Boolean>> =
            flow {
                try {
                    localDataSource.deleteConfigById(configId)
                    emit(Result.success(true))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }

        override suspend fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>> =
            flow {
                try {
                    val encryptedPassword = encryptionUtils.encrypt(config.password)
                    val entity = mapper.mapToEntity(config, encryptedPassword)
                    val updatedEntity = mapper.updateTimestamp(entity)

                    localDataSource.updateConfig(updatedEntity)
                    emit(Result.success(true))
                } catch (e: Exception) {
                    emit(Result.failure(e))
                }
            }
    }
