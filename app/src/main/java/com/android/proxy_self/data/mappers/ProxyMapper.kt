package com.android.proxy_self.data.mappers

import com.android.proxy_self.data.datasources.local.ProxyEntity
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Mapper class for converting between data and domain entities
 * Implements Single Responsibility Principle
 */
@Singleton
class ProxyMapper
    @Inject
    constructor() {
        /**
         * Convert ProxyEntity to ProxyConfig (data layer to domain layer)
         * Handles decryption of password and parsing of domains
         */
        fun mapToDomain(
            entity: ProxyEntity,
            decryptedPassword: String,
        ): ProxyConfig {
            val domains =
                if (entity.domains.isBlank()) {
                    emptyList()
                } else {
                    entity.domains.split(",")
                        .map { it.trim() }
                        .filter { it.isNotBlank() }
                }

            return ProxyConfig(
                id = entity.id,
                serverAddress = entity.serverAddress,
                serverPort = entity.serverPort,
                username = entity.username,
                password = decryptedPassword,
                proxyType = ProxyType.fromValue(entity.proxyType),
                domains = domains,
                isEnabled = entity.isEnabled,
                createdAt = entity.createdAt,
                updatedAt = entity.updatedAt,
            )
        }

        /**
         * Convert ProxyConfig to ProxyEntity (domain layer to data layer)
         * Handles encryption of password and serialization of domains
         */
        fun mapToEntity(
            config: ProxyConfig,
            encryptedPassword: String,
        ): ProxyEntity {
            val domainsString = config.domains.joinToString(",")

            return ProxyEntity(
                id = config.id,
                serverAddress = config.serverAddress,
                serverPort = config.serverPort,
                username = config.username,
                encryptedPassword = encryptedPassword,
                proxyType = config.proxyType.value,
                domains = domainsString,
                isEnabled = config.isEnabled,
                createdAt = config.createdAt,
                updatedAt = config.updatedAt,
            )
        }

        /**
         * Convert list of ProxyEntity to list of ProxyConfig
         */
        fun mapToDomainList(
            entities: List<ProxyEntity>,
            decryptPassword: (String) -> String,
        ): List<ProxyConfig> {
            return entities.map { entity ->
                val decryptedPassword = decryptPassword(entity.encryptedPassword)
                mapToDomain(entity, decryptedPassword)
            }
        }

        /**
         * Create new ProxyEntity with updated timestamp
         */
        fun updateTimestamp(entity: ProxyEntity): ProxyEntity {
            return entity.copy(updatedAt = System.currentTimeMillis())
        }
    }
