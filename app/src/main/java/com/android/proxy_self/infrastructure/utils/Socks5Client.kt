package com.android.proxy_self.infrastructure.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.DataInputStream
import java.io.DataOutputStream
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.Socket
import java.nio.ByteBuffer

/**
 * SOCKS5 client implementation for proxy connections
 * Implements RFC 1928 SOCKS Protocol Version 5
 */
class Socks5Client(
    private val proxyHost: String,
    private val proxyPort: Int,
    private val username: String? = null,
    private val password: String? = null,
) {
    companion object {
        private const val TAG = "Socks5Client"
        
        // SOCKS5 Protocol Constants
        private const val SOCKS_VERSION = 0x05
        private const val NO_AUTH = 0x00
        private const val USERNAME_PASSWORD_AUTH = 0x02
        private const val CONNECT_COMMAND = 0x01
        private const val IPV4_ADDRESS_TYPE = 0x01
        private const val DOMAIN_ADDRESS_TYPE = 0x03
        private const val IPV6_ADDRESS_TYPE = 0x04
        
        // Response codes
        private const val SUCCESS = 0x00
        private const val GENERAL_FAILURE = 0x01
        private const val CONNECTION_NOT_ALLOWED = 0x02
        private const val NETWORK_UNREACHABLE = 0x03
        private const val HOST_UNREACHABLE = 0x04
        private const val CONNECTION_REFUSED = 0x05
        private const val TTL_EXPIRED = 0x06
        private const val COMMAND_NOT_SUPPORTED = 0x07
        private const val ADDRESS_TYPE_NOT_SUPPORTED = 0x08
    }

    /**
     * Create SOCKS5 connection to target host
     */
    suspend fun connect(targetHost: String, targetPort: Int): Socket? = withContext(Dispatchers.IO) {
        try {
            DebugLogger.methodEntry(DebugLogger.Tags.SOCKS5, "connect",
                mapOf("proxyHost" to proxyHost, "proxyPort" to proxyPort, "targetHost" to targetHost, "targetPort" to targetPort))

            val socket = Socket()
            socket.connect(InetSocketAddress(proxyHost, proxyPort), 10000) // 10s timeout
            DebugLogger.d(DebugLogger.Tags.SOCKS5, "TCP connection established to proxy")

            val input = DataInputStream(socket.getInputStream())
            val output = DataOutputStream(socket.getOutputStream())

            // Step 1: Authentication negotiation
            if (!performAuthentication(input, output)) {
                DebugLogger.e(DebugLogger.Tags.SOCKS5, "Authentication failed")
                socket.close()
                return@withContext null
            }

            // Step 2: Connection request
            if (!performConnection(input, output, targetHost, targetPort)) {
                DebugLogger.e(DebugLogger.Tags.SOCKS5, "Connection request failed")
                socket.close()
                return@withContext null
            }

            DebugLogger.methodExit(DebugLogger.Tags.SOCKS5, "connect", "success")
            socket
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.SOCKS5, "Failed to establish SOCKS5 connection", e)
            null
        }
    }

    /**
     * Perform SOCKS5 authentication
     */
    private fun performAuthentication(input: DataInputStream, output: DataOutputStream): Boolean {
        return try {
            // Send authentication methods
            val authMethods = if (username != null && password != null) {
                byteArrayOf(SOCKS_VERSION.toByte(), 2, NO_AUTH.toByte(), USERNAME_PASSWORD_AUTH.toByte())
            } else {
                byteArrayOf(SOCKS_VERSION.toByte(), 1, NO_AUTH.toByte())
            }
            
            output.write(authMethods)
            output.flush()
            
            // Read server response
            val version = input.readByte()
            val method = input.readByte()
            
            if (version != SOCKS_VERSION.toByte()) {
                Log.e(TAG, "Invalid SOCKS version: $version")
                return false
            }
            
            when (method.toInt() and 0xFF) {
                NO_AUTH -> {
                    Log.d(TAG, "No authentication required")
                    true
                }
                USERNAME_PASSWORD_AUTH -> {
                    Log.d(TAG, "Username/password authentication required")
                    performUsernamePasswordAuth(input, output)
                }
                else -> {
                    Log.e(TAG, "Unsupported authentication method: $method")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Authentication failed", e)
            false
        }
    }

    /**
     * Perform username/password authentication
     */
    private fun performUsernamePasswordAuth(input: DataInputStream, output: DataOutputStream): Boolean {
        return try {
            if (username == null || password == null) {
                Log.e(TAG, "Username/password required but not provided")
                return false
            }
            
            // Send credentials
            val credentials = ByteBuffer.allocate(3 + username.length + password.length)
            credentials.put(0x01) // Auth version
            credentials.put(username.length.toByte())
            credentials.put(username.toByteArray())
            credentials.put(password.length.toByte())
            credentials.put(password.toByteArray())
            
            output.write(credentials.array())
            output.flush()
            
            // Read response
            val authVersion = input.readByte()
            val status = input.readByte()
            
            if (status == 0x00.toByte()) {
                Log.d(TAG, "Authentication successful")
                true
            } else {
                Log.e(TAG, "Authentication failed with status: $status")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Username/password authentication failed", e)
            false
        }
    }

    /**
     * Perform SOCKS5 connection request
     */
    private fun performConnection(
        input: DataInputStream,
        output: DataOutputStream,
        targetHost: String,
        targetPort: Int
    ): Boolean {
        return try {
            // Build connection request
            val request = buildConnectionRequest(targetHost, targetPort)
            output.write(request)
            output.flush()
            
            // Read response
            val version = input.readByte()
            val reply = input.readByte()
            val reserved = input.readByte()
            val addressType = input.readByte()
            
            if (version != SOCKS_VERSION.toByte()) {
                Log.e(TAG, "Invalid SOCKS version in response: $version")
                return false
            }
            
            if (reply != SUCCESS.toByte()) {
                Log.e(TAG, "Connection failed with reply code: ${reply.toInt() and 0xFF}")
                return false
            }
            
            // Skip bound address and port
            when (addressType.toInt() and 0xFF) {
                IPV4_ADDRESS_TYPE -> {
                    input.skipBytes(4) // IPv4 address
                    input.skipBytes(2) // Port
                }
                IPV6_ADDRESS_TYPE -> {
                    input.skipBytes(16) // IPv6 address
                    input.skipBytes(2) // Port
                }
                DOMAIN_ADDRESS_TYPE -> {
                    val domainLength = input.readByte().toInt() and 0xFF
                    input.skipBytes(domainLength) // Domain name
                    input.skipBytes(2) // Port
                }
            }
            
            Log.d(TAG, "SOCKS5 connection request successful")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Connection request failed", e)
            false
        }
    }

    /**
     * Build SOCKS5 connection request packet
     */
    private fun buildConnectionRequest(targetHost: String, targetPort: Int): ByteArray {
        val isIpAddress = try {
            InetAddress.getByName(targetHost)
            true
        } catch (e: Exception) {
            false
        }
        
        return if (isIpAddress) {
            // IP address
            val address = InetAddress.getByName(targetHost)
            if (address.address.size == 4) {
                // IPv4
                ByteBuffer.allocate(10).apply {
                    put(SOCKS_VERSION.toByte())
                    put(CONNECT_COMMAND.toByte())
                    put(0x00) // Reserved
                    put(IPV4_ADDRESS_TYPE.toByte())
                    put(address.address)
                    putShort(targetPort.toShort())
                }.array()
            } else {
                // IPv6
                ByteBuffer.allocate(22).apply {
                    put(SOCKS_VERSION.toByte())
                    put(CONNECT_COMMAND.toByte())
                    put(0x00) // Reserved
                    put(IPV6_ADDRESS_TYPE.toByte())
                    put(address.address)
                    putShort(targetPort.toShort())
                }.array()
            }
        } else {
            // Domain name
            val hostBytes = targetHost.toByteArray()
            ByteBuffer.allocate(7 + hostBytes.size).apply {
                put(SOCKS_VERSION.toByte())
                put(CONNECT_COMMAND.toByte())
                put(0x00) // Reserved
                put(DOMAIN_ADDRESS_TYPE.toByte())
                put(hostBytes.size.toByte())
                put(hostBytes)
                putShort(targetPort.toShort())
            }.array()
        }
    }
}
