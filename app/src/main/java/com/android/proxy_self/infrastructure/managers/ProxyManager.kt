package com.android.proxy_self.infrastructure.managers

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import com.android.proxy_self.infrastructure.utils.NetworkUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Authenticator
import okhttp3.Credentials
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager class for proxy operations
 * Handles SOCKS5 and HTTP proxy connections with authentication
 * Implements Single Responsibility Principle
 */
@Singleton
class ProxyManager
    @Inject
    constructor(
        private val networkUtils: NetworkUtils,
    ) {
        /**
         * Test proxy connection with given configuration
         */
        suspend fun testProxyConnection(config: ProxyConfig): Boolean =
            withContext(Dispatchers.IO) {
                try {
                    val proxy = createProxy(config)
                    val client = createHttpClient(config, proxy)

                    // Test with a simple HTTP request
                    val request =
                        Request.Builder()
                            .url("http://httpbin.org/ip") // Simple endpoint to test connectivity
                            .build()

                    val response = client.newCall(request).execute()
                    response.use {
                        it.isSuccessful
                    }
                } catch (e: Exception) {
                    false
                }
            }

        /**
         * Check if domain should use proxy based on whitelist
         */
        fun shouldUseProxy(
            domain: String,
            whitelist: List<String>,
        ): Boolean {
            return networkUtils.isDomainInWhitelist(domain, whitelist)
        }

        /**
         * Create proxy instance based on configuration
         */
        private fun createProxy(config: ProxyConfig): Proxy {
            val proxyType =
                when (config.proxyType) {
                    ProxyType.HTTP -> Proxy.Type.HTTP
                    ProxyType.SOCKS5 -> Proxy.Type.SOCKS
                }

            val socketAddress = InetSocketAddress(config.serverAddress, config.serverPort)
            return Proxy(proxyType, socketAddress)
        }

        /**
         * Create HTTP client with proxy configuration
         */
        private fun createHttpClient(
            config: ProxyConfig,
            proxy: Proxy,
        ): OkHttpClient {
            return OkHttpClient.Builder()
                .proxy(proxy)
                .authenticator(createProxyAuthenticator(config))
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build()
        }

        /**
         * Create proxy authenticator for authentication
         */
        private fun createProxyAuthenticator(config: ProxyConfig): Authenticator {
            return object : Authenticator {
                override fun authenticate(
                    route: Route?,
                    response: Response,
                ): Request? {
                    // Check if this is a proxy authentication challenge
                    if (response.code == 407) {
                        val credential = Credentials.basic(config.username, config.password)
                        return response.request.newBuilder()
                            .header("Proxy-Authorization", credential)
                            .build()
                    }
                    return null
                }
            }
        }

        /**
         * Validate proxy configuration
         */
        fun validateProxyConfig(config: ProxyConfig): ProxyValidationResult {
            val errors = mutableListOf<String>()

            // Validate server address
            if (config.serverAddress.isBlank()) {
                errors.add("Server address cannot be empty")
            }

            // Validate port
            if (config.serverPort !in 1..65535) {
                errors.add("Port must be between 1 and 65535")
            }

            // Validate credentials
            if (config.username.isBlank()) {
                errors.add("Username cannot be empty")
            }

            if (config.password.isBlank()) {
                errors.add("Password cannot be empty")
            }

            // Validate domains
            if (config.domains.isEmpty()) {
                errors.add("At least one domain must be specified")
            }

            return if (errors.isEmpty()) {
                ProxyValidationResult.Valid
            } else {
                ProxyValidationResult.Invalid(errors)
            }
        }

        /**
         * Get proxy connection info for debugging
         */
        fun getProxyConnectionInfo(config: ProxyConfig): ProxyConnectionInfo {
            return ProxyConnectionInfo(
                serverAddress = config.serverAddress,
                serverPort = config.serverPort,
                proxyType = config.proxyType.displayName,
                username = config.username,
                domainCount = config.domains.size,
                domains = config.domains,
            )
        }

        /**
         * Create SOCKS proxy for VPN service (placeholder)
         */
        suspend fun createSocksProxy(config: ProxyConfig): SocksProxyResult {
            return try {
                // TODO: Implement actual SOCKS proxy creation for VPN service
                // This would involve creating a SOCKS proxy server that the VPN service can use

                SocksProxyResult.Success("SOCKS proxy created successfully")
            } catch (e: Exception) {
                SocksProxyResult.Error(e.message ?: "Unknown error")
            }
        }

        /**
         * Stop SOCKS proxy (placeholder)
         */
        suspend fun stopSocksProxy(): Boolean {
            return try {
                // TODO: Implement actual SOCKS proxy stopping
                true
            } catch (e: Exception) {
                false
            }
        }
    }

/**
 * Sealed class for proxy validation results
 */
sealed class ProxyValidationResult {
    object Valid : ProxyValidationResult()

    data class Invalid(val errors: List<String>) : ProxyValidationResult()
}

/**
 * Data class for proxy connection information
 */
data class ProxyConnectionInfo(
    val serverAddress: String,
    val serverPort: Int,
    val proxyType: String,
    val username: String,
    val domainCount: Int,
    val domains: List<String>,
)

/**
 * Sealed class for SOCKS proxy results
 */
sealed class SocksProxyResult {
    data class Success(val message: String) : SocksProxyResult()

    data class Error(val message: String) : SocksProxyResult()
}
