package com.android.proxy_self.infrastructure.utils

import android.content.Context
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import java.io.IOException
import java.security.GeneralSecurityException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for encryption/decryption operations
 * Uses Android Security Crypto library for AES-256 encryption
 * Implements Single Responsibility Principle
 */
@Singleton
class EncryptionUtils
    @Inject
    constructor(
        private val context: Context,
    ) {
        private val masterKey: MasterKey by lazy {
            MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()
        }

        private val encryptedSharedPreferences by lazy {
            EncryptedSharedPreferences.create(
                context,
                "proxy_encrypted_prefs",
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
            )
        }

        /**
         * Encrypt a string value
         * @param plainText The text to encrypt
         * @return Encrypted string or original text if encryption fails
         */
        fun encrypt(plainText: String): String {
            return try {
                if (plainText.isBlank()) return plainText

                // Generate a unique key for this encryption
                val key = "encrypted_${System.currentTimeMillis()}_${plainText.hashCode()}"

                // Store the encrypted value in EncryptedSharedPreferences
                encryptedSharedPreferences.edit()
                    .putString(key, plainText)
                    .apply()

                // Return the key as the "encrypted" value
                key
            } catch (e: GeneralSecurityException) {
                // Log error in production, return original for now
                plainText
            } catch (e: IOException) {
                // Log error in production, return original for now
                plainText
            }
        }

        /**
         * Decrypt a string value
         * @param encryptedText The encrypted text (actually the key)
         * @return Decrypted string or original text if decryption fails
         */
        fun decrypt(encryptedText: String): String {
            return try {
                if (encryptedText.isBlank()) return encryptedText

                // If it doesn't look like our encrypted key format, return as-is
                if (!encryptedText.startsWith("encrypted_")) {
                    return encryptedText
                }

                // Retrieve the original value using the key
                encryptedSharedPreferences.getString(encryptedText, encryptedText) ?: encryptedText
            } catch (e: GeneralSecurityException) {
                // Log error in production, return original for now
                encryptedText
            } catch (e: IOException) {
                // Log error in production, return original for now
                encryptedText
            }
        }

        /**
         * Clear all encrypted data
         */
        fun clearAllEncryptedData() {
            try {
                encryptedSharedPreferences.edit().clear().apply()
            } catch (e: Exception) {
                // Log error in production
            }
        }

        /**
         * Check if encryption is available
         */
        fun isEncryptionAvailable(): Boolean {
            return try {
                // Test encryption/decryption
                val testText = "test"
                val encrypted = encrypt(testText)
                val decrypted = decrypt(encrypted)
                decrypted == testText
            } catch (e: Exception) {
                false
            }
        }
    }
