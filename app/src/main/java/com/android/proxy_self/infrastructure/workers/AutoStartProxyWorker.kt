package com.android.proxy_self.infrastructure.workers

import android.content.Context
import android.content.Intent
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.android.proxy_self.domain.usecases.GetProxyConfigUseCase
import com.android.proxy_self.infrastructure.services.ProxyService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first

/**
 * WorkManager worker for auto-starting proxy after device boot
 * Implements delayed start with proper error handling
 * Uses Hilt for dependency injection
 */
@HiltWorker
class AutoStartProxyWorker
    @AssistedInject
    constructor(
        @Assisted context: Context,
        @Assisted workerParams: WorkerParameters,
        private val getProxyConfigUseCase: GetProxyConfigUseCase,
    ) : CoroutineWorker(context, workerParams) {
        override suspend fun doWork(): Result {
            return try {
                // Get saved proxy configuration
                getProxyConfigUseCase().first().fold(
                    onSuccess = { config ->
                        if (config.isValid() && config.isEnabled) {
                            // Start proxy service
                            val intent =
                                Intent(applicationContext, ProxyService::class.java).apply {
                                    action = ProxyService.ACTION_START_PROXY
                                    putExtra(ProxyService.EXTRA_PROXY_CONFIG, config)
                                }

                            applicationContext.startForegroundService(intent)
                            Result.success()
                        } else {
                            // Configuration is invalid or disabled
                            Result.success() // Don't retry
                        }
                    },
                    onFailure = { error ->
                        // No configuration found or error occurred
                        Result.failure()
                    },
                )
            } catch (e: Exception) {
                // Error occurred during auto-start
                Result.retry()
            }
        }
    }
