package com.android.proxy_self.infrastructure.managers

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import com.android.proxy_self.infrastructure.utils.NetworkUtils
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager class for network monitoring and operations
 * Implements Single Responsibility Principle
 */
@Singleton
class NetworkManager
    @Inject
    constructor(
        private val context: Context,
        private val networkUtils: NetworkUtils,
    ) {
        private val connectivityManager: ConnectivityManager by lazy {
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        }

        /**
         * Observe network connectivity changes
         */
        fun observeNetworkConnectivity(): Flow<NetworkState> =
            callbackFlow {
                val networkCallback =
                    object : ConnectivityManager.NetworkCallback() {
                        override fun onAvailable(network: Network) {
                            trySend(NetworkState.Available)
                        }

                        override fun onLost(network: Network) {
                            trySend(NetworkState.Lost)
                        }

                        override fun onCapabilitiesChanged(
                            network: Network,
                            networkCapabilities: NetworkCapabilities,
                        ) {
                            val hasInternet =
                                networkCapabilities.hasCapability(
                                    NetworkCapabilities.NET_CAPABILITY_INTERNET,
                                )
                            val hasValidated =
                                networkCapabilities.hasCapability(
                                    NetworkCapabilities.NET_CAPABILITY_VALIDATED,
                                )

                            if (hasInternet && hasValidated) {
                                trySend(NetworkState.Available)
                            } else {
                                trySend(NetworkState.Limited)
                            }
                        }
                    }

                val networkRequest =
                    NetworkRequest.Builder()
                        .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                        .build()

                connectivityManager.registerNetworkCallback(networkRequest, networkCallback)

                // Send initial state
                trySend(getCurrentNetworkState())

                awaitClose {
                    connectivityManager.unregisterNetworkCallback(networkCallback)
                }
            }.distinctUntilChanged()

        /**
         * Get current network state
         */
        fun getCurrentNetworkState(): NetworkState {
            return if (networkUtils.isNetworkAvailable()) {
                NetworkState.Available
            } else {
                NetworkState.Lost
            }
        }

        /**
         * Get detailed network information
         */
        fun getNetworkInfo(): NetworkInfo {
            val info = networkUtils.getNetworkInfo()
            return NetworkInfo(
                isConnected = info.isAvailable,
                connectionType = info.type,
                isWifi = info.isWifi,
                isCellular = info.isCellular,
                isMetered = info.isMetered,
                timestamp = System.currentTimeMillis(),
            )
        }

        /**
         * Check if specific host is reachable
         */
        suspend fun isHostReachable(
            host: String,
            port: Int,
            timeoutMs: Int = 5000,
        ): Boolean {
            return networkUtils.isHostReachable(host, port, timeoutMs)
        }

        /**
         * Extract domain from URL
         */
        fun extractDomain(url: String): String? {
            return networkUtils.extractDomainFromUrl(url)
        }

        /**
         * Check if domain should be routed through proxy
         */
        fun shouldRouteThoughProxy(
            domain: String,
            whitelist: List<String>,
        ): Boolean {
            return networkUtils.isDomainInWhitelist(domain, whitelist)
        }

        /**
         * Get active network capabilities
         */
        fun getActiveNetworkCapabilities(): NetworkCapabilities? {
            val activeNetwork = connectivityManager.activeNetwork ?: return null
            return connectivityManager.getNetworkCapabilities(activeNetwork)
        }

        /**
         * Check if VPN is active
         */
        fun isVpnActive(): Boolean {
            val capabilities = getActiveNetworkCapabilities() ?: return false
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN)
        }

        /**
         * Get network statistics (placeholder for future implementation)
         */
        fun getNetworkStats(): NetworkStats {
            return NetworkStats(
                bytesReceived = 0L, // TODO: Implement actual network stats
                bytesSent = 0L,
                packetsReceived = 0L,
                packetsSent = 0L,
                timestamp = System.currentTimeMillis(),
            )
        }
    }

/**
 * Enum representing network states
 */
enum class NetworkState {
    Available,
    Limited,
    Lost,
}

/**
 * Data class for network information
 */
data class NetworkInfo(
    val isConnected: Boolean,
    val connectionType: String,
    val isWifi: Boolean,
    val isCellular: Boolean,
    val isMetered: Boolean,
    val timestamp: Long,
)

/**
 * Data class for network statistics
 */
data class NetworkStats(
    val bytesReceived: Long,
    val bytesSent: Long,
    val packetsReceived: Long,
    val packetsSent: Long,
    val timestamp: Long,
)
