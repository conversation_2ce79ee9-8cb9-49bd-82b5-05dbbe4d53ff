package com.android.proxy_self.infrastructure.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for network operations
 * Implements Single Responsibility Principle
 */
@Singleton
class NetworkUtils
    @Inject
    constructor(
        private val context: Context,
    ) {
        private val connectivityManager: ConnectivityManager by lazy {
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        }

        /**
         * Check if device has internet connection
         */
        fun isNetworkAvailable(): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        }

        /**
         * Check if device is connected to WiFi
         */
        fun isWifiConnected(): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
            }
        }

        /**
         * Check if device is connected to cellular network
         */
        fun isCellularConnected(): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.type == ConnectivityManager.TYPE_MOBILE && networkInfo.isConnected
            }
        }

        /**
         * Get current network type as string
         */
        fun getNetworkType(): String {
            return when {
                isWifiConnected() -> "WiFi"
                isCellularConnected() -> "Cellular"
                isNetworkAvailable() -> "Other"
                else -> "No Connection"
            }
        }

        /**
         * Check if device has metered connection
         */
        fun isMeteredConnection(): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                connectivityManager.isActiveNetworkMetered
            } else {
                // Assume cellular is metered, WiFi is not
                isCellularConnected()
            }
        }

        /**
         * Get network capabilities information
         */
        fun getNetworkInfo(): NetworkInfo {
            return NetworkInfo(
                isAvailable = isNetworkAvailable(),
                isWifi = isWifiConnected(),
                isCellular = isCellularConnected(),
                isMetered = isMeteredConnection(),
                type = getNetworkType(),
            )
        }

        /**
         * Check if a specific host is reachable (basic connectivity test)
         */
        suspend fun isHostReachable(
            host: String,
            port: Int,
            timeoutMs: Int = 5000,
        ): Boolean {
            return try {
                kotlinx.coroutines.withTimeout(timeoutMs.toLong()) {
                    kotlinx.coroutines.Dispatchers.IO.run {
                        java.net.Socket().use { socket ->
                            socket.connect(java.net.InetSocketAddress(host, port), timeoutMs)
                            socket.isConnected
                        }
                    }
                }
            } catch (e: Exception) {
                false
            }
        }

        /**
         * Parse domain from URL
         */
        fun extractDomainFromUrl(url: String): String? {
            return try {
                val uri = java.net.URI(url)
                uri.host
            } catch (e: Exception) {
                null
            }
        }

        /**
         * Check if domain matches any in the whitelist
         */
        fun isDomainInWhitelist(
            domain: String,
            whitelist: List<String>,
        ): Boolean {
            if (domain.isBlank() || whitelist.isEmpty()) return false

            val normalizedDomain = domain.lowercase().trim()

            return whitelist.any { whitelistDomain ->
                val normalizedWhitelistDomain = whitelistDomain.lowercase().trim()

                // Exact match
                normalizedDomain == normalizedWhitelistDomain ||
                    // Subdomain match (e.g., sub.example.com matches example.com)
                    normalizedDomain.endsWith(".$normalizedWhitelistDomain")
            }
        }
    }

/**
 * Data class representing network information
 */
data class NetworkInfo(
    val isAvailable: Boolean,
    val isWifi: Boolean,
    val isCellular: Boolean,
    val isMetered: Boolean,
    val type: String,
)
