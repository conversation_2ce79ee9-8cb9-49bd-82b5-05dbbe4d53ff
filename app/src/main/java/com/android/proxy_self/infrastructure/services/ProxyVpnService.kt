package com.android.proxy_self.infrastructure.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.android.proxy_self.MainActivity
import com.android.proxy_self.R
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.infrastructure.managers.NetworkManager
import com.android.proxy_self.infrastructure.managers.ProxyManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.ByteBuffer
import javax.inject.Inject

/**
 * VPN Service for intercepting network traffic and routing through proxy
 * Extends Android VpnService to create a VPN tunnel
 * Implements domain-based routing logic
 */
@AndroidEntryPoint
class ProxyVpnService : VpnService() {
    @Inject
    lateinit var proxyManager: ProxyManager

    @Inject
    lateinit var networkManager: NetworkManager

    private var vpnInterface: ParcelFileDescriptor? = null
    private var serviceJob: Job? = null
    private var isRunning = false
    private var currentConfig: ProxyConfig? = null

    companion object {
        const val ACTION_START_VPN = "com.android.proxy_self.START_VPN"
        const val ACTION_STOP_VPN = "com.android.proxy_self.STOP_VPN"
        const val EXTRA_PROXY_CONFIG = "proxy_config"

        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "proxy_vpn_channel"
        private const val VPN_MTU = 1500
        private const val VPN_ADDRESS = "********"
        private const val VPN_ROUTE = "0.0.0.0"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        when (intent?.action) {
            ACTION_START_VPN -> {
                val config =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG, ProxyConfig::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG)
                    }
                config?.let { startVpn(it) }
            }
            ACTION_STOP_VPN -> {
                stopVpn()
            }
        }
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        stopVpn()
    }

    /**
     * Start VPN with given proxy configuration
     */
    private fun startVpn(config: ProxyConfig) {
        if (isRunning) return

        try {
            currentConfig = config

            // Create VPN interface
            vpnInterface = createVpnInterface()

            if (vpnInterface != null) {
                isRunning = true
                startForeground(NOTIFICATION_ID, createNotification(config))

                // Start packet processing in background
                serviceJob =
                    CoroutineScope(Dispatchers.IO).launch {
                        processPackets()
                    }
            }
        } catch (e: Exception) {
            // Handle VPN creation failure
            stopVpn()
        }
    }

    /**
     * Stop VPN service
     */
    private fun stopVpn() {
        isRunning = false
        serviceJob?.cancel()
        vpnInterface?.close()
        vpnInterface = null
        currentConfig = null
        stopForeground(true)
        stopSelf()
    }

    /**
     * Create VPN interface
     */
    private fun createVpnInterface(): ParcelFileDescriptor? {
        return Builder()
            .setMtu(VPN_MTU)
            .addAddress(VPN_ADDRESS, 32)
            .addRoute(VPN_ROUTE, 0)
            .addDnsServer("*******")
            .addDnsServer("*******")
            .setSession("ProxyVPN")
            .setConfigureIntent(
                PendingIntent.getActivity(
                    this,
                    0,
                    Intent(this, MainActivity::class.java),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                ),
            )
            .establish()
    }

    /**
     * Process network packets
     */
    private suspend fun processPackets() {
        val vpnInput = FileInputStream(vpnInterface?.fileDescriptor)
        val vpnOutput = FileOutputStream(vpnInterface?.fileDescriptor)

        val packet = ByteArray(32767)

        try {
            while (isRunning && !serviceJob?.isCancelled!!) {
                val length = vpnInput.read(packet)
                if (length > 0) {
                    // Process the packet
                    processPacket(ByteBuffer.wrap(packet, 0, length), vpnOutput)
                }
            }
        } catch (e: Exception) {
            // Handle packet processing errors
        } finally {
            vpnInput.close()
            vpnOutput.close()
        }
    }

    /**
     * Process individual network packet
     */
    private suspend fun processPacket(
        packet: ByteBuffer,
        vpnOutput: FileOutputStream,
    ) {
        try {
            // Parse IP packet
            val ipPacket = parseIpPacket(packet)

            if (ipPacket != null) {
                val domain = extractDomainFromPacket(ipPacket)

                if (domain != null && currentConfig != null) {
                    val shouldUseProxy = proxyManager.shouldUseProxy(domain, currentConfig!!.domains)

                    if (shouldUseProxy) {
                        // Route through proxy
                        routePacketThroughProxy(ipPacket, vpnOutput)
                    } else {
                        // Route directly
                        routePacketDirectly(ipPacket, vpnOutput)
                    }
                } else {
                    // Default: route directly
                    routePacketDirectly(packet, vpnOutput)
                }
            }
        } catch (e: Exception) {
            // Handle packet processing errors
        }
    }

    /**
     * Parse IP packet (simplified implementation)
     */
    private fun parseIpPacket(packet: ByteBuffer): IpPacket? {
        return try {
            if (packet.remaining() < 20) return null

            val version = (packet.get(0).toInt() and 0xF0) shr 4
            if (version != 4) return null // Only IPv4 for now

            val protocol = packet.get(9).toInt() and 0xFF
            val sourceIp = ByteArray(4)
            val destIp = ByteArray(4)

            packet.position(12)
            packet.get(sourceIp)
            packet.get(destIp)

            IpPacket(
                version = version,
                protocol = protocol,
                sourceIp = sourceIp,
                destIp = destIp,
                data = packet.array(),
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Extract domain from packet (simplified - would need more sophisticated implementation)
     */
    private fun extractDomainFromPacket(packet: IpPacket): String? {
        // TODO: Implement proper domain extraction from:
        // 1. DNS queries
        // 2. HTTP Host headers
        // 3. TLS SNI (Server Name Indication)

        // For now, return null (would route directly)
        return null
    }

    /**
     * Route packet through proxy
     */
    private suspend fun routePacketThroughProxy(
        packet: IpPacket,
        vpnOutput: FileOutputStream,
    ) {
        // TODO: Implement proxy routing
        // This would involve:
        // 1. Establishing connection to proxy server
        // 2. Sending packet data through proxy
        // 3. Receiving response from proxy
        // 4. Writing response back to VPN interface

        // For now, route directly as fallback
        routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
    }

    /**
     * Route packet directly (bypass proxy)
     */
    private fun routePacketDirectly(
        packet: ByteBuffer,
        vpnOutput: FileOutputStream,
    ) {
        try {
            vpnOutput.write(packet.array(), 0, packet.remaining())
        } catch (e: Exception) {
            // Handle routing errors
        }
    }

    /**
     * Route packet directly (overload for ByteBuffer)
     */
    private fun routePacketDirectly(
        packet: IpPacket,
        vpnOutput: FileOutputStream,
    ) {
        routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel =
                NotificationChannel(
                    CHANNEL_ID,
                    "Proxy VPN Service",
                    NotificationManager.IMPORTANCE_LOW,
                ).apply {
                    description = "Proxy VPN service notifications"
                    setShowBadge(false)
                }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Create foreground service notification
     */
    private fun createNotification(config: ProxyConfig): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent =
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Proxy VPN Active")
            .setContentText("Routing ${config.domains.size} domains through ${config.serverAddress}")
            .setSmallIcon(R.drawable.ic_vpn_key_24)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
}

/**
 * Data class representing an IP packet
 */
data class IpPacket(
    val version: Int,
    val protocol: Int,
    val sourceIp: ByteArray,
    val destIp: ByteArray,
    val data: ByteArray,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as IpPacket

        if (version != other.version) return false
        if (protocol != other.protocol) return false
        if (!sourceIp.contentEquals(other.sourceIp)) return false
        if (!destIp.contentEquals(other.destIp)) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = version
        result = 31 * result + protocol
        result = 31 * result + sourceIp.contentHashCode()
        result = 31 * result + destIp.contentHashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}
