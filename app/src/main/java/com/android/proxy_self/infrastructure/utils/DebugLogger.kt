package com.android.proxy_self.infrastructure.utils

import android.util.Log
import com.android.proxy_self.BuildConfig

/**
 * Centralized debug logging utility
 * Only logs in debug builds to avoid performance impact in release
 */
object DebugLogger {
    
    private const val APP_TAG = "ProxyApp"
    
    // Component-specific tags
    object Tags {
        const val VIEWMODEL = "ProxyViewModel"
        const val USE_CASE = "UseCase"
        const val REPOSITORY = "Repository"
        const val SERVICE = "Service"
        const val MANAGER = "Manager"
        const val UTILS = "Utils"
        const val DATABASE = "Database"
        const val NETWORK = "Network"
        const val VPN = "VPN"
        const val SOCKS5 = "Socks5"
        const val DOMAIN_EXTRACTOR = "DomainExtractor"
        const val ENCRYPTION = "Encryption"
        const val VALIDATION = "Validation"
        const val UI = "UI"
        const val NAVIGATION = "Navigation"
        const val NOTIFICATION = "Notification"
        const val WORKER = "Worker"
        const val RECEIVER = "Receiver"
        const val TILE = "Tile"
    }
    
    /**
     * Log debug message
     */
    fun d(tag: String, message: String, throwable: Throwable? = null) {
        if (BuildConfig.DEBUG) {
            val fullTag = "$APP_TAG:$tag"
            if (throwable != null) {
                Log.d(fullTag, message, throwable)
            } else {
                Log.d(fullTag, message)
            }
        }
    }
    
    /**
     * Log info message
     */
    fun i(tag: String, message: String, throwable: Throwable? = null) {
        if (BuildConfig.DEBUG) {
            val fullTag = "$APP_TAG:$tag"
            if (throwable != null) {
                Log.i(fullTag, message, throwable)
            } else {
                Log.i(fullTag, message)
            }
        }
    }
    
    /**
     * Log warning message
     */
    fun w(tag: String, message: String, throwable: Throwable? = null) {
        if (BuildConfig.DEBUG) {
            val fullTag = "$APP_TAG:$tag"
            if (throwable != null) {
                Log.w(fullTag, message, throwable)
            } else {
                Log.w(fullTag, message)
            }
        }
    }
    
    /**
     * Log error message
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        if (BuildConfig.DEBUG) {
            val fullTag = "$APP_TAG:$tag"
            if (throwable != null) {
                Log.e(fullTag, message, throwable)
            } else {
                Log.e(fullTag, message)
            }
        }
    }
    
    /**
     * Log verbose message
     */
    fun v(tag: String, message: String, throwable: Throwable? = null) {
        if (BuildConfig.DEBUG) {
            val fullTag = "$APP_TAG:$tag"
            if (throwable != null) {
                Log.v(fullTag, message, throwable)
            } else {
                Log.v(fullTag, message)
            }
        }
    }
    
    /**
     * Log method entry
     */
    fun methodEntry(tag: String, methodName: String, params: Map<String, Any?> = emptyMap()) {
        if (BuildConfig.DEBUG) {
            val paramsStr = if (params.isNotEmpty()) {
                params.entries.joinToString(", ") { "${it.key}=${it.value}" }
            } else {
                "no params"
            }
            d(tag, "→ $methodName($paramsStr)")
        }
    }
    
    /**
     * Log method exit
     */
    fun methodExit(tag: String, methodName: String, result: Any? = null) {
        if (BuildConfig.DEBUG) {
            val resultStr = result?.let { " → $it" } ?: ""
            d(tag, "← $methodName$resultStr")
        }
    }
    
    /**
     * Log method execution time
     */
    inline fun <T> measureTime(tag: String, methodName: String, block: () -> T): T {
        return if (BuildConfig.DEBUG) {
            val startTime = System.currentTimeMillis()
            val result = block()
            val duration = System.currentTimeMillis() - startTime
            d(tag, "$methodName executed in ${duration}ms")
            result
        } else {
            block()
        }
    }
    
    /**
     * Log state change
     */
    fun stateChange(tag: String, from: Any?, to: Any?, context: String = "") {
        if (BuildConfig.DEBUG) {
            val contextStr = if (context.isNotEmpty()) " [$context]" else ""
            d(tag, "State change$contextStr: $from → $to")
        }
    }
    
    /**
     * Log network request
     */
    fun networkRequest(tag: String, method: String, url: String, headers: Map<String, String> = emptyMap()) {
        if (BuildConfig.DEBUG) {
            d(tag, "Network Request: $method $url")
            if (headers.isNotEmpty()) {
                headers.forEach { (key, value) ->
                    v(tag, "Header: $key = $value")
                }
            }
        }
    }
    
    /**
     * Log network response
     */
    fun networkResponse(tag: String, code: Int, message: String, duration: Long = 0) {
        if (BuildConfig.DEBUG) {
            val durationStr = if (duration > 0) " (${duration}ms)" else ""
            d(tag, "Network Response: $code $message$durationStr")
        }
    }
    
    /**
     * Log database operation
     */
    fun databaseOperation(tag: String, operation: String, table: String, details: String = "") {
        if (BuildConfig.DEBUG) {
            val detailsStr = if (details.isNotEmpty()) " - $details" else ""
            d(tag, "DB Operation: $operation on $table$detailsStr")
        }
    }
    
    /**
     * Log user action
     */
    fun userAction(tag: String, action: String, details: Map<String, Any?> = emptyMap()) {
        if (BuildConfig.DEBUG) {
            val detailsStr = if (details.isNotEmpty()) {
                details.entries.joinToString(", ") { "${it.key}=${it.value}" }
            } else {
                ""
            }
            i(tag, "User Action: $action${if (detailsStr.isNotEmpty()) " ($detailsStr)" else ""}")
        }
    }
    
    /**
     * Log configuration change
     */
    fun configChange(tag: String, key: String, oldValue: Any?, newValue: Any?) {
        if (BuildConfig.DEBUG) {
            d(tag, "Config Change: $key = $oldValue → $newValue")
        }
    }
    
    /**
     * Log performance metric
     */
    fun performance(tag: String, metric: String, value: Number, unit: String = "") {
        if (BuildConfig.DEBUG) {
            val unitStr = if (unit.isNotEmpty()) " $unit" else ""
            i(tag, "Performance: $metric = $value$unitStr")
        }
    }
    
    /**
     * Log security event
     */
    fun security(tag: String, event: String, details: String = "") {
        if (BuildConfig.DEBUG) {
            val detailsStr = if (details.isNotEmpty()) " - $details" else ""
            w(tag, "Security Event: $event$detailsStr")
        }
    }
}
