package com.android.proxy_self.infrastructure.utils

import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets

/**
 * Utility class for extracting domain names from network packets
 * Supports DNS queries, HTTP Host headers, and TLS SNI
 */
object DomainExtractor {
    private const val TAG = "DomainExtractor"
    
    // Protocol constants
    private const val DNS_PORT = 53
    private const val HTTP_PORT = 80
    private const val HTTPS_PORT = 443
    private const val TCP_PROTOCOL = 6
    private const val UDP_PROTOCOL = 17
    
    /**
     * Extract domain from IP packet
     */
    fun extractDomain(packet: ByteBuffer): String? {
        return try {
            if (packet.remaining() < 20) return null
            
            // Parse IP header
            val version = (packet.get(0).toInt() and 0xF0) shr 4
            if (version != 4) return null // Only IPv4 for now
            
            val protocol = packet.get(9).toInt() and 0xFF
            val headerLength = (packet.get(0).toInt() and 0x0F) * 4
            
            if (packet.remaining() < headerLength) return null
            
            // Extract destination IP and port
            val destIp = ByteArray(4)
            packet.position(16)
            packet.get(destIp)
            
            // Skip to transport layer
            packet.position(headerLength)
            
            when (protocol) {
                UDP_PROTOCOL -> extractFromUdp(packet)
                TCP_PROTOCOL -> extractFromTcp(packet)
                else -> null
            }
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from packet", e)
            null
        }
    }
    
    /**
     * Extract domain from UDP packet (mainly DNS)
     */
    private fun extractFromUdp(packet: ByteBuffer): String? {
        return try {
            if (packet.remaining() < 8) return null // UDP header size
            
            val sourcePort = packet.short.toInt() and 0xFFFF
            val destPort = packet.short.toInt() and 0xFFFF
            val length = packet.short.toInt() and 0xFFFF
            val checksum = packet.short.toInt() and 0xFFFF
            
            // Check if it's a DNS query
            if (destPort == DNS_PORT || sourcePort == DNS_PORT) {
                extractFromDnsQuery(packet)
            } else {
                null
            }
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from UDP packet", e)
            null
        }
    }
    
    /**
     * Extract domain from TCP packet (HTTP Host header or TLS SNI)
     */
    private fun extractFromTcp(packet: ByteBuffer): String? {
        return try {
            if (packet.remaining() < 20) return null // TCP header minimum size
            
            val sourcePort = packet.short.toInt() and 0xFFFF
            val destPort = packet.short.toInt() and 0xFFFF
            
            // Skip sequence and ack numbers
            packet.position(packet.position() + 8)
            
            val headerLength = ((packet.get().toInt() and 0xF0) shr 4) * 4
            
            // Skip to TCP payload
            packet.position(packet.position() + headerLength - 13)
            
            when (destPort) {
                HTTP_PORT -> extractFromHttpHeader(packet)
                HTTPS_PORT -> extractFromTlsSni(packet)
                else -> null
            }
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from TCP packet", e)
            null
        }
    }
    
    /**
     * Extract domain from DNS query
     */
    private fun extractFromDnsQuery(packet: ByteBuffer): String? {
        return try {
            if (packet.remaining() < 12) return null // DNS header size
            
            // Skip DNS header
            packet.position(packet.position() + 12)
            
            // Read question section
            val domain = readDnsName(packet)

            if (domain != null) {
                DebugLogger.d(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Extracted domain from DNS query: $domain")
            }
            domain
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from DNS query", e)
            null
        }
    }
    
    /**
     * Extract domain from HTTP Host header
     */
    private fun extractFromHttpHeader(packet: ByteBuffer): String? {
        return try {
            val remaining = packet.remaining()
            if (remaining < 10) return null
            
            // Convert to string to search for Host header
            val data = ByteArray(minOf(remaining, 1024)) // Limit to first 1KB
            packet.get(data)
            
            val httpData = String(data, StandardCharsets.UTF_8)
            
            // Look for Host header
            val hostPattern = Regex("Host:\\s*([^\\r\\n]+)", RegexOption.IGNORE_CASE)
            val match = hostPattern.find(httpData)
            
            val domain = match?.groupValues?.get(1)?.trim()
            if (domain != null) {
                DebugLogger.d(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Extracted domain from HTTP Host header: $domain")
            }

            domain
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from HTTP header", e)
            null
        }
    }
    
    /**
     * Extract domain from TLS SNI (Server Name Indication)
     */
    private fun extractFromTlsSni(packet: ByteBuffer): String? {
        return try {
            if (packet.remaining() < 43) return null // Minimum TLS ClientHello size
            
            // Check if it's a TLS handshake
            val contentType = packet.get().toInt() and 0xFF
            if (contentType != 0x16) return null // Handshake
            
            // Skip version and length
            packet.position(packet.position() + 4)
            
            // Check handshake type
            val handshakeType = packet.get().toInt() and 0xFF
            if (handshakeType != 0x01) return null // ClientHello
            
            // Skip handshake length and version
            packet.position(packet.position() + 6)
            
            // Skip random (32 bytes)
            packet.position(packet.position() + 32)
            
            // Skip session ID
            val sessionIdLength = packet.get().toInt() and 0xFF
            packet.position(packet.position() + sessionIdLength)
            
            // Skip cipher suites
            val cipherSuitesLength = packet.short.toInt() and 0xFFFF
            packet.position(packet.position() + cipherSuitesLength)
            
            // Skip compression methods
            val compressionMethodsLength = packet.get().toInt() and 0xFF
            packet.position(packet.position() + compressionMethodsLength)
            
            // Parse extensions
            if (packet.remaining() < 2) return null
            val extensionsLength = packet.short.toInt() and 0xFFFF
            
            val domain = parseExtensions(packet, extensionsLength)
            if (domain != null) {
                DebugLogger.d(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Extracted domain from TLS SNI: $domain")
            }

            domain
        } catch (e: Exception) {
            DebugLogger.e(DebugLogger.Tags.DOMAIN_EXTRACTOR, "Error extracting domain from TLS SNI", e)
            null
        }
    }
    
    /**
     * Parse TLS extensions to find SNI
     */
    private fun parseExtensions(packet: ByteBuffer, extensionsLength: Int): String? {
        val endPosition = packet.position() + extensionsLength
        
        while (packet.position() < endPosition && packet.remaining() >= 4) {
            val extensionType = packet.short.toInt() and 0xFFFF
            val extensionLength = packet.short.toInt() and 0xFFFF
            
            if (extensionType == 0x0000) { // SNI extension
                return parseSniExtension(packet, extensionLength)
            } else {
                // Skip this extension
                packet.position(packet.position() + extensionLength)
            }
        }
        
        return null
    }
    
    /**
     * Parse SNI extension
     */
    private fun parseSniExtension(packet: ByteBuffer, extensionLength: Int): String? {
        if (extensionLength < 5) return null
        
        val listLength = packet.short.toInt() and 0xFFFF
        val nameType = packet.get().toInt() and 0xFF
        
        if (nameType != 0x00) return null // hostname
        
        val nameLength = packet.short.toInt() and 0xFFFF
        if (nameLength <= 0 || packet.remaining() < nameLength) return null
        
        val nameBytes = ByteArray(nameLength)
        packet.get(nameBytes)
        
        return String(nameBytes, StandardCharsets.UTF_8)
    }
    
    /**
     * Read DNS name from packet
     */
    private fun readDnsName(packet: ByteBuffer): String? {
        val name = StringBuilder()
        var jumped = false
        var jumps = 0
        val maxJumps = 5
        
        while (packet.hasRemaining()) {
            val length = packet.get().toInt() and 0xFF
            
            if (length == 0) {
                break
            }
            
            if ((length and 0xC0) == 0xC0) {
                // Compression pointer
                if (!jumped) {
                    packet.position(packet.position() + 1) // Skip second byte
                }
                
                if (jumps++ > maxJumps) {
                    return null // Avoid infinite loops
                }
                
                jumped = true
                continue
            }
            
            if (name.isNotEmpty()) {
                name.append('.')
            }
            
            if (packet.remaining() < length) {
                return null
            }
            
            val labelBytes = ByteArray(length)
            packet.get(labelBytes)
            name.append(String(labelBytes, StandardCharsets.UTF_8))
        }
        
        return if (name.isNotEmpty()) name.toString() else null
    }
}
