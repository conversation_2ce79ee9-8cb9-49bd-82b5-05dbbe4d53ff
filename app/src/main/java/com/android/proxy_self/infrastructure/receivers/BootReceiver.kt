package com.android.proxy_self.infrastructure.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import androidx.work.*
import com.android.proxy_self.infrastructure.workers.AutoStartProxyWorker
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * Broadcast receiver for device boot events
 * Handles auto-start functionality when device boots
 * Implements delayed start to ensure system stability
 */
@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    companion object {
        private const val AUTO_START_WORK_NAME = "auto_start_proxy_work"
        private const val DELAY_SECONDS = 30L // Wait 30 seconds after boot
        private const val KEY_AUTO_START = "auto_start_enabled"
    }

    override fun onReceive(
        context: Context,
        intent: Intent,
    ) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED,
            -> {
                handleBootCompleted(context)
            }
        }
    }

    /**
     * Handle boot completed event
     */
    private fun handleBootCompleted(context: Context) {
        try {
            // Check if auto-start is enabled
            val isAutoStartEnabled = sharedPreferences.getBoolean(KEY_AUTO_START, false)

            if (isAutoStartEnabled) {
                scheduleAutoStartWork(context)
            }
        } catch (e: Exception) {
            // Log error in production
            // For now, silently fail to avoid crashes
        }
    }

    /**
     * Schedule delayed auto-start work using WorkManager
     */
    private fun scheduleAutoStartWork(context: Context) {
        val constraints =
            Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()

        val autoStartRequest =
            OneTimeWorkRequestBuilder<AutoStartProxyWorker>()
                .setConstraints(constraints)
                .setInitialDelay(DELAY_SECONDS, TimeUnit.SECONDS)
                .addTag(AUTO_START_WORK_NAME)
                .build()

        WorkManager.getInstance(context)
            .enqueueUniqueWork(
                AUTO_START_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                autoStartRequest,
            )
    }

    /**
     * Cancel scheduled auto-start work
     */
    fun cancelAutoStartWork(context: Context) {
        WorkManager.getInstance(context)
            .cancelUniqueWork(AUTO_START_WORK_NAME)
    }
}
