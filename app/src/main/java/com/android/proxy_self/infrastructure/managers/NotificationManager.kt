package com.android.proxy_self.infrastructure.managers

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import com.android.proxy_self.MainActivity
import com.android.proxy_self.R
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.infrastructure.services.ProxyService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling notifications in the proxy application
 * Manages foreground service notifications and status updates
 */
@Singleton
class ProxyNotificationManager
    @Inject
    constructor(
        private val context: Context,
    ) {
        companion object {
            const val CHANNEL_ID = "proxy_service_channel"
            const val NOTIFICATION_ID = 1001
            const val STOP_ACTION = "STOP_PROXY"
            const val OPEN_APP_ACTION = "OPEN_APP"
        }

        private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        init {
            createNotificationChannel()
        }

        /**
         * Creates notification channel for Android O and above
         */
        private fun createNotificationChannel() {
            val channel =
                NotificationChannel(
                    CHANNEL_ID,
                    context.getString(R.string.notification_channel_name),
                    NotificationManager.IMPORTANCE_LOW,
                ).apply {
                    description = context.getString(R.string.notification_channel_description)
                    setShowBadge(false)
                    enableLights(false)
                    enableVibration(false)
                    setSound(null, null)
                }
            notificationManager.createNotificationChannel(channel)
        }

        /**
         * Creates foreground service notification
         */
        fun createServiceNotification(
            status: ProxyStatus,
            serverHost: String = "",
            serverPort: Int = 0,
        ): android.app.Notification {
            val title =
                when (status) {
                    ProxyStatus.CONNECTED -> context.getString(R.string.notification_title_connected)
                    ProxyStatus.CONNECTING -> context.getString(R.string.notification_title_connecting)
                    ProxyStatus.RECONNECTING -> context.getString(R.string.notification_title_reconnecting)
                    ProxyStatus.DISCONNECTED -> context.getString(R.string.notification_title_disconnected)
                    ProxyStatus.ERROR -> context.getString(R.string.notification_title_error)
                }

            val content =
                when (status) {
                    ProxyStatus.CONNECTED -> {
                        if (serverHost.isNotEmpty() && serverPort > 0) {
                            context.getString(R.string.notification_content_connected, serverHost, serverPort)
                        } else {
                            context.getString(R.string.notification_content_connected_simple)
                        }
                    }
                    ProxyStatus.CONNECTING -> context.getString(R.string.notification_content_connecting)
                    ProxyStatus.RECONNECTING -> context.getString(R.string.notification_content_reconnecting)
                    ProxyStatus.DISCONNECTED -> context.getString(R.string.notification_content_disconnected)
                    ProxyStatus.ERROR -> context.getString(R.string.notification_content_error)
                }

            val openAppIntent =
                Intent(context, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
            val openAppPendingIntent =
                PendingIntent.getActivity(
                    context,
                    0,
                    openAppIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                )

            val stopIntent =
                Intent(context, ProxyService::class.java).apply {
                    action = STOP_ACTION
                }
            val stopPendingIntent =
                PendingIntent.getService(
                    context,
                    1,
                    stopIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                )

            val iconRes =
                when (status) {
                    ProxyStatus.CONNECTED -> R.drawable.ic_proxy_24
                    ProxyStatus.CONNECTING -> R.drawable.ic_proxy_connecting_24
                    ProxyStatus.RECONNECTING -> R.drawable.ic_proxy_connecting_24
                    ProxyStatus.DISCONNECTED -> R.drawable.ic_proxy_off_24
                    ProxyStatus.ERROR -> R.drawable.ic_proxy_error_24
                }

            return NotificationCompat.Builder(context, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setSmallIcon(iconRes)
                .setContentIntent(openAppPendingIntent)
                .setOngoing(status == ProxyStatus.CONNECTED || status == ProxyStatus.CONNECTING)
                .setAutoCancel(false)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .apply {
                    if (status == ProxyStatus.CONNECTED || status == ProxyStatus.CONNECTING) {
                        addAction(
                            R.drawable.ic_stop_24,
                            context.getString(R.string.notification_action_stop),
                            stopPendingIntent,
                        )
                    }
                }
                .build()
        }

        /**
         * Updates the foreground service notification
         */
        fun updateNotification(
            status: ProxyStatus,
            serverHost: String = "",
            serverPort: Int = 0,
        ) {
            val notification = createServiceNotification(status, serverHost, serverPort)
            notificationManager.notify(NOTIFICATION_ID, notification)
        }

        /**
         * Shows a simple status notification
         */
        fun showStatusNotification(
            title: String,
            message: String,
            isOngoing: Boolean = false,
        ) {
            val openAppIntent =
                Intent(context, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
            val openAppPendingIntent =
                PendingIntent.getActivity(
                    context,
                    0,
                    openAppIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                )

            val notification =
                NotificationCompat.Builder(context, CHANNEL_ID)
                    .setContentTitle(title)
                    .setContentText(message)
                    .setSmallIcon(R.drawable.ic_proxy_24)
                    .setContentIntent(openAppPendingIntent)
                    .setOngoing(isOngoing)
                    .setAutoCancel(!isOngoing)
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                    .build()

            notificationManager.notify(NOTIFICATION_ID + 1, notification)
        }

        /**
         * Cancels all notifications
         */
        fun cancelAllNotifications() {
            notificationManager.cancelAll()
        }

        /**
         * Cancels specific notification
         */
        fun cancelNotification(notificationId: Int) {
            notificationManager.cancel(notificationId)
        }
    }
