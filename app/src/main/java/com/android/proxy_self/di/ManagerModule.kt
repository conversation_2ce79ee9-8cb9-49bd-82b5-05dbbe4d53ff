package com.android.proxy_self.di

import android.content.Context
import com.android.proxy_self.infrastructure.managers.NetworkManager
import com.android.proxy_self.infrastructure.managers.ProxyManager
import com.android.proxy_self.infrastructure.managers.ProxyNotificationManager
import com.android.proxy_self.infrastructure.utils.NetworkUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for manager dependencies
 * Provides manager classes for infrastructure layer
 */
@Module
@InstallIn(SingletonComponent::class)
object ManagerModule {
    @Provides
    @Singleton
    fun provideProxyManager(networkUtils: NetworkUtils): ProxyManager {
        return ProxyManager(networkUtils)
    }

    @Provides
    @Singleton
    fun provideNetworkManager(
        @ApplicationContext context: Context,
        networkUtils: NetworkUtils,
    ): NetworkManager {
        return NetworkManager(context, networkUtils)
    }

    @Provides
    @Singleton
    fun provideProxyNotificationManager(
        @ApplicationContext context: Context,
    ): ProxyNotificationManager {
        return ProxyNotificationManager(context)
    }
}
