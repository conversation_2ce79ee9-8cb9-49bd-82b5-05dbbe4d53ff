package com.android.proxy_self.di

import android.content.Context
import androidx.room.Room
import com.android.proxy_self.data.datasources.local.ProxyDao
import com.android.proxy_self.data.datasources.local.ProxyDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for database dependencies
 * Provides Room database and DAO instances
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    @Provides
    @Singleton
    fun provideProxyDatabase(
        @ApplicationContext context: Context,
    ): ProxyDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            ProxyDatabase::class.java,
            "proxy_database",
        )
            .fallbackToDestructiveMigration()
            .build()
    }

    @Provides
    fun provideProxyDao(database: ProxyDatabase): ProxyDao {
        return database.proxyDao()
    }
}
