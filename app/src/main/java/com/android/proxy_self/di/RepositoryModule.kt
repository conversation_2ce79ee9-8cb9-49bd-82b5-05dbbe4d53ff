package com.android.proxy_self.di

import com.android.proxy_self.data.repositories.ProxyRepositoryImpl
import com.android.proxy_self.domain.repositories.ProxyRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for repository dependencies
 * Binds repository implementations to interfaces
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    @Singleton
    abstract fun bindProxyRepository(proxyRepositoryImpl: ProxyRepositoryImpl): ProxyRepository
}
