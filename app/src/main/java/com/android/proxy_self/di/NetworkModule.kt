package com.android.proxy_self.di

import android.content.Context
import android.content.SharedPreferences
import com.android.proxy_self.infrastructure.utils.EncryptionUtils
import com.android.proxy_self.infrastructure.utils.NetworkUtils
import com.android.proxy_self.infrastructure.utils.ValidationUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * Hilt module for network and utility dependencies
 * Provides OkHttp, Retrofit, and utility classes
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://httpbin.org/") // Base URL for testing
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    fun provideSharedPreferences(
        @ApplicationContext context: Context,
    ): SharedPreferences {
        return context.getSharedPreferences("proxy_settings", Context.MODE_PRIVATE)
    }

    @Provides
    @Singleton
    fun provideEncryptionUtils(
        @ApplicationContext context: Context,
    ): EncryptionUtils {
        return EncryptionUtils(context)
    }

    @Provides
    @Singleton
    fun provideNetworkUtils(
        @ApplicationContext context: Context,
    ): NetworkUtils {
        return NetworkUtils(context)
    }

    @Provides
    @Singleton
    fun provideValidationUtils(): ValidationUtils {
        return ValidationUtils()
    }
}
