package com.android.proxy_self.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.android.proxy_self.presentation.ui.components.LoadingButton
import com.android.proxy_self.presentation.viewmodel.SettingsViewModel

/**
 * Settings screen for application preferences
 * Implements Material 3 design with proper state management
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    onNavigateToAbout: () -> Unit,
    viewModel: SettingsViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Handle side effects
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessage()
        }
    }

    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            kotlinx.coroutines.delay(5000)
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Settings") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                        )
                    }
                },
            )
        },
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // General Settings
            SettingsSection(
                title = "General",
                content = {
                    SettingsItem(
                        title = "Auto-start on boot",
                        description = "Automatically start proxy when device boots",
                        checked = uiState.isAutoStartEnabled,
                        onCheckedChange = { viewModel.toggleAutoStart() },
                        enabled = !uiState.isAnyOperationInProgress(),
                    )

                    SettingsItem(
                        title = "Dark theme",
                        description = "Use dark theme for the app",
                        checked = uiState.isDarkThemeEnabled,
                        onCheckedChange = { viewModel.toggleDarkTheme() },
                        enabled = !uiState.isAnyOperationInProgress(),
                    )

                    SettingsItem(
                        title = "Notifications",
                        description = "Show proxy status notifications",
                        checked = uiState.isNotificationsEnabled,
                        onCheckedChange = { viewModel.toggleNotifications() },
                        enabled = !uiState.isAnyOperationInProgress(),
                    )
                },
            )

            // Data Management
            SettingsSection(
                title = "Data Management",
                content = {
                    LoadingButton(
                        onClick = { viewModel.exportConfiguration() },
                        text = "Export Configuration",
                        isLoading = uiState.isExporting,
                        enabled = !uiState.isAnyOperationInProgress(),
                        variant = com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined,
                        modifier = Modifier.fillMaxWidth(),
                    )

                    LoadingButton(
                        onClick = { viewModel.importConfiguration() },
                        text = "Import Configuration",
                        isLoading = uiState.isImporting,
                        enabled = !uiState.isAnyOperationInProgress(),
                        variant = com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined,
                        modifier = Modifier.fillMaxWidth(),
                    )

                    LoadingButton(
                        onClick = { viewModel.clearAllData() },
                        text = "Clear All Data",
                        isLoading = uiState.isClearing,
                        enabled = !uiState.isAnyOperationInProgress(),
                        variant = com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined,
                        modifier = Modifier.fillMaxWidth(),
                    )
                },
            )

            // App Information
            SettingsSection(
                title = "App Information",
                content = {
                    InfoItem(
                        label = "Version",
                        value = uiState.appVersion,
                    )

                    InfoItem(
                        label = "Build Number",
                        value = uiState.buildNumber,
                    )

                    OutlinedButton(
                        onClick = onNavigateToAbout,
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        Text("About")
                    }
                },
            )

            // Messages
            MessageSection(
                uiState = uiState,
                onClearMessage = viewModel::clearMessage,
                onClearError = viewModel::clearError,
            )
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
            )

            content()
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    enabled: Boolean = true,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled,
        )
    }
}

@Composable
private fun InfoItem(
    label: String,
    value: String,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
        )
    }
}

@Composable
private fun MessageSection(
    uiState: com.android.proxy_self.presentation.state.SettingsUiState,
    onClearMessage: () -> Unit,
    onClearError: () -> Unit,
) {
    // Success Message
    uiState.message?.let { message ->
        Card(
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                ),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.weight(1f),
                )
                TextButton(onClick = onClearMessage) {
                    Text("Dismiss")
                }
            }
        }
    }

    // Error Message
    uiState.error?.let { error ->
        Card(
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer,
                ),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.weight(1f),
                )
                TextButton(onClick = onClearError) {
                    Text("Dismiss")
                }
            }
        }
    }
}
