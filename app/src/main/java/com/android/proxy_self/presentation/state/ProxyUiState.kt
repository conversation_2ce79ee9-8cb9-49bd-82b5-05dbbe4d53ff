package com.android.proxy_self.presentation.state

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import com.android.proxy_self.domain.entities.ProxyType

/**
 * UI State for proxy configuration screen
 * Follows Jetpack Compose state management best practices
 */
data class ProxyUiState(
    // Proxy configuration
    val proxyConfig: ProxyConfig = ProxyConfig(),
    // UI state flags
    val isProxyEnabled: Boolean = false,
    val isLoading: Boolean = false,
    val isTestingConnection: Boolean = false,
    val isSaving: Boolean = false,
    // Status information
    val proxyStatus: ProxyStatusInfo? = null,
    // Error handling
    val error: String? = null,
    val validationErrors: Map<String, String> = emptyMap(),
    // Form state
    val serverAddress: String = "",
    val serverPort: String = "",
    val username: String = "",
    val password: String = "",
    val domains: String = "",
    val selectedProxyType: ProxyType = ProxyType.SOCKS5,
    val isPasswordVisible: Boolean = false,
    // Success messages
    val successMessage: String? = null,
    // Connection test result
    val connectionTestResult: ConnectionTestResult? = null,
) {
    /**
     * Check if form has any validation errors
     */
    fun hasValidationErrors(): Boolean = validationErrors.isNotEmpty()

    /**
     * Check if form is valid for saving
     */
    fun isFormValid(): Boolean {
        return serverAddress.isNotBlank() &&
            serverPort.isNotBlank() &&
            username.isNotBlank() &&
            password.isNotBlank() &&
            domains.isNotBlank() &&
            !hasValidationErrors()
    }

    /**
     * Check if any operation is in progress
     */
    fun isAnyOperationInProgress(): Boolean {
        return isLoading || isTestingConnection || isSaving
    }

    /**
     * Get domains as list
     */
    fun getDomainsList(): List<String> {
        return if (domains.isBlank()) {
            emptyList()
        } else {
            domains.split(",")
                .map { it.trim() }
                .filter { it.isNotBlank() }
        }
    }

    /**
     * Create ProxyConfig from current form state
     */
    fun toProxyConfig(): ProxyConfig {
        return ProxyConfig(
            id = proxyConfig.id,
            serverAddress = serverAddress.trim(),
            serverPort = serverPort.trim().toIntOrNull() ?: 0,
            username = username.trim(),
            password = password,
            proxyType = selectedProxyType,
            domains = getDomainsList(),
            isEnabled = isProxyEnabled,
            createdAt = proxyConfig.createdAt,
            updatedAt = System.currentTimeMillis(),
        )
    }
}

/**
 * Sealed class for connection test results
 */
sealed class ConnectionTestResult {
    object Success : ConnectionTestResult()

    data class Failure(val error: String) : ConnectionTestResult()
}

/**
 * UI State for settings screen
 */
data class SettingsUiState(
    // Settings
    val isAutoStartEnabled: Boolean = false,
    val isDarkThemeEnabled: Boolean = false,
    val isNotificationsEnabled: Boolean = true,
    // UI state
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    // Messages
    val message: String? = null,
    val error: String? = null,
    // Data management
    val isExporting: Boolean = false,
    val isImporting: Boolean = false,
    val isClearing: Boolean = false,
    // App info
    val appVersion: String = "",
    val buildNumber: String = "",
) {
    /**
     * Check if any operation is in progress
     */
    fun isAnyOperationInProgress(): Boolean {
        return isLoading || isSaving || isExporting || isImporting || isClearing
    }
}

/**
 * UI State for about screen
 */
data class AboutUiState(
    val appName: String = "Proxy Self",
    val appVersion: String = "",
    val buildNumber: String = "",
    val buildDate: String = "",
    val developerName: String = "Android Developer",
    val developerEmail: String = "<EMAIL>",
    val githubUrl: String = "",
    val licenseUrl: String = "",
    val privacyPolicyUrl: String = "",
    val openSourceLibraries: List<LibraryInfo> = emptyList(),
)

/**
 * Data class for open source library information
 */
data class LibraryInfo(
    val name: String,
    val version: String,
    val license: String,
    val url: String,
)
