package com.android.proxy_self.presentation.quicksettings

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Intent
import android.graphics.drawable.Icon
import android.os.Build
import android.service.quicksettings.Tile
import android.service.quicksettings.TileService
import com.android.proxy_self.R
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.usecases.GetProxyConfigUseCase
import com.android.proxy_self.domain.usecases.StartProxyUseCase
import com.android.proxy_self.domain.usecases.StopProxyUseCase
import com.android.proxy_self.infrastructure.services.ProxyService
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Quick Settings Tile for proxy control
 * Allows users to toggle proxy from notification panel
 * Implements Android TileService
 */
@AndroidEntryPoint
class ProxyQuickSettingsTile : TileService() {
    @Inject
    lateinit var startProxyUseCase: StartProxyUseCase

    @Inject
    lateinit var stopProxyUseCase: StopProxyUseCase

    @Inject
    lateinit var getProxyConfigUseCase: GetProxyConfigUseCase

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var isProxyEnabled = false
    private var proxyStatus = ProxyStatus.DISCONNECTED

    override fun onStartListening() {
        super.onStartListening()
        updateTileState()
    }

    override fun onClick() {
        super.onClick()

        // Toggle proxy state
        if (isProxyEnabled) {
            stopProxy()
        } else {
            startProxy()
        }
    }

    /**
     * Start proxy service
     */
    private fun startProxy() {
        serviceScope.launch {
            try {
                // Get current proxy configuration
                getProxyConfigUseCase().first().fold(
                    onSuccess = { config ->
                        if (config.isValid()) {
                            // Start proxy service
                            val intent =
                                Intent(this@ProxyQuickSettingsTile, ProxyService::class.java).apply {
                                    action = ProxyService.ACTION_START_PROXY
                                    putExtra(ProxyService.EXTRA_PROXY_CONFIG, config)
                                }
                            startService(intent)

                            // Update tile state
                            isProxyEnabled = true
                            proxyStatus = ProxyStatus.CONNECTING
                            updateTileState()
                        } else {
                            // Configuration is invalid, open main app
                            openMainApp()
                        }
                    },
                    onFailure = {
                        // No configuration found, open main app
                        openMainApp()
                    },
                )
            } catch (e: Exception) {
                // Error occurred, open main app
                openMainApp()
            }
        }
    }

    /**
     * Stop proxy service
     */
    private fun stopProxy() {
        serviceScope.launch {
            try {
                // Stop proxy service
                val intent =
                    Intent(this@ProxyQuickSettingsTile, ProxyService::class.java).apply {
                        action = ProxyService.ACTION_STOP_PROXY
                    }
                startService(intent)

                // Update tile state
                isProxyEnabled = false
                proxyStatus = ProxyStatus.DISCONNECTED
                updateTileState()
            } catch (e: Exception) {
                // Error occurred, but still update tile state
                isProxyEnabled = false
                proxyStatus = ProxyStatus.ERROR
                updateTileState()
            }
        }
    }

    /**
     * Update tile visual state
     */
    private fun updateTileState() {
        qsTile?.let { tile ->
            when {
                !isProxyEnabled -> {
                    tile.state = Tile.STATE_INACTIVE
                    tile.label = "Proxy Off"
                    tile.contentDescription = "Proxy is disconnected. Tap to connect."
                    tile.icon = Icon.createWithResource(this, R.drawable.ic_proxy_off_24)
                }
                proxyStatus == ProxyStatus.CONNECTED -> {
                    tile.state = Tile.STATE_ACTIVE
                    tile.label = "Proxy On"
                    tile.contentDescription = "Proxy is connected. Tap to disconnect."
                    tile.icon = Icon.createWithResource(this, R.drawable.ic_proxy_24)
                }
                proxyStatus == ProxyStatus.CONNECTING || proxyStatus == ProxyStatus.RECONNECTING -> {
                    tile.state = Tile.STATE_ACTIVE
                    tile.label = "Connecting"
                    tile.contentDescription = "Proxy is connecting. Tap to cancel."
                    tile.icon = Icon.createWithResource(this, R.drawable.ic_proxy_connecting_24)
                }
                proxyStatus == ProxyStatus.ERROR -> {
                    tile.state = Tile.STATE_UNAVAILABLE
                    tile.label = "Proxy Error"
                    tile.contentDescription = "Proxy connection error. Tap to retry."
                    tile.icon = Icon.createWithResource(this, R.drawable.ic_proxy_error_24)
                }
                else -> {
                    tile.state = Tile.STATE_INACTIVE
                    tile.label = "Proxy"
                    tile.contentDescription = "Proxy tile"
                    tile.icon = Icon.createWithResource(this, R.drawable.ic_proxy_24)
                }
            }
            tile.updateTile()
        }
    }

    /**
     * Open main application
     */
    @SuppressLint("StartActivityAndCollapseDeprecated")
    private fun openMainApp() {
        val intent =
            packageManager.getLaunchIntentForPackage(packageName)?.apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
        intent?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // API 34+ - Use PendingIntent
                val pendingIntent =
                    PendingIntent.getActivity(
                        this,
                        0,
                        it,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                    )
                startActivityAndCollapse(pendingIntent)
            } else {
                // API 29-33 - Use Intent (deprecated but still works)
                startActivityAndCollapse(it)
            }
        }
    }

    /**
     * Update tile state based on service status
     * This method can be called from the service to update tile state
     */
    fun updateTileFromService(
        enabled: Boolean,
        status: ProxyStatus,
    ) {
        isProxyEnabled = enabled
        proxyStatus = status
        updateTileState()
    }

    companion object {
        /**
         * Request tile update from external components
         */
        fun requestTileUpdate(context: android.content.Context) {
            try {
                TileService.requestListeningState(
                    context,
                    android.content.ComponentName(
                        context,
                        ProxyQuickSettingsTile::class.java,
                    ),
                )
            } catch (e: Exception) {
                // Ignore if tile service is not available
            }
        }
    }
}
