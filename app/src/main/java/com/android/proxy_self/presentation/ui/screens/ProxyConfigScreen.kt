package com.android.proxy_self.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.android.proxy_self.presentation.state.ConnectionTestResult
import com.android.proxy_self.presentation.ui.components.LoadingButton
import com.android.proxy_self.presentation.ui.components.ProxyConfigForm
import com.android.proxy_self.presentation.ui.components.ProxyStatusCard
import com.android.proxy_self.presentation.viewmodel.ProxyViewModel

/**
 * Main screen for proxy configuration
 * Implements Material 3 design with proper state management
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProxyConfigScreen(
    onNavigateToSettings: () -> Unit,
    onNavigateToAbout: () -> Unit,
    viewModel: ProxyViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Handle side effects
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // Error will be shown in UI, auto-clear after some time
            kotlinx.coroutines.delay(5000)
            viewModel.clearError()
        }
    }

    LaunchedEffect(uiState.successMessage) {
        uiState.successMessage?.let {
            // Success message will be shown in UI, auto-clear after some time
            kotlinx.coroutines.delay(3000)
            viewModel.clearSuccessMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Proxy Configuration") },
                actions = {
                    IconButton(onClick = onNavigateToSettings) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "Settings",
                        )
                    }
                    IconButton(onClick = onNavigateToAbout) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "About",
                        )
                    }
                },
            )
        },
        snackbarHost = { SnackbarHost(hostState = remember { SnackbarHostState() }) },
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // Proxy Status Card
            ProxyStatusCard(
                statusInfo = uiState.proxyStatus,
                isEnabled = uiState.isProxyEnabled,
            )

            // Configuration Form
            ProxyConfigForm(
                uiState = uiState,
                onServerAddressChange = viewModel::updateServerAddress,
                onServerPortChange = viewModel::updateServerPort,
                onUsernameChange = viewModel::updateUsername,
                onPasswordChange = viewModel::updatePassword,
                onDomainsChange = viewModel::updateDomains,
                onProxyTypeChange = viewModel::updateProxyType,
                onPasswordVisibilityToggle = viewModel::togglePasswordVisibility,
            )

            // Action Buttons
            ActionButtons(
                uiState = uiState,
                onStartProxy = viewModel::startProxy,
                onStopProxy = viewModel::stopProxy,
                onSaveConfig = viewModel::saveConfiguration,
                onTestConnection = viewModel::testConnection,
            )

            // Messages
            MessageSection(
                uiState = uiState,
                onClearError = viewModel::clearError,
                onClearSuccess = viewModel::clearSuccessMessage,
                onClearTestResult = viewModel::clearConnectionTestResult,
            )
        }
    }
}

@Composable
private fun ActionButtons(
    uiState: com.android.proxy_self.presentation.state.ProxyUiState,
    onStartProxy: () -> Unit,
    onStopProxy: () -> Unit,
    onSaveConfig: () -> Unit,
    onTestConnection: () -> Unit,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "Actions",
                style = MaterialTheme.typography.titleMedium,
            )

            // Primary Action (Start/Stop)
            if (uiState.isProxyEnabled) {
                LoadingButton(
                    onClick = onStopProxy,
                    text = "Stop Proxy",
                    isLoading = uiState.isLoading,
                    modifier = Modifier.fillMaxWidth(),
                )
            } else {
                LoadingButton(
                    onClick = onStartProxy,
                    text = "Start Proxy",
                    isLoading = uiState.isLoading,
                    enabled = uiState.isFormValid(),
                    modifier = Modifier.fillMaxWidth(),
                )
            }

            // Secondary Actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                LoadingButton(
                    onClick = onSaveConfig,
                    text = "Save",
                    isLoading = uiState.isSaving,
                    enabled = uiState.isFormValid() && !uiState.isAnyOperationInProgress(),
                    variant = com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined,
                    modifier = Modifier.weight(1f),
                )

                LoadingButton(
                    onClick = onTestConnection,
                    text = "Test",
                    isLoading = uiState.isTestingConnection,
                    enabled = uiState.isFormValid() && !uiState.isAnyOperationInProgress(),
                    variant = com.android.proxy_self.presentation.ui.components.ButtonVariant.Outlined,
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

@Composable
private fun MessageSection(
    uiState: com.android.proxy_self.presentation.state.ProxyUiState,
    onClearError: () -> Unit,
    onClearSuccess: () -> Unit,
    onClearTestResult: () -> Unit,
) {
    // Error Message
    uiState.error?.let { error ->
        Card(
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer,
                ),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.weight(1f),
                )
                TextButton(onClick = onClearError) {
                    Text("Dismiss")
                }
            }
        }
    }

    // Success Message
    uiState.successMessage?.let { message ->
        Card(
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                ),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.weight(1f),
                )
                TextButton(onClick = onClearSuccess) {
                    Text("Dismiss")
                }
            }
        }
    }

    // Connection Test Result
    uiState.connectionTestResult?.let { result ->
        val (backgroundColor, textColor, message) =
            when (result) {
                is ConnectionTestResult.Success ->
                    Triple(
                        MaterialTheme.colorScheme.primaryContainer,
                        MaterialTheme.colorScheme.onPrimaryContainer,
                        "Connection test successful!",
                    )
                is ConnectionTestResult.Failure ->
                    Triple(
                        MaterialTheme.colorScheme.errorContainer,
                        MaterialTheme.colorScheme.onErrorContainer,
                        "Connection test failed: ${result.error}",
                    )
            }

        Card(
            colors = CardDefaults.cardColors(containerColor = backgroundColor),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = textColor,
                    modifier = Modifier.weight(1f),
                )
                TextButton(onClick = onClearTestResult) {
                    Text("Dismiss")
                }
            }
        }
    }
}
