package com.android.proxy_self.presentation.viewmodel

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.proxy_self.presentation.state.SettingsUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for settings screen
 * Manages application settings and preferences
 * Follows Single Responsibility Principle
 */
@HiltViewModel
class SettingsViewModel
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
    ) : ViewModel() {
        private val sharedPreferences: SharedPreferences by lazy {
            context.getSharedPreferences("proxy_settings", Context.MODE_PRIVATE)
        }

        private val _uiState = MutableStateFlow(SettingsUiState())
        val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

        companion object {
            private const val KEY_AUTO_START = "auto_start_enabled"
            private const val KEY_DARK_THEME = "dark_theme_enabled"
            private const val KEY_NOTIFICATIONS = "notifications_enabled"
        }

        init {
            loadSettings()
            loadAppInfo()
        }

        /**
         * Load settings from SharedPreferences
         */
        private fun loadSettings() {
            _uiState.value =
                _uiState.value.copy(
                    isAutoStartEnabled = sharedPreferences.getBoolean(KEY_AUTO_START, false),
                    isDarkThemeEnabled = sharedPreferences.getBoolean(KEY_DARK_THEME, false),
                    isNotificationsEnabled = sharedPreferences.getBoolean(KEY_NOTIFICATIONS, true),
                )
        }

        /**
         * Load app information
         */
        private fun loadAppInfo() {
            try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                _uiState.value =
                    _uiState.value.copy(
                        appVersion = packageInfo.versionName ?: "Unknown",
                        buildNumber = packageInfo.longVersionCode.toString(),
                    )
            } catch (e: Exception) {
                _uiState.value =
                    _uiState.value.copy(
                        appVersion = "Unknown",
                        buildNumber = "Unknown",
                    )
            }
        }

        /**
         * Toggle auto-start setting
         */
        fun toggleAutoStart() {
            val newValue = !_uiState.value.isAutoStartEnabled
            updateSetting(KEY_AUTO_START, newValue) {
                _uiState.value = _uiState.value.copy(isAutoStartEnabled = newValue)
            }
        }

        /**
         * Toggle dark theme setting
         */
        fun toggleDarkTheme() {
            val newValue = !_uiState.value.isDarkThemeEnabled
            updateSetting(KEY_DARK_THEME, newValue) {
                _uiState.value = _uiState.value.copy(isDarkThemeEnabled = newValue)
            }
        }

        /**
         * Toggle notifications setting
         */
        fun toggleNotifications() {
            val newValue = !_uiState.value.isNotificationsEnabled
            updateSetting(KEY_NOTIFICATIONS, newValue) {
                _uiState.value = _uiState.value.copy(isNotificationsEnabled = newValue)
            }
        }

        /**
         * Export configuration (placeholder)
         */
        fun exportConfiguration() {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isExporting = true)

                try {
                    // TODO: Implement actual export functionality
                    // This would involve:
                    // 1. Getting current proxy configuration
                    // 2. Serializing to JSON
                    // 3. Saving to file or sharing

                    kotlinx.coroutines.delay(1000) // Simulate export process

                    _uiState.value =
                        _uiState.value.copy(
                            isExporting = false,
                            message = "Configuration exported successfully",
                        )
                } catch (e: Exception) {
                    _uiState.value =
                        _uiState.value.copy(
                            isExporting = false,
                            error = "Failed to export configuration: ${e.message}",
                        )
                }
            }
        }

        /**
         * Import configuration (placeholder)
         */
        fun importConfiguration() {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isImporting = true)

                try {
                    // TODO: Implement actual import functionality
                    // This would involve:
                    // 1. File picker to select configuration file
                    // 2. Parsing JSON configuration
                    // 3. Validating configuration
                    // 4. Saving to database

                    kotlinx.coroutines.delay(1000) // Simulate import process

                    _uiState.value =
                        _uiState.value.copy(
                            isImporting = false,
                            message = "Configuration imported successfully",
                        )
                } catch (e: Exception) {
                    _uiState.value =
                        _uiState.value.copy(
                            isImporting = false,
                            error = "Failed to import configuration: ${e.message}",
                        )
                }
            }
        }

        /**
         * Clear all data
         */
        fun clearAllData() {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isClearing = true)

                try {
                    // TODO: Implement actual data clearing
                    // This would involve:
                    // 1. Clearing database
                    // 2. Clearing encrypted preferences
                    // 3. Clearing regular preferences
                    // 4. Stopping any running services

                    kotlinx.coroutines.delay(1000) // Simulate clearing process

                    // Reset settings to defaults
                    sharedPreferences.edit().clear().apply()
                    loadSettings()

                    _uiState.value =
                        _uiState.value.copy(
                            isClearing = false,
                            message = "All data cleared successfully",
                        )
                } catch (e: Exception) {
                    _uiState.value =
                        _uiState.value.copy(
                            isClearing = false,
                            error = "Failed to clear data: ${e.message}",
                        )
                }
            }
        }

        /**
         * Clear message
         */
        fun clearMessage() {
            _uiState.value = _uiState.value.copy(message = null)
        }

        /**
         * Clear error
         */
        fun clearError() {
            _uiState.value = _uiState.value.copy(error = null)
        }

        /**
         * Update setting in SharedPreferences
         */
        private fun updateSetting(
            key: String,
            value: Boolean,
            onSuccess: () -> Unit,
        ) {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isSaving = true)

                try {
                    sharedPreferences.edit()
                        .putBoolean(key, value)
                        .apply()

                    onSuccess()

                    _uiState.value =
                        _uiState.value.copy(
                            isSaving = false,
                            message = "Setting updated successfully",
                        )
                } catch (e: Exception) {
                    _uiState.value =
                        _uiState.value.copy(
                            isSaving = false,
                            error = "Failed to update setting: ${e.message}",
                        )
                }
            }
        }

        /**
         * Get auto-start setting
         */
        fun isAutoStartEnabled(): Boolean = _uiState.value.isAutoStartEnabled

        /**
         * Get dark theme setting
         */
        fun isDarkThemeEnabled(): Boolean = _uiState.value.isDarkThemeEnabled

        /**
         * Get notifications setting
         */
        fun isNotificationsEnabled(): Boolean = _uiState.value.isNotificationsEnabled
    }
