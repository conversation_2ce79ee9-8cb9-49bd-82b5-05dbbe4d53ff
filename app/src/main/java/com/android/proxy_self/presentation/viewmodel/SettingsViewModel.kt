package com.android.proxy_self.presentation.viewmodel

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.usecases.GetProxyConfigUseCase
import com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase
import com.android.proxy_self.infrastructure.utils.DebugLogger
import com.android.proxy_self.presentation.state.SettingsUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

/**
 * ViewModel for settings screen
 * Manages application settings and preferences
 * Follows Single Responsibility Principle
 */
@HiltViewModel
class SettingsViewModel
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val getProxyConfigUseCase: GetProxyConfigUseCase,
        private val saveProxyConfigUseCase: SaveProxyConfigUseCase,
    ) : ViewModel() {
        private val sharedPreferences: SharedPreferences by lazy {
            context.getSharedPreferences("proxy_settings", Context.MODE_PRIVATE)
        }

        private val _uiState = MutableStateFlow(SettingsUiState())
        val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

        companion object {
            private const val KEY_AUTO_START = "auto_start_enabled"
            private const val KEY_DARK_THEME = "dark_theme_enabled"
            private const val KEY_NOTIFICATIONS = "notifications_enabled"
        }

        init {
            loadSettings()
            loadAppInfo()
        }

        /**
         * Load settings from SharedPreferences
         */
        private fun loadSettings() {
            _uiState.value =
                _uiState.value.copy(
                    isAutoStartEnabled = sharedPreferences.getBoolean(KEY_AUTO_START, false),
                    isDarkThemeEnabled = sharedPreferences.getBoolean(KEY_DARK_THEME, false),
                    isNotificationsEnabled = sharedPreferences.getBoolean(KEY_NOTIFICATIONS, true),
                )
        }

        /**
         * Load app information
         */
        private fun loadAppInfo() {
            try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                _uiState.value =
                    _uiState.value.copy(
                        appVersion = packageInfo.versionName ?: "Unknown",
                        buildNumber = packageInfo.longVersionCode.toString(),
                    )
            } catch (e: Exception) {
                _uiState.value =
                    _uiState.value.copy(
                        appVersion = "Unknown",
                        buildNumber = "Unknown",
                    )
            }
        }

        /**
         * Toggle auto-start setting
         */
        fun toggleAutoStart() {
            val newValue = !_uiState.value.isAutoStartEnabled
            updateSetting(KEY_AUTO_START, newValue) {
                _uiState.value = _uiState.value.copy(isAutoStartEnabled = newValue)
            }
        }

        /**
         * Toggle dark theme setting
         */
        fun toggleDarkTheme() {
            val newValue = !_uiState.value.isDarkThemeEnabled
            updateSetting(KEY_DARK_THEME, newValue) {
                _uiState.value = _uiState.value.copy(isDarkThemeEnabled = newValue)
            }
        }

        /**
         * Toggle notifications setting
         */
        fun toggleNotifications() {
            val newValue = !_uiState.value.isNotificationsEnabled
            updateSetting(KEY_NOTIFICATIONS, newValue) {
                _uiState.value = _uiState.value.copy(isNotificationsEnabled = newValue)
            }
        }

        /**
         * Export configuration (placeholder)
         */
        fun exportConfiguration() {
            viewModelScope.launch {
                DebugLogger.methodEntry(DebugLogger.Tags.VIEWMODEL, "exportConfiguration")
                _uiState.value = _uiState.value.copy(isExporting = true)

                try {
                    // 1. Get current proxy configuration
                    getProxyConfigUseCase().collect { result ->
                        result.fold(
                            onSuccess = { config ->
                                // 2. Serialize to JSON
                                val json = Json { prettyPrint = true }
                                val configJson = json.encodeToString(config)

                                // 3. Save to file
                                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                                val fileName = "proxy_config_$timestamp.json"
                                val downloadsDir = File(context.getExternalFilesDir(null), "exports")

                                if (!downloadsDir.exists()) {
                                    downloadsDir.mkdirs()
                                }

                                val file = File(downloadsDir, fileName)
                                FileOutputStream(file).use { output ->
                                    output.write(configJson.toByteArray())
                                }

                                DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Configuration exported to: ${file.absolutePath}")

                                _uiState.value = _uiState.value.copy(
                                    isExporting = false,
                                    message = "Configuration exported to: ${file.absolutePath}",
                                )
                            },
                            onFailure = { error ->
                                DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Failed to get config for export", error)
                                _uiState.value = _uiState.value.copy(
                                    isExporting = false,
                                    error = "Failed to get configuration: ${error.message}",
                                )
                            }
                        )
                    }
                } catch (e: Exception) {
                    DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Export failed", e)
                    _uiState.value = _uiState.value.copy(
                        isExporting = false,
                        error = "Failed to export configuration: ${e.message}",
                    )
                }
            }
        }

        /**
         * Import configuration from file
         */
        fun importConfiguration(filePath: String? = null) {
            viewModelScope.launch {
                DebugLogger.methodEntry(DebugLogger.Tags.VIEWMODEL, "importConfiguration", mapOf("filePath" to filePath))
                _uiState.value = _uiState.value.copy(isImporting = true)

                try {
                    // For now, try to import from the most recent export file
                    val exportsDir = File(context.getExternalFilesDir(null), "exports")
                    val configFiles = exportsDir.listFiles { file ->
                        file.name.startsWith("proxy_config_") && file.name.endsWith(".json")
                    }?.sortedByDescending { it.lastModified() }

                    if (configFiles.isNullOrEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            isImporting = false,
                            error = "No configuration files found to import",
                        )
                        return@launch
                    }

                    // Use the most recent file
                    val configFile = configFiles.first()
                    val configJson = configFile.readText()

                    // 2. Parse JSON configuration
                    val json = Json { ignoreUnknownKeys = true }
                    val config = json.decodeFromString<ProxyConfig>(configJson)

                    // 3. Validate configuration
                    if (config.serverAddress.isBlank() || config.serverPort !in 1..65535) {
                        throw IllegalArgumentException("Invalid configuration: server address or port")
                    }

                    // 4. Save to database
                    saveProxyConfigUseCase(config).collect { result ->
                        result.fold(
                            onSuccess = { success ->
                                if (success) {
                                    DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Configuration imported successfully from: ${configFile.name}")
                                    _uiState.value = _uiState.value.copy(
                                        isImporting = false,
                                        message = "Configuration imported successfully from: ${configFile.name}",
                                    )
                                } else {
                                    _uiState.value = _uiState.value.copy(
                                        isImporting = false,
                                        error = "Failed to save imported configuration",
                                    )
                                }
                            },
                            onFailure = { error ->
                                DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Failed to save imported config", error)
                                _uiState.value = _uiState.value.copy(
                                    isImporting = false,
                                    error = "Failed to save configuration: ${error.message}",
                                )
                            }
                        )
                    }
                } catch (e: Exception) {
                    DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Import failed", e)
                    _uiState.value = _uiState.value.copy(
                        isImporting = false,
                        error = "Failed to import configuration: ${e.message}",
                    )
                }
            }
        }

        /**
         * Clear all data
         */
        fun clearAllData() {
            viewModelScope.launch {
                DebugLogger.methodEntry(DebugLogger.Tags.VIEWMODEL, "clearAllData")
                _uiState.value = _uiState.value.copy(isClearing = true)

                try {
                    // 1. Clear regular preferences
                    DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Clearing SharedPreferences")
                    sharedPreferences.edit().clear().apply()

                    // 2. Clear exported files
                    DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Clearing exported files")
                    val exportsDir = File(context.getExternalFilesDir(null), "exports")
                    if (exportsDir.exists()) {
                        exportsDir.listFiles()?.forEach { file ->
                            if (file.delete()) {
                                DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Deleted file: ${file.name}")
                            }
                        }
                    }

                    // 3. Clear app cache
                    DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Clearing app cache")
                    context.cacheDir.listFiles()?.forEach { file ->
                        if (file.isDirectory) {
                            file.deleteRecursively()
                        } else {
                            file.delete()
                        }
                    }

                    // Reset settings to defaults
                    loadSettings()

                    DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "All data cleared successfully")
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        message = "All data cleared successfully",
                    )
                } catch (e: Exception) {
                    DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Failed to clear data", e)
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        error = "Failed to clear data: ${e.message}",
                    )
                }
            }
        }

        /**
         * Clear message
         */
        fun clearMessage() {
            _uiState.value = _uiState.value.copy(message = null)
        }

        /**
         * Clear error
         */
        fun clearError() {
            _uiState.value = _uiState.value.copy(error = null)
        }

        /**
         * Update setting in SharedPreferences
         */
        private fun updateSetting(
            key: String,
            value: Boolean,
            onSuccess: () -> Unit,
        ) {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isSaving = true)

                try {
                    sharedPreferences.edit()
                        .putBoolean(key, value)
                        .apply()

                    onSuccess()

                    _uiState.value =
                        _uiState.value.copy(
                            isSaving = false,
                            message = "Setting updated successfully",
                        )
                } catch (e: Exception) {
                    _uiState.value =
                        _uiState.value.copy(
                            isSaving = false,
                            error = "Failed to update setting: ${e.message}",
                        )
                }
            }
        }

        /**
         * Get auto-start setting
         */
        fun isAutoStartEnabled(): Boolean = _uiState.value.isAutoStartEnabled

        /**
         * Get dark theme setting
         */
        fun isDarkThemeEnabled(): Boolean = _uiState.value.isDarkThemeEnabled

        /**
         * Get notifications setting
         */
        fun isNotificationsEnabled(): Boolean = _uiState.value.isNotificationsEnabled
    }
