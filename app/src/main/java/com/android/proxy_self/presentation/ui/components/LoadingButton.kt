package com.android.proxy_self.presentation.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Reusable button component with loading state
 * Implements Material 3 design principles
 */
@Composable
fun LoadingButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    variant: ButtonVariant = ButtonVariant.Filled,
) {
    when (variant) {
        ButtonVariant.Filled -> {
            Button(
                onClick = onClick,
                enabled = enabled && !isLoading,
                modifier = modifier,
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                )
            }
        }
        ButtonVariant.Outlined -> {
            OutlinedButton(
                onClick = onClick,
                enabled = enabled && !isLoading,
                modifier = modifier,
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                )
            }
        }
        ButtonVariant.Text -> {
            TextButton(
                onClick = onClick,
                enabled = enabled && !isLoading,
                modifier = modifier,
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                )
            }
        }
    }
}

@Composable
private fun ButtonContent(
    text: String,
    isLoading: Boolean,
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
            )
        }
        Text(text = text)
    }
}

/**
 * Button variants for different use cases
 */
enum class ButtonVariant {
    Filled,
    Outlined,
    Text,
}
