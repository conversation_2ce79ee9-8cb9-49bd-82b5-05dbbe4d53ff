package com.android.proxy_self.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.android.proxy_self.presentation.state.AboutUiState
import com.android.proxy_self.presentation.state.LibraryInfo

/**
 * About screen showing app information and credits
 * Implements Material 3 design principles
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(onNavigateBack: () -> Unit) {
    val aboutState =
        AboutUiState(
            appName = "Proxy Self",
            appVersion = "1.0.0",
            buildNumber = "1",
            buildDate = "July 13, 2025",
            developerName = "Android Developer",
            developerEmail = "<EMAIL>",
            openSourceLibraries = getOpenSourceLibraries(),
        )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("About") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                        )
                    }
                },
            )
        },
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // App Information
            AppInfoSection(aboutState)

            // Developer Information
            DeveloperInfoSection(aboutState)

            // Features
            FeaturesSection()

            // Open Source Libraries
            OpenSourceLibrariesSection(aboutState.openSourceLibraries)

            // Legal
            LegalSection()
        }
    }
}

@Composable
private fun AppInfoSection(aboutState: AboutUiState) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            Text(
                text = aboutState.appName,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
            )

            Text(
                text = "Version ${aboutState.appVersion} (${aboutState.buildNumber})",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            Text(
                text = "Built on ${aboutState.buildDate}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Android Proxy Application with Authentication and Domain-Based Routing",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

@Composable
private fun DeveloperInfoSection(aboutState: AboutUiState) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "Developer",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
            )

            InfoRow(
                label = "Name",
                value = aboutState.developerName,
            )

            InfoRow(
                label = "Email",
                value = aboutState.developerEmail,
            )
        }
    }
}

@Composable
private fun FeaturesSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "Features",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
            )

            val features =
                listOf(
                    "SOCKS5 and HTTP proxy support",
                    "Proxy authentication",
                    "Domain-based routing",
                    "VPN service integration",
                    "Quick Settings tile",
                    "Background service",
                    "Auto-start capability",
                    "Material 3 design",
                    "Dark theme support",
                    "Configuration export/import",
                )

            features.forEach { feature ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "•",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(end = 8.dp),
                    )
                    Text(
                        text = feature,
                        style = MaterialTheme.typography.bodyMedium,
                    )
                }
            }
        }
    }
}

@Composable
private fun OpenSourceLibrariesSection(libraries: List<LibraryInfo>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "Open Source Libraries",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
            )

            libraries.forEach { library ->
                LibraryItem(library = library)
            }
        }
    }
}

@Composable
private fun LibraryItem(library: LibraryInfo) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp),
    ) {
        Text(
            text = library.name,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
        )
        Text(
            text = "Version: ${library.version}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = "License: ${library.license}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )

        if (library != getOpenSourceLibraries().last()) {
            Divider(modifier = Modifier.padding(top = 8.dp))
        }
    }
}

@Composable
private fun LegalSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "Legal",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
            )

            Text(
                text = "This application is provided as-is without any warranty. Use at your own risk.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            Text(
                text = "© 2025 Android Developer. All rights reserved.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

@Composable
private fun InfoRow(
    label: String,
    value: String,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
        )
    }
}

private fun getOpenSourceLibraries(): List<LibraryInfo> {
    return listOf(
        LibraryInfo(
            name = "Jetpack Compose",
            version = "2025.06.00",
            license = "Apache 2.0",
            url = "https://developer.android.com/jetpack/compose",
        ),
        LibraryInfo(
            name = "Hilt",
            version = "2.51.1",
            license = "Apache 2.0",
            url = "https://dagger.dev/hilt/",
        ),
        LibraryInfo(
            name = "Room",
            version = "2.6.1",
            license = "Apache 2.0",
            url = "https://developer.android.com/training/data-storage/room",
        ),
        LibraryInfo(
            name = "OkHttp",
            version = "4.12.0",
            license = "Apache 2.0",
            url = "https://square.github.io/okhttp/",
        ),
        LibraryInfo(
            name = "Retrofit",
            version = "2.11.0",
            license = "Apache 2.0",
            url = "https://square.github.io/retrofit/",
        ),
        LibraryInfo(
            name = "Security Crypto",
            version = "1.1.0-alpha06",
            license = "Apache 2.0",
            url = "https://developer.android.com/jetpack/androidx/releases/security",
        ),
    )
}
