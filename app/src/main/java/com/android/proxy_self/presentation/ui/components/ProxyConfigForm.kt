package com.android.proxy_self.presentation.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.android.proxy_self.domain.entities.ProxyType
import com.android.proxy_self.presentation.state.ProxyUiState

/**
 * Reusable form component for proxy configuration
 * Implements Material 3 design principles
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProxyConfigForm(
    uiState: ProxyUiState,
    onServerAddressChange: (String) -> Unit,
    onServerPortChange: (String) -> Unit,
    onUsernameChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onDomainsChange: (String) -> Unit,
    onProxyTypeChange: (ProxyType) -> Unit,
    onPasswordVisibilityToggle: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        // Server Configuration Section
        Card(
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                Text(
                    text = "Server Configuration",
                    style = MaterialTheme.typography.titleMedium,
                )

                // Server Address
                OutlinedTextField(
                    value = uiState.serverAddress,
                    onValueChange = onServerAddressChange,
                    label = { Text("Server Address") },
                    placeholder = { Text("*************") },
                    isError = uiState.validationErrors.containsKey("serverAddress"),
                    supportingText = {
                        uiState.validationErrors["serverAddress"]?.let { error ->
                            Text(
                                text = error,
                                color = MaterialTheme.colorScheme.error,
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                )

                // Server Port
                OutlinedTextField(
                    value = uiState.serverPort,
                    onValueChange = onServerPortChange,
                    label = { Text("Port") },
                    placeholder = { Text("1080") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = uiState.validationErrors.containsKey("serverPort"),
                    supportingText = {
                        uiState.validationErrors["serverPort"]?.let { error ->
                            Text(
                                text = error,
                                color = MaterialTheme.colorScheme.error,
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                )

                // Proxy Type Dropdown
                var expanded by remember { mutableStateOf(false) }

                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded },
                ) {
                    OutlinedTextField(
                        value = uiState.selectedProxyType.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Proxy Type") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .menuAnchor(),
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false },
                    ) {
                        ProxyType.values().forEach { proxyType ->
                            DropdownMenuItem(
                                text = { Text(proxyType.displayName) },
                                onClick = {
                                    onProxyTypeChange(proxyType)
                                    expanded = false
                                },
                            )
                        }
                    }
                }
            }
        }

        // Authentication Section
        Card(
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                Text(
                    text = "Authentication",
                    style = MaterialTheme.typography.titleMedium,
                )

                // Username
                OutlinedTextField(
                    value = uiState.username,
                    onValueChange = onUsernameChange,
                    label = { Text("Username") },
                    isError = uiState.validationErrors.containsKey("username"),
                    supportingText = {
                        uiState.validationErrors["username"]?.let { error ->
                            Text(
                                text = error,
                                color = MaterialTheme.colorScheme.error,
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                )

                // Password
                OutlinedTextField(
                    value = uiState.password,
                    onValueChange = onPasswordChange,
                    label = { Text("Password") },
                    visualTransformation =
                        if (uiState.isPasswordVisible) {
                            VisualTransformation.None
                        } else {
                            PasswordVisualTransformation()
                        },
                    trailingIcon = {
                        IconButton(onClick = onPasswordVisibilityToggle) {
                            Icon(
                                imageVector =
                                    if (uiState.isPasswordVisible) {
                                        Icons.Default.VisibilityOff
                                    } else {
                                        Icons.Default.Visibility
                                    },
                                contentDescription =
                                    if (uiState.isPasswordVisible) {
                                        "Hide password"
                                    } else {
                                        "Show password"
                                    },
                            )
                        }
                    },
                    isError = uiState.validationErrors.containsKey("password"),
                    supportingText = {
                        uiState.validationErrors["password"]?.let { error ->
                            Text(
                                text = error,
                                color = MaterialTheme.colorScheme.error,
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                )
            }
        }

        // Domains Section
        Card(
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                Text(
                    text = "Domain Whitelist",
                    style = MaterialTheme.typography.titleMedium,
                )

                OutlinedTextField(
                    value = uiState.domains,
                    onValueChange = onDomainsChange,
                    label = { Text("Domains (comma-separated)") },
                    placeholder = { Text("example.com,google.com,github.com") },
                    isError = uiState.validationErrors.containsKey("domains"),
                    supportingText = {
                        uiState.validationErrors["domains"]?.let { error ->
                            Text(
                                text = error,
                                color = MaterialTheme.colorScheme.error,
                            )
                        } ?: Text(
                            text = "Only traffic to these domains will use the proxy",
                            style = MaterialTheme.typography.bodySmall,
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 4,
                )
            }
        }
    }
}
