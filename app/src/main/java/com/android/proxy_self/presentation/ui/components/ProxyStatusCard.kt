package com.android.proxy_self.presentation.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.RadioButtonUnchecked
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import com.android.proxy_self.presentation.ui.theme.*

/**
 * Card component displaying current proxy status
 * Shows connection state, statistics, and active domains
 */
@Composable
fun ProxyStatusCard(
    statusInfo: ProxyStatusInfo?,
    isEnabled: <PERSON><PERSON><PERSON>,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors =
            CardDefaults.cardColors(
                containerColor = getStatusBackgroundColor(statusInfo?.status, isEnabled),
            ),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            // Status Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = "Proxy Status",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                )

                StatusIndicator(
                    status = statusInfo?.status,
                    isEnabled = isEnabled,
                )
            }

            // Status Details
            if (statusInfo != null) {
                StatusDetails(statusInfo = statusInfo)
            } else {
                Text(
                    text = if (isEnabled) "Starting..." else "Disconnected",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }

            // Error Message
            statusInfo?.lastError?.let { error ->
                Card(
                    colors =
                        CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                        ),
                ) {
                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.padding(12.dp),
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    status: ProxyStatus?,
    isEnabled: Boolean,
) {
    val (icon, color, text) =
        when {
            !isEnabled ->
                Triple(
                    Icons.Default.RadioButtonUnchecked,
                    StatusDisconnected,
                    "Disconnected",
                )
            status == ProxyStatus.CONNECTED ->
                Triple(
                    Icons.Default.CheckCircle,
                    StatusConnected,
                    "Connected",
                )
            status == ProxyStatus.CONNECTING || status == ProxyStatus.RECONNECTING ->
                Triple(
                    Icons.Default.Sync,
                    StatusConnecting,
                    "Connecting",
                )
            status == ProxyStatus.ERROR ->
                Triple(
                    Icons.Default.Error,
                    StatusError,
                    "Error",
                )
            else ->
                Triple(
                    Icons.Default.RadioButtonUnchecked,
                    StatusDisconnected,
                    "Disconnected",
                )
        }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = color,
            modifier = Modifier.size(20.dp),
        )
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge,
            color = color,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
private fun StatusDetails(statusInfo: ProxyStatusInfo) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // Connection Duration
        statusInfo.getConnectionDuration()?.let { duration ->
            StatusDetailRow(
                label = "Connected for:",
                value = duration,
            )
        }

        // Active Connections
        if (statusInfo.activeConnections > 0) {
            StatusDetailRow(
                label = "Active connections:",
                value = statusInfo.activeConnections.toString(),
            )
        }

        // Data Transferred
        if (statusInfo.bytesTransferred > 0) {
            StatusDetailRow(
                label = "Data transferred:",
                value = statusInfo.getFormattedBytesTransferred(),
            )
        }

        // Active Domains
        if (statusInfo.activeDomains.isNotEmpty()) {
            StatusDetailRow(
                label = "Active domains:",
                value = "${statusInfo.activeDomains.size} domains",
            )

            // Show first few domains
            val domainsToShow = statusInfo.activeDomains.take(3)
            domainsToShow.forEach { domain ->
                Text(
                    text = "• $domain",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(start = 16.dp),
                )
            }

            if (statusInfo.activeDomains.size > 3) {
                Text(
                    text = "• and ${statusInfo.activeDomains.size - 3} more...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(start = 16.dp),
                )
            }
        }
    }
}

@Composable
private fun StatusDetailRow(
    label: String,
    value: String,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
private fun getStatusBackgroundColor(
    status: ProxyStatus?,
    isEnabled: Boolean,
): androidx.compose.ui.graphics.Color {
    return when {
        !isEnabled -> MaterialTheme.colorScheme.surfaceVariant
        status == ProxyStatus.CONNECTED -> MaterialTheme.colorScheme.primaryContainer
        status == ProxyStatus.ERROR -> MaterialTheme.colorScheme.errorContainer
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
}
