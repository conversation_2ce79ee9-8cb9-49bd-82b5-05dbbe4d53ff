package com.android.proxy_self.domain.usecases.base

import kotlinx.coroutines.flow.Flow

/**
 * Base use case class following SOLID principles
 * Implements Single Responsibility and Open/Closed principles
 *
 * @param T Return type
 * @param P Parameters type
 */
abstract class BaseUseCase<T, P> {
    /**
     * Execute the use case with given parameters
     * @param params Parameters for the use case
     * @return Flow of Result containing the operation result
     */
    abstract suspend fun execute(params: P): Flow<Result<T>>

    /**
     * Operator function to make use case callable
     * @param params Parameters for the use case
     * @return Flow of Result containing the operation result
     */
    suspend operator fun invoke(params: P): Flow<Result<T>> = execute(params)
}

/**
 * Base use case for operations that don't require parameters
 *
 * @param T Return type
 */
abstract class BaseUseCaseNoParams<T> {
    /**
     * Execute the use case without parameters
     * @return Flow of Result containing the operation result
     */
    abstract suspend fun execute(): Flow<Result<T>>

    /**
     * Operator function to make use case callable
     * @return Flow of Result containing the operation result
     */
    suspend operator fun invoke(): Flow<Result<T>> = execute()
}

/**
 * Extension function to create success result
 */
fun <T> T.asSuccess(): Result<T> = Result.success(this)

/**
 * Extension function to create failure result
 */
fun <T> Throwable.asFailure(): Result<T> = Result.failure(this)
