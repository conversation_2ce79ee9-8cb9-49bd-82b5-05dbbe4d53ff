package com.android.proxy_self.domain.usecases

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for getting proxy configuration
 * Implements Single Responsibility Principle
 */
@Singleton
class GetProxyConfigUseCase
    @Inject
    constructor(
        private val repository: ProxyRepository,
    ) : BaseUseCaseNoParams<ProxyConfig>() {
        /**
         * Execute get proxy configuration operation
         * @return Flow of Result containing ProxyConfig
         */
        override suspend fun execute(): Flow<Result<ProxyConfig>> {
            return repository.getProxyConfig()
        }
    }
