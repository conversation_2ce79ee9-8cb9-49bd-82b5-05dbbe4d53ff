package com.android.proxy_self.domain.entities

import kotlinx.serialization.Serializable

/**
 * Enum representing supported proxy types
 */
@Serializable
enum class ProxyType(val displayName: String, val value: String) {
    SOCKS5("SOCKS5", "socks5"),
    HTTP("HTTP", "http"),
    ;

    companion object {
        /**
         * Get ProxyType from string value
         */
        fun fromValue(value: String): ProxyType {
            return values().find { it.value.equals(value, ignoreCase = true) } ?: SOCKS5
        }

        /**
         * Get all display names for UI
         */
        fun getDisplayNames(): List<String> {
            return values().map { it.displayName }
        }
    }
}
