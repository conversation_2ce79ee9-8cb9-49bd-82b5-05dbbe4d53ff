package com.android.proxy_self.domain.repositories

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for proxy operations
 * Follows Dependency Inversion Principle - domain layer defines the contract
 */
interface ProxyRepository {
    /**
     * Get current proxy configuration
     * @return Flow of Result containing ProxyConfig
     */
    suspend fun getProxyConfig(): Flow<Result<ProxyConfig>>

    /**
     * Save proxy configuration
     * @param config ProxyConfig to save
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun saveProxyConfig(config: ProxyConfig): Flow<Result<Boolean>>

    /**
     * Start proxy service with given configuration
     * @param config ProxyConfig to use for starting proxy
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun startProxy(config: ProxyConfig): Flow<Result<Boolean>>

    /**
     * Stop proxy service
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun stopProxy(): Flow<Result<Boolean>>

    /**
     * Test proxy connection with given configuration
     * @param config ProxyConfig to test
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun testConnection(config: ProxyConfig): Flow<Result<Boolean>>

    /**
     * Get current proxy status information
     * @return Flow of ProxyStatusInfo
     */
    fun getProxyStatus(): Flow<ProxyStatusInfo>

    /**
     * Check if proxy is currently enabled
     * @return Flow of Boolean indicating proxy state
     */
    fun isProxyEnabled(): Flow<Boolean>

    /**
     * Get all saved proxy configurations
     * @return Flow of Result containing List of ProxyConfig
     */
    suspend fun getAllConfigs(): Flow<Result<List<ProxyConfig>>>

    /**
     * Delete proxy configuration
     * @param configId ID of configuration to delete
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun deleteConfig(configId: Long): Flow<Result<Boolean>>

    /**
     * Update proxy configuration
     * @param config ProxyConfig to update
     * @return Flow of Result containing Boolean success indicator
     */
    suspend fun updateConfig(config: ProxyConfig): Flow<Result<Boolean>>
}
