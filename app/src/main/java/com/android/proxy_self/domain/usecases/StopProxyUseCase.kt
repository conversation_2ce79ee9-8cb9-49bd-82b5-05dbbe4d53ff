package com.android.proxy_self.domain.usecases

import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.domain.usecases.base.BaseUseCaseNoParams
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for stopping proxy service
 * Implements Single Responsibility Principle
 */
@Singleton
class StopProxyUseCase
    @Inject
    constructor(
        private val repository: ProxyRepository,
    ) : BaseUseCaseNoParams<Boolean>() {
        /**
         * Execute proxy stop operation
         * @return Flow of Result containing Boolean success indicator
         */
        override suspend fun execute(): Flow<Result<Boolean>> {
            return repository.stopProxy()
        }
    }
