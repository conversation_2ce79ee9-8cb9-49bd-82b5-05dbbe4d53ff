package com.android.proxy_self.domain.usecases

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.domain.usecases.base.BaseUseCase
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for testing proxy connection
 * Implements Single Responsibility Principle
 */
@Singleton
class TestProxyConnectionUseCase
    @Inject
    constructor(
        private val repository: ProxyRepository,
    ) : BaseUseCase<Boolean, ProxyConfig>() {
        /**
         * Execute test proxy connection operation
         * @param params ProxyConfig to test
         * @return Flow of Result containing Boolean success indicator
         */
        override suspend fun execute(params: ProxyConfig): Flow<Result<Boolean>> {
            return repository.testConnection(params)
        }
    }
