package com.android.proxy_self.domain.entities

/**
 * Enum representing proxy connection status
 */
enum class ProxyStatus(val displayName: String) {
    DISCONNECTED("Disconnected"),
    CONNECTING("Connecting"),
    CONNECTED("Connected"),
    ERROR("Error"),
    RECONNECTING("Reconnecting"),
    ;

    /**
     * Check if proxy is in active state
     */
    fun isActive(): Boolean {
        return this == CONNECTED || this == CONNECTING || this == RECONNECTING
    }

    /**
     * Check if proxy has error
     */
    fun hasError(): Boolean {
        return this == ERROR
    }
}

/**
 * Data class representing detailed proxy status information
 */
data class ProxyStatusInfo(
    val status: ProxyStatus = ProxyStatus.DISCONNECTED,
    val connectionTime: Long? = null,
    val lastError: String? = null,
    val bytesTransferred: Long = 0L,
    val activeConnections: Int = 0,
    val activeDomains: List<String> = emptyList(),
    val timestamp: Long = System.currentTimeMillis(),
) {
    /**
     * Get formatted connection duration
     */
    fun getConnectionDuration(): String? {
        return connectionTime?.let { startTime ->
            val duration = System.currentTimeMillis() - startTime
            val seconds = duration / 1000
            val minutes = seconds / 60
            val hours = minutes / 60

            when {
                hours > 0 -> "${hours}h ${minutes % 60}m"
                minutes > 0 -> "${minutes}m ${seconds % 60}s"
                else -> "${seconds}s"
            }
        }
    }

    /**
     * Get formatted bytes transferred
     */
    fun getFormattedBytesTransferred(): String {
        return when {
            bytesTransferred >= 1024 * 1024 * 1024 -> "%.2f GB".format(bytesTransferred / (1024.0 * 1024.0 * 1024.0))
            bytesTransferred >= 1024 * 1024 -> "%.2f MB".format(bytesTransferred / (1024.0 * 1024.0))
            bytesTransferred >= 1024 -> "%.2f KB".format(bytesTransferred / 1024.0)
            else -> "$bytesTransferred B"
        }
    }
}
