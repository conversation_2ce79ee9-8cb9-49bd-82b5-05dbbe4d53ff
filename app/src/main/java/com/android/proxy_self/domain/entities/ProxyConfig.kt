package com.android.proxy_self.domain.entities

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Domain entity representing proxy configuration
 * Contains all necessary information for proxy setup including domain whitelist
 */
@Parcelize
data class ProxyConfig(
    val id: Long = 0,
    val serverAddress: String = "",
    val serverPort: Int = 0,
    val username: String = "",
    val password: String = "",
    val proxyType: ProxyType = ProxyType.SOCKS5,
    val domains: List<String> = emptyList(),
    val isEnabled: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
) : Parcelable {
    /**
     * Validates if the proxy configuration is complete and valid
     */
    fun isValid(): Boolean {
        return serverAddress.isNotBlank() &&
            serverPort in 1..65535 &&
            username.isNotBlank() &&
            password.isNotBlank() &&
            domains.isNotEmpty() &&
            domains.all { it.isNotBlank() }
    }

    /**
     * Returns domains as comma-separated string for storage
     */
    fun getDomainsAsString(): String {
        return domains.joinToString(",")
    }

    companion object {
        /**
         * Creates ProxyConfig from comma-separated domains string
         */
        fun fromDomainsString(
            id: Long = 0,
            serverAddress: String = "",
            serverPort: Int = 0,
            username: String = "",
            password: String = "",
            proxyType: ProxyType = ProxyType.SOCKS5,
            domainsString: String = "",
            isEnabled: Boolean = false,
            createdAt: Long = System.currentTimeMillis(),
            updatedAt: Long = System.currentTimeMillis(),
        ): ProxyConfig {
            val domains =
                if (domainsString.isBlank()) {
                    emptyList()
                } else {
                    domainsString.split(",")
                        .map { it.trim() }
                        .filter { it.isNotBlank() }
                }

            return ProxyConfig(
                id = id,
                serverAddress = serverAddress,
                serverPort = serverPort,
                username = username,
                password = password,
                proxyType = proxyType,
                domains = domains,
                isEnabled = isEnabled,
                createdAt = createdAt,
                updatedAt = updatedAt,
            )
        }
    }
}
