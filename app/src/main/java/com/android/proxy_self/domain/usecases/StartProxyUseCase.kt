package com.android.proxy_self.domain.usecases

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.domain.usecases.base.BaseUseCase
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for starting proxy service
 * Implements Single Responsibility Principle
 */
@Singleton
class StartProxyUseCase
    @Inject
    constructor(
        private val repository: ProxyRepository,
    ) : BaseUseCase<Boolean, ProxyConfig>() {
        /**
         * Execute proxy start operation
         * @param params ProxyConfig containing proxy configuration
         * @return Flow of Result containing Boolean success indicator
         */
        override suspend fun execute(params: ProxyConfig): Flow<Result<Boolean>> {
            return repository.startProxy(params)
        }
    }
