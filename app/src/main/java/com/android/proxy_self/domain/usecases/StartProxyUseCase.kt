package com.android.proxy_self.domain.usecases

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.repositories.ProxyRepository
import com.android.proxy_self.domain.usecases.base.BaseUseCase
import com.android.proxy_self.infrastructure.utils.DebugLogger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for starting proxy service
 * Implements Single Responsibility Principle
 */
@Singleton
class StartProxyUseCase
    @Inject
    constructor(
        private val repository: ProxyRepository,
    ) : BaseUseCase<Boolean, ProxyConfig>() {
        /**
         * Execute proxy start operation
         * @param params ProxyConfig containing proxy configuration
         * @return Flow of Result containing Boolean success indicator
         */
        override suspend fun execute(params: ProxyConfig): Flow<Result<Boolean>> {
            DebugLogger.methodEntry(DebugLogger.Tags.USE_CASE, "StartProxyUseCase.execute",
                mapOf("serverAddress" to params.serverAddress, "serverPort" to params.serverPort))

            return repository.startProxy(params).onEach { result ->
                result.fold(
                    onSuccess = { success ->
                        DebugLogger.d(DebugLogger.Tags.USE_CASE, "StartProxyUseCase completed successfully: $success")
                    },
                    onFailure = { error ->
                        DebugLogger.e(DebugLogger.Tags.USE_CASE, "StartProxyUseCase failed", error)
                    }
                )
            }
        }
    }
