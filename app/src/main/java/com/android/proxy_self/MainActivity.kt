package com.android.proxy_self

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.android.proxy_self.presentation.ui.navigation.ProxyNavigation
import com.android.proxy_self.presentation.ui.theme.ProxySelfTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main activity for the Proxy Self application
 * Uses Jetpack Compose for UI and Hilt for dependency injection
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ProxySelfTheme {
                ProxyNavigation()
            }
        }
    }
}
