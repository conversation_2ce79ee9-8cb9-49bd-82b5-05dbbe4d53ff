package com.android.proxy_self.data.mappers

import com.android.proxy_self.data.datasources.local.ProxyEntity
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for ProxyMapper
 */
class ProxyMapperTest {
    private lateinit var mapper: ProxyMapper

    @Before
    fun setUp() {
        mapper = ProxyMapper()
    }

    @Test
    fun `mapToDomain maps ProxyEntity to ProxyConfig correctly`() {
        // Given
        val entity =
            ProxyEntity(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                encryptedPassword = "encrypted_pass",
                proxyType = "http",
                domains = "example.com,test.org,github.com",
                isEnabled = true,
                createdAt = 1234567890L,
                updatedAt = 1234567891L,
            )

        // When
        val domain = mapper.mapToDomain(entity, "decrypted_pass")

        // Then
        assertEquals("ID should match", 1, domain.id)
        assertEquals("Server address should match", "proxy.example.com", domain.serverAddress)
        assertEquals("Server port should match", 8080, domain.serverPort)
        assertEquals("Username should match", "testuser", domain.username)
        assertEquals("Password should be decrypted", "decrypted_pass", domain.password)
        assertEquals("Type should be HTTP", ProxyType.HTTP, domain.proxyType)
        assertEquals(
            "Domains should be parsed correctly",
            listOf("example.com", "test.org", "github.com"),
            domain.domains,
        )
        assertTrue("Should be enabled", domain.isEnabled)
        assertEquals("Created at should match", 1234567890L, domain.createdAt)
        assertEquals("Updated at should match", 1234567891L, domain.updatedAt)
    }

    @Test
    fun `toDomain handles empty domains correctly`() {
        // Given
        val entity =
            ProxyEntity(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                encryptedPassword = "encrypted_pass",
                proxyType = "http",
                domains = "",
                isEnabled = true,
                createdAt = 1234567890L,
                updatedAt = 1234567890L,
            )

        // When
        val domain = mapper.mapToDomain(entity, "decrypted_pass")

        // Then
        assertTrue("Domains should be empty list", domain.domains.isEmpty())
    }

    @Test
    fun `toDomain handles single domain correctly`() {
        // Given
        val entity =
            ProxyEntity(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                encryptedPassword = "encrypted_pass",
                proxyType = "http",
                domains = "example.com",
                isEnabled = true,
                createdAt = 1234567890L,
                updatedAt = 1234567890L,
            )

        // When
        val domain = mapper.mapToDomain(entity, "decrypted_pass")

        // Then
        assertEquals("Should have one domain", 1, domain.domains.size)
        assertEquals("Domain should match", "example.com", domain.domains[0])
    }

    @Test
    fun `toEntity maps ProxyConfig to ProxyEntity correctly`() {
        // Given
        val domain =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                password = "plain_pass",
                proxyType = ProxyType.HTTP,
                domains = listOf("example.com", "test.org", "github.com"),
                isEnabled = true,
                createdAt = 1234567890L,
                updatedAt = 1234567891L,
            )

        // When
        val entity = mapper.mapToEntity(domain, "encrypted_pass")

        // Then
        assertEquals("ID should match", 1, entity.id)
        assertEquals("Server address should match", "proxy.example.com", entity.serverAddress)
        assertEquals("Server port should match", 8080, entity.serverPort)
        assertEquals("Username should match", "testuser", entity.username)
        assertEquals("Password should be encrypted", "encrypted_pass", entity.encryptedPassword)
        assertEquals("Type should be http string", "http", entity.proxyType)
        assertEquals(
            "Domains should be joined correctly",
            "example.com,test.org,github.com",
            entity.domains,
        )
        assertTrue("Should be enabled", entity.isEnabled)
        assertEquals("Created at should match", 1234567890L, entity.createdAt)
        assertEquals("Updated at should match", 1234567891L, entity.updatedAt)
    }

    @Test
    fun `toEntity handles empty domains correctly`() {
        // Given
        val domain =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                password = "plain_pass",
                proxyType = ProxyType.HTTP,
                domains = emptyList(),
                isEnabled = true,
            )

        // When
        val entity = mapper.mapToEntity(domain, "encrypted_pass")

        // Then
        assertEquals("Domains should be empty string", "", entity.domains)
    }

    @Test
    fun `toEntity handles single domain correctly`() {
        // Given
        val domain =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "testuser",
                password = "plain_pass",
                proxyType = ProxyType.HTTP,
                domains = listOf("example.com"),
                isEnabled = true,
            )

        // When
        val entity = mapper.mapToEntity(domain, "encrypted_pass")

        // Then
        assertEquals("Should have single domain", "example.com", entity.domains)
    }
}
