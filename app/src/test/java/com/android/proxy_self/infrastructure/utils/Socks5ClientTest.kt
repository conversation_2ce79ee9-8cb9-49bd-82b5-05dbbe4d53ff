package com.android.proxy_self.infrastructure.utils

import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.DataInputStream
import java.io.DataOutputStream
import java.net.Socket
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@RunWith(MockitoJUnitRunner::class)
class Socks5ClientTest {

    private lateinit var socks5Client: Socks5Client

    @Before
    fun setUp() {
        socks5Client = Socks5Client(
            proxyHost = "127.0.0.1",
            proxyPort = 1080,
            username = "testuser",
            password = "testpass"
        )
    }

    @Test
    fun `test SOCKS5 authentication negotiation with no auth`() = runTest {
        // Given
        val responseBytes = byteArrayOf(0x05, 0x00) // Version 5, No auth
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performAuthentication",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream) as Boolean

        // Then
        assertEquals(true, result)
    }

    @Test
    fun `test SOCKS5 authentication negotiation with username password`() = runTest {
        // Given
        val responseBytes = byteArrayOf(
            0x05, 0x02, // Version 5, Username/password auth
            0x01, 0x00  // Auth version 1, Success
        )
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performAuthentication",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream) as Boolean

        // Then
        assertEquals(true, result)
    }

    @Test
    fun `test SOCKS5 connection request success`() = runTest {
        // Given
        val responseBytes = byteArrayOf(
            0x05, 0x00, 0x00, 0x01, // Version 5, Success, Reserved, IPv4
            0x7F, 0x00, 0x00, 0x01, // 127.0.0.1
            0x04, 0x38 // Port 1080
        )
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performConnection",
            DataInputStream::class.java,
            DataOutputStream::class.java,
            String::class.java,
            Int::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream, "example.com", 80) as Boolean

        // Then
        assertEquals(true, result)
    }

    @Test
    fun `test SOCKS5 connection request failure`() = runTest {
        // Given
        val responseBytes = byteArrayOf(
            0x05, 0x01, 0x00, 0x01, // Version 5, General failure, Reserved, IPv4
            0x00, 0x00, 0x00, 0x00, // 0.0.0.0
            0x00, 0x00 // Port 0
        )
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performConnection",
            DataInputStream::class.java,
            DataOutputStream::class.java,
            String::class.java,
            Int::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream, "example.com", 80) as Boolean

        // Then
        assertEquals(false, result)
    }

    @Test
    fun `test build connection request for IPv4`() = runTest {
        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "buildConnectionRequest",
            String::class.java,
            Int::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, "127.0.0.1", 80) as ByteArray

        // Then
        assertNotNull(result)
        assertEquals(0x05, result[0].toInt()) // SOCKS version
        assertEquals(0x01, result[1].toInt()) // Connect command
        assertEquals(0x00, result[2].toInt()) // Reserved
        assertEquals(0x01, result[3].toInt()) // IPv4 address type
    }

    @Test
    fun `test build connection request for domain`() = runTest {
        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "buildConnectionRequest",
            String::class.java,
            Int::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, "example.com", 80) as ByteArray

        // Then
        assertNotNull(result)
        assertEquals(0x05, result[0].toInt()) // SOCKS version
        assertEquals(0x01, result[1].toInt()) // Connect command
        assertEquals(0x00, result[2].toInt()) // Reserved
        assertEquals(0x03, result[3].toInt()) // Domain address type
        assertEquals(11, result[4].toInt()) // Domain length
    }

    @Test
    fun `test invalid SOCKS version response`() = runTest {
        // Given
        val responseBytes = byteArrayOf(0x04, 0x00) // Version 4 (invalid), No auth
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performAuthentication",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream) as Boolean

        // Then
        assertEquals(false, result)
    }

    @Test
    fun `test unsupported authentication method`() = runTest {
        // Given
        val responseBytes = byteArrayOf(0x05, 0xFF.toByte()) // Version 5, No acceptable methods
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performAuthentication",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream) as Boolean

        // Then
        assertEquals(false, result)
    }

    @Test
    fun `test username password authentication failure`() = runTest {
        // Given
        val responseBytes = byteArrayOf(0x01, 0x01) // Auth version 1, Failure
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = socks5Client.javaClass.getDeclaredMethod(
            "performUsernamePasswordAuth",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(socks5Client, inputStream, outputStream) as Boolean

        // Then
        assertEquals(false, result)
    }

    @Test
    fun `test client without credentials for username password auth`() = runTest {
        // Given
        val clientWithoutCreds = Socks5Client("127.0.0.1", 1080)
        val responseBytes = byteArrayOf(0x01, 0x00) // Auth version 1, Success
        val inputStream = DataInputStream(ByteArrayInputStream(responseBytes))
        val outputStream = DataOutputStream(ByteArrayOutputStream())

        // When
        val result = clientWithoutCreds.javaClass.getDeclaredMethod(
            "performUsernamePasswordAuth",
            DataInputStream::class.java,
            DataOutputStream::class.java
        ).apply { isAccessible = true }
            .invoke(clientWithoutCreds, inputStream, outputStream) as Boolean

        // Then
        assertEquals(false, result)
    }
}
