package com.android.proxy_self.domain.entities

import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for ProxyConfig entity
 */
class ProxyConfigTest {
    @Test
    fun `valid proxy config should return true`() {
        // Given
        val config =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "user",
                password = "pass",
                proxyType = ProxyType.HTTP,
                domains = listOf("example.com"),
                isEnabled = true,
            )

        // When
        val result = config.isValid()

        // Then
        assertTrue("Valid proxy config should return true", result)
    }

    @Test
    fun `proxy config with empty address should return false`() {
        // Given
        val config =
            ProxyConfig(
                id = 1,
                serverAddress = "",
                serverPort = 8080,
                username = "user",
                password = "pass",
                proxyType = ProxyType.HTTP,
                domains = emptyList(),
                isEnabled = true,
            )

        // When
        val result = config.isValid()

        // Then
        assertFalse("Config with empty address should be invalid", result)
    }

    @Test
    fun `proxy config with invalid port should return false`() {
        // Given
        val config =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 0,
                username = "user",
                password = "pass",
                proxyType = ProxyType.HTTP,
                domains = emptyList(),
                isEnabled = true,
            )

        // When
        val result = config.isValid()

        // Then
        assertFalse("Config with invalid port should be invalid", result)
    }

    @Test
    fun `proxy config with empty credentials should return false`() {
        // Given
        val config =
            ProxyConfig(
                id = 1,
                serverAddress = "proxy.example.com",
                serverPort = 8080,
                username = "",
                password = "",
                proxyType = ProxyType.HTTP,
                domains = emptyList(),
                isEnabled = true,
            )

        // When
        val result = config.isValid()

        // Then
        assertFalse("Config with empty credentials should be invalid", result)
    }

    @Test
    fun `proxy config default values should be correct`() {
        // Given
        val config = ProxyConfig()

        // Then
        assertEquals("Default ID should be 0", 0L, config.id)
        assertEquals("Default address should be empty", "", config.serverAddress)
        assertEquals("Default port should be 0", 0, config.serverPort)
        assertEquals("Default username should be empty", "", config.username)
        assertEquals("Default password should be empty", "", config.password)
        assertEquals("Default type should be SOCKS5", ProxyType.SOCKS5, config.proxyType)
        assertTrue("Default domains should be empty", config.domains.isEmpty())
        assertFalse("Default enabled should be false", config.isEnabled)
    }
}
