package com.android.proxy_self.data.database

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.android.proxy_self.data.database.dao.ProxyConfigDao
import com.android.proxy_self.data.database.entities.ProxyConfigEntity
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Integration tests for ProxyDatabase
 */
@RunWith(AndroidJUnit4::class)
class ProxyDatabaseTest {
    private lateinit var database: ProxyDatabase
    private lateinit var proxyConfigDao: ProxyConfigDao

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        database =
            Room.inMemoryDatabaseBuilder(
                context,
                ProxyDatabase::class.java,
            ).build()
        proxyConfigDao = database.proxyConfigDao()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun insertAndGetProxyConfig() =
        runTest {
            // Given
            val proxyConfig =
                ProxyConfigEntity(
                    id = 1,
                    name = "Test Proxy",
                    type = "HTTP",
                    serverHost = "proxy.example.com",
                    serverPort = 8080,
                    username = "testuser",
                    password = "testpass",
                    allowedDomains = "example.com,test.org",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            // When
            proxyConfigDao.insert(proxyConfig)
            val retrieved = proxyConfigDao.getById(1)

            // Then
            assertNotNull("Retrieved config should not be null", retrieved)
            assertEquals("Name should match", "Test Proxy", retrieved?.name)
            assertEquals("Type should match", "HTTP", retrieved?.type)
            assertEquals("Server host should match", "proxy.example.com", retrieved?.serverHost)
            assertEquals("Server port should match", 8080, retrieved?.serverPort)
            assertEquals("Username should match", "testuser", retrieved?.username)
            assertEquals("Password should match", "testpass", retrieved?.password)
            assertEquals("Allowed domains should match", "example.com,test.org", retrieved?.allowedDomains)
            assertTrue("Should be enabled", retrieved?.isEnabled == true)
        }

    @Test
    fun getAllProxyConfigs() =
        runTest {
            // Given
            val config1 =
                ProxyConfigEntity(
                    id = 1,
                    name = "Proxy 1",
                    type = "HTTP",
                    serverHost = "proxy1.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            val config2 =
                ProxyConfigEntity(
                    id = 2,
                    name = "Proxy 2",
                    type = "HTTPS",
                    serverHost = "proxy2.example.com",
                    serverPort = 443,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = false,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            // When
            proxyConfigDao.insert(config1)
            proxyConfigDao.insert(config2)
            val allConfigs = proxyConfigDao.getAll().first()

            // Then
            assertEquals("Should have 2 configs", 2, allConfigs.size)
            assertTrue("Should contain config 1", allConfigs.any { it.name == "Proxy 1" })
            assertTrue("Should contain config 2", allConfigs.any { it.name == "Proxy 2" })
        }

    @Test
    fun updateProxyConfig() =
        runTest {
            // Given
            val originalConfig =
                ProxyConfigEntity(
                    id = 1,
                    name = "Original Proxy",
                    type = "HTTP",
                    serverHost = "original.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            proxyConfigDao.insert(originalConfig)

            val updatedConfig =
                originalConfig.copy(
                    name = "Updated Proxy",
                    serverHost = "updated.example.com",
                    serverPort = 3128,
                    isEnabled = false,
                    updatedAt = System.currentTimeMillis(),
                )

            // When
            proxyConfigDao.update(updatedConfig)
            val retrieved = proxyConfigDao.getById(1)

            // Then
            assertNotNull("Retrieved config should not be null", retrieved)
            assertEquals("Name should be updated", "Updated Proxy", retrieved?.name)
            assertEquals("Server host should be updated", "updated.example.com", retrieved?.serverHost)
            assertEquals("Server port should be updated", 3128, retrieved?.serverPort)
            assertFalse("Should be disabled", retrieved?.isEnabled == true)
        }

    @Test
    fun deleteProxyConfig() =
        runTest {
            // Given
            val config =
                ProxyConfigEntity(
                    id = 1,
                    name = "Test Proxy",
                    type = "HTTP",
                    serverHost = "proxy.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            proxyConfigDao.insert(config)

            // When
            proxyConfigDao.delete(config)
            val retrieved = proxyConfigDao.getById(1)

            // Then
            assertNull("Config should be deleted", retrieved)
        }

    @Test
    fun getEnabledProxyConfigs() =
        runTest {
            // Given
            val enabledConfig =
                ProxyConfigEntity(
                    id = 1,
                    name = "Enabled Proxy",
                    type = "HTTP",
                    serverHost = "enabled.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            val disabledConfig =
                ProxyConfigEntity(
                    id = 2,
                    name = "Disabled Proxy",
                    type = "HTTP",
                    serverHost = "disabled.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = false,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            // When
            proxyConfigDao.insert(enabledConfig)
            proxyConfigDao.insert(disabledConfig)
            val enabledConfigs = proxyConfigDao.getEnabled().first()

            // Then
            assertEquals("Should have 1 enabled config", 1, enabledConfigs.size)
            assertEquals("Should be the enabled config", "Enabled Proxy", enabledConfigs[0].name)
        }

    @Test
    fun deleteAllProxyConfigs() =
        runTest {
            // Given
            val config1 =
                ProxyConfigEntity(
                    id = 1,
                    name = "Proxy 1",
                    type = "HTTP",
                    serverHost = "proxy1.example.com",
                    serverPort = 8080,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            val config2 =
                ProxyConfigEntity(
                    id = 2,
                    name = "Proxy 2",
                    type = "HTTPS",
                    serverHost = "proxy2.example.com",
                    serverPort = 443,
                    username = "",
                    password = "",
                    allowedDomains = "",
                    isEnabled = true,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

            proxyConfigDao.insert(config1)
            proxyConfigDao.insert(config2)

            // When
            proxyConfigDao.deleteAll()
            val allConfigs = proxyConfigDao.getAll().first()

            // Then
            assertTrue("All configs should be deleted", allConfigs.isEmpty())
        }
}
