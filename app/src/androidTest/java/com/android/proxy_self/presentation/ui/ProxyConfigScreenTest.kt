package com.android.proxy_self.presentation.ui

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.entities.ProxyType
import com.android.proxy_self.presentation.ui.screens.ProxyConfigScreen
import com.android.proxy_self.presentation.ui.state.ProxyUiState
import com.android.proxy_self.presentation.ui.theme.ProxySelfTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Integration tests for ProxyConfigScreen UI
 */
@RunWith(AndroidJUnit4::class)
class ProxyConfigScreenTest {
    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun proxyConfigScreen_displaysCorrectInitialState() {
        // Given
        val initialState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.DISCONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = initialState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("Proxy Configuration").assertIsDisplayed()
        composeTestRule.onNodeWithText("Disconnected").assertIsDisplayed()
        composeTestRule.onNodeWithText("Connect").assertIsDisplayed()
        composeTestRule.onNodeWithText("Save Configuration").assertIsDisplayed()
    }

    @Test
    fun proxyConfigScreen_displaysConnectedState() {
        // Given
        val connectedConfig =
            ProxyConfig(
                id = 1,
                name = "Test Proxy",
                type = ProxyType.HTTP,
                serverHost = "proxy.example.com",
                serverPort = 8080,
                username = "",
                password = "",
                allowedDomains = emptyList(),
                isEnabled = true,
            )

        val connectedState =
            ProxyUiState(
                currentConfig = connectedConfig,
                status = ProxyStatus.CONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = connectedState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("Connected").assertIsDisplayed()
        composeTestRule.onNodeWithText("Disconnect").assertIsDisplayed()
        composeTestRule.onNodeWithText("proxy.example.com").assertIsDisplayed()
        composeTestRule.onNodeWithText("8080").assertIsDisplayed()
    }

    @Test
    fun proxyConfigScreen_displaysLoadingState() {
        // Given
        val loadingState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.CONNECTING,
                isLoading = true,
                errorMessage = null,
            )

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = loadingState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("Connecting").assertIsDisplayed()
        composeTestRule.onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate)).assertIsDisplayed()
    }

    @Test
    fun proxyConfigScreen_displaysErrorState() {
        // Given
        val errorState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.ERROR,
                isLoading = false,
                errorMessage = "Connection failed",
            )

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = errorState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("Error").assertIsDisplayed()
        composeTestRule.onNodeWithText("Connection failed").assertIsDisplayed()
    }

    @Test
    fun proxyConfigScreen_userCanInputServerDetails() {
        // Given
        val initialState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.DISCONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        var configChanged = false

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = initialState,
                    onConfigChange = { configChanged = true },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("Enter server address").performTextInput("proxy.test.com")
        composeTestRule.onNodeWithText("Enter port number").performTextInput("3128")

        // Verify input fields contain the entered text
        composeTestRule.onNodeWithText("proxy.test.com").assertIsDisplayed()
        composeTestRule.onNodeWithText("3128").assertIsDisplayed()
    }

    @Test
    fun proxyConfigScreen_connectButtonTriggersCallback() {
        // Given
        val initialState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.DISCONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        var connectClicked = false

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = initialState,
                    onConfigChange = { },
                    onConnect = { connectClicked = true },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        composeTestRule.onNodeWithText("Connect").performClick()

        // Then
        assert(connectClicked) { "Connect callback should be triggered" }
    }

    @Test
    fun proxyConfigScreen_disconnectButtonTriggersCallback() {
        // Given
        val connectedState =
            ProxyUiState(
                currentConfig =
                    ProxyConfig(
                        id = 1,
                        name = "Test Proxy",
                        type = ProxyType.HTTP,
                        serverHost = "proxy.example.com",
                        serverPort = 8080,
                        username = "",
                        password = "",
                        allowedDomains = emptyList(),
                        isEnabled = true,
                    ),
                status = ProxyStatus.CONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        var disconnectClicked = false

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = connectedState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { disconnectClicked = true },
                    onSaveConfig = { },
                    onTestConnection = { },
                )
            }
        }

        composeTestRule.onNodeWithText("Disconnect").performClick()

        // Then
        assert(disconnectClicked) { "Disconnect callback should be triggered" }
    }

    @Test
    fun proxyConfigScreen_saveButtonTriggersCallback() {
        // Given
        val initialState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.DISCONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        var saveClicked = false

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = initialState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { saveClicked = true },
                    onTestConnection = { },
                )
            }
        }

        composeTestRule.onNodeWithText("Save Configuration").performClick()

        // Then
        assert(saveClicked) { "Save callback should be triggered" }
    }

    @Test
    fun proxyConfigScreen_testConnectionButtonTriggersCallback() {
        // Given
        val initialState =
            ProxyUiState(
                currentConfig = null,
                status = ProxyStatus.DISCONNECTED,
                isLoading = false,
                errorMessage = null,
            )

        var testClicked = false

        // When
        composeTestRule.setContent {
            ProxySelfTheme {
                ProxyConfigScreen(
                    uiState = initialState,
                    onConfigChange = { },
                    onConnect = { },
                    onDisconnect = { },
                    onSaveConfig = { },
                    onTestConnection = { testClicked = true },
                )
            }
        }

        composeTestRule.onNodeWithText("Test Connection").performClick()

        // Then
        assert(testClicked) { "Test connection callback should be triggered" }
    }
}
