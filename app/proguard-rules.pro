# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Preserve line numbers for debugging
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep all classes in our main package
-keep class com.android.proxy_self.** { *; }

# Keep Hilt generated classes
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.internal.managers.ViewComponentManager$FragmentContextWrapper { *; }

# Keep Room database classes
-keep class * extends androidx.room.RoomDatabase { *; }
-keep @androidx.room.Entity class * { *; }
-keep @androidx.room.Dao class * { *; }

# Keep Retrofit and OkHttp classes
-keep class retrofit2.** { *; }
-keep class okhttp3.** { *; }
-keep class okio.** { *; }

# Keep Kotlin serialization
-keep class kotlinx.serialization.** { *; }
-keep @kotlinx.serialization.Serializable class * { *; }

# Keep VPN service classes
-keep class * extends android.net.VpnService { *; }
-keep class * extends android.app.Service { *; }

# Keep SOCKS5 implementation
-keep class com.android.proxy_self.infrastructure.utils.Socks5Client { *; }
-keep class com.android.proxy_self.infrastructure.utils.DomainExtractor { *; }

# Keep domain entities
-keep class com.android.proxy_self.domain.entities.** { *; }

# Preserve enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove debug logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove our debug logger in release builds
-assumenosideeffects class com.android.proxy_self.infrastructure.utils.DebugLogger {
    public static void d(...);
    public static void v(...);
    public static void i(...);
    public static void w(...);
    public static void e(...);
    public static void methodEntry(...);
    public static void methodExit(...);
    public static void stateChange(...);
    public static void networkRequest(...);
    public static void networkResponse(...);
    public static void databaseOperation(...);
    public static void userAction(...);
    public static void configChange(...);
    public static void performance(...);
    public static void security(...);
}