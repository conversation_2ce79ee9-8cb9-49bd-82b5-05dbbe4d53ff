<?xml version="1.0" encoding="utf-8"?>
<baseline version="1.0">
    <file name="src/androidTest/java/com/android/proxy_self/ExampleInstrumentedTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="5" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/androidTest/java/com/android/proxy_self/data/database/ProxyDatabaseTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="12" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/androidTest/java/com/android/proxy_self/presentation/ui/ProxyConfigScreenTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/main/java/com/android/proxy_self/MainActivity.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/ProxyApplication.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/datasources/local/ProxyDao.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/datasources/local/ProxyDatabase.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="27" column="21" source="standard:property-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/datasources/local/ProxyEntity.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="22" column="36" source="standard:discouraged-comment-location" />
        <error line="26" column="26" source="standard:discouraged-comment-location" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/datasources/local/ProxyLocalDataSource.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/datasources/remote/ProxyRemoteDataSource.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/mappers/ProxyMapper.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/data/repositories/ProxyRepositoryImpl.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="34" column="21" source="standard:property-naming" />
        <error line="38" column="21" source="standard:property-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/di/DatabaseModule.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/di/ManagerModule.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/di/NetworkModule.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/di/RepositoryModule.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/entities/ProxyConfig.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/entities/ProxyStatus.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/entities/ProxyType.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/repositories/ProxyRepository.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/GetProxyConfigUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/SaveProxyConfigUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/StartProxyUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/StopProxyUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/TestProxyConnectionUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/domain/usecases/base/BaseUseCase.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/managers/NetworkManager.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="157" column="37" source="standard:discouraged-comment-location" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/managers/NotificationManager.kt">
        <error line="1" column="1" source="standard:filename" />
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/managers/ProxyManager.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/receivers/BootReceiver.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="7" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/services/ProxyService.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/services/ProxyVpnService.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="18" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/utils/EncryptionUtils.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/utils/NetworkUtils.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/utils/ValidationUtils.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/infrastructure/workers/AutoStartProxyWorker.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/quicksettings/ProxyQuickSettingsTile.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/state/ProxyUiState.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/components/LoadingButton.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="4" column="1" source="standard:no-wildcard-imports" />
        <error line="15" column="5" source="standard:function-naming" />
        <error line="64" column="13" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/components/ProxyConfigForm.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="8" column="1" source="standard:no-wildcard-imports" />
        <error line="9" column="1" source="standard:no-wildcard-imports" />
        <error line="24" column="5" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/components/ProxyStatusCard.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="9" column="1" source="standard:no-wildcard-imports" />
        <error line="17" column="1" source="standard:no-wildcard-imports" />
        <error line="24" column="5" source="standard:function-naming" />
        <error line="93" column="13" source="standard:function-naming" />
        <error line="151" column="13" source="standard:function-naming" />
        <error line="210" column="13" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/navigation/ProxyNavigation.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="28" column="5" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/screens/AboutScreen.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="8" column="1" source="standard:no-wildcard-imports" />
        <error line="24" column="5" source="standard:function-naming" />
        <error line="79" column="13" source="standard:function-naming" />
        <error line="123" column="13" source="standard:function-naming" />
        <error line="154" column="13" source="standard:function-naming" />
        <error line="207" column="13" source="standard:function-naming" />
        <error line="232" column="13" source="standard:function-naming" />
        <error line="260" column="13" source="standard:function-naming" />
        <error line="293" column="13" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/screens/ProxyConfigScreen.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="9" column="1" source="standard:no-wildcard-imports" />
        <error line="10" column="1" source="standard:no-wildcard-imports" />
        <error line="28" column="5" source="standard:function-naming" />
        <error line="122" column="13" source="standard:function-naming" />
        <error line="190" column="13" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/screens/SettingsScreen.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
        <error line="8" column="1" source="standard:no-wildcard-imports" />
        <error line="9" column="1" source="standard:no-wildcard-imports" />
        <error line="24" column="5" source="standard:function-naming" />
        <error line="167" column="13" source="standard:function-naming" />
        <error line="192" column="13" source="standard:function-naming" />
        <error line="227" column="13" source="standard:function-naming" />
        <error line="249" column="13" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/theme/Color.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/theme/Theme.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="48" column="5" source="standard:function-naming" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/ui/theme/Type.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/viewmodel/ProxyViewModel.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="7" column="1" source="standard:no-wildcard-imports" />
        <error line="12" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/main/java/com/android/proxy_self/presentation/viewmodel/SettingsViewModel.kt">
        <error line="1" column="9" source="standard:package-name" />
    </file>
    <file name="src/test/java/com/android/proxy_self/ExampleUnitTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/test/java/com/android/proxy_self/data/mappers/ProxyMapperTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="6" column="1" source="standard:no-wildcard-imports" />
    </file>
    <file name="src/test/java/com/android/proxy_self/domain/entities/ProxyConfigTest.kt">
        <error line="1" column="9" source="standard:package-name" />
        <error line="3" column="1" source="standard:no-wildcard-imports" />
    </file>
</baseline>
