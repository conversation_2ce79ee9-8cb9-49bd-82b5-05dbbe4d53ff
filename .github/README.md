# GitHub Actions CI/CD Pipeline

This directory contains GitHub Actions workflows for automated CI/CD pipeline.

## Workflows

### 🔄 CI Pipeline (`ci.yml`)

Automated continuous integration pipeline that runs on every push and pull request.

#### Jobs:

1. **Code Quality & Linting**
   - Runs Ktlint (<PERSON><PERSON><PERSON> linter)
   - Runs Android Lint
   - Uploads lint reports as artifacts

2. **Unit Tests**
   - Executes all unit tests
   - Generates test coverage reports with Jacoco
   - Uploads test results and coverage reports

3. **Documentation Generation**
   - Generates KDoc documentation using Dokka
   - Deploys documentation to GitHub Pages (main branch only)
   - Uploads documentation artifacts

4. **Build APK**
   - Builds debug APK
   - Uploads APK as artifact
   - Runs only after lint and tests pass

5. **Security Scan**
   - Runs Trivy vulnerability scanner
   - Uploads security scan results to GitHub Security tab

## Triggers

- **Push**: `main`, `develop`, `feature/*` branches
- **Pull Request**: `main`, `develop` branches

## Artifacts

The CI pipeline generates the following artifacts:

- **Lint Reports**: Ktlint and Android Lint results
- **Test Results**: Unit test reports and coverage
- **Documentation**: KDoc HTML documentation
- **APK**: Debug build artifact
- **Security**: Vulnerability scan results

## Setup Requirements

### 1. Repository Settings

Enable the following in your repository settings:

- **Actions**: Enable GitHub Actions
- **Pages**: Enable GitHub Pages for documentation deployment
- **Security**: Enable security alerts and dependency scanning

### 2. Branch Protection

Configure branch protection rules for `main` and `develop`:

```yaml
Required status checks:
  - Code Quality & Linting
  - Unit Tests
  - Build APK
  
Require branches to be up to date: ✅
Require pull request reviews: ✅
Dismiss stale reviews: ✅
Restrict pushes to matching branches: ✅
```

### 3. JDK Requirements

The CI pipeline uses **JDK 21** (Temurin distribution). Make sure your local development environment also uses JDK 21 for consistency.

### 4. Secrets (Optional)

No secrets are required for the basic CI pipeline. All operations use the default `GITHUB_TOKEN`.

## Local Development

### Quality Check Script

Run all quality checks locally before pushing:

```bash
# Make script executable
chmod +x scripts/quality-check.sh

# Run quality checks
./scripts/quality-check.sh
```

### Code Formatting

Auto-fix code formatting issues:

```bash
# Make script executable
chmod +x scripts/format-code.sh

# Format code
./scripts/format-code.sh
```

### Manual Commands

```bash
# Lint check
./gradlew ktlintCheck

# Auto-format
./gradlew ktlintFormat

# Android lint
./gradlew lintDebug

# Unit tests
./gradlew testDebugUnitTest

# Test coverage
./gradlew jacocoTestReport

# Generate documentation
./gradlew dokkaHtml

# Build APK
./gradlew assembleDebug

# Run all quality checks
./gradlew qualityCheck
```

## Configuration Files

- **`.editorconfig`**: Code style configuration
- **`.ktlint.yml`**: Ktlint-specific rules and settings
- **`app/build.gradle.kts`**: Gradle plugins and tasks configuration

## Reports and Documentation

After CI runs, you can access:

1. **GitHub Actions**: View workflow runs and logs
2. **Artifacts**: Download generated reports and APKs
3. **GitHub Pages**: View generated documentation at `https://your-username.github.io/proxy-self/docs/`
4. **Security**: View vulnerability reports in Security tab

## Troubleshooting

### Common Issues

1. **Ktlint Failures**
   ```bash
   # Auto-fix formatting
   ./gradlew ktlintFormat
   ```

2. **Test Failures**
   ```bash
   # Run specific test
   ./gradlew test --tests "ClassName.testMethod"
   ```

3. **Build Failures**
   ```bash
   # Clean and rebuild
   ./gradlew clean assembleDebug
   ```

4. **Documentation Generation Issues**
   ```bash
   # Check Dokka configuration
   ./gradlew dokkaHtml --info
   ```

### Performance Optimization

- **Gradle Caching**: Enabled for faster builds
- **Parallel Execution**: Jobs run in parallel when possible
- **Artifact Caching**: Dependencies cached between runs
- **Conditional Deployment**: Documentation only deployed from main branch

## Monitoring

Monitor CI/CD pipeline health:

- **Success Rate**: Track workflow success/failure rates
- **Build Time**: Monitor build duration trends
- **Test Coverage**: Track coverage percentage over time
- **Security**: Monitor vulnerability scan results

## Contributing

When contributing:

1. Run local quality checks before pushing
2. Ensure all CI checks pass
3. Maintain or improve test coverage
4. Update documentation as needed
5. Follow code style guidelines
