name: CI

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

permissions:
  contents: write
  pull-requests: write
  security-events: write
  actions: write

jobs:
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
      
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: <PERSON>ache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Run Ktlint Format (Auto-fix)
      run: ./gradlew ktlintFormat

    - name: Check for Ktlint changes
      id: ktlint-changes
      run: |
        if [[ -n $(git status --porcelain) ]]; then
          echo "changes=true" >> $GITHUB_OUTPUT
          echo "Found Ktlint formatting changes"
        else
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "No Ktlint formatting changes"
        fi

    - name: Commit Ktlint fixes
      if: steps.ktlint-changes.outputs.changes == 'true' && github.event_name == 'push'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "Android Proxy Bot"
        git add .
        git commit -m "🔧 Auto-fix Ktlint formatting issues

        - Applied Kotlin code style formatting
        - Fixed indentation and spacing
        - Ensured consistent code formatting across project

        [skip ci]"
        git push

    - name: Run Ktlint Check
      run: ./gradlew ktlintCheck

    - name: Run Android Lint
      run: ./gradlew lintDebug

    - name: Upload lint reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: lint-reports
        path: |
          app/build/reports/ktlint/
          app/build/reports/lint-results-debug.html
          app/build/reports/lint-results-debug.xml

  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Run unit tests
      run: ./gradlew testDebugUnitTest
      
    - name: Generate test coverage report
      run: ./gradlew jacocoTestReport
      
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          app/build/reports/tests/
          app/build/reports/jacoco/

  documentation:
    name: Generate Documentation
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Generate KDoc documentation
      run: ./gradlew dokkaHtml

    - name: Check for KDoc changes
      id: kdoc-changes
      run: |
        if [[ -n $(git status --porcelain) ]]; then
          echo "changes=true" >> $GITHUB_OUTPUT
          echo "Found KDoc documentation changes"
        else
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "No KDoc documentation changes"
        fi

    - name: Commit KDoc updates
      if: steps.kdoc-changes.outputs.changes == 'true' && github.event_name == 'push'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "Android Proxy Docs Bot"
        git add .
        git commit -m "📚 Auto-update KDoc documentation

        - Generated latest API documentation
        - Updated class and method documentation
        - Refreshed code examples and usage guides
        - Synchronized documentation with code changes

        [skip ci]"
        git push

    - name: Deploy documentation to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v4
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: app/build/dokka/html/
        destination_dir: docs

  build:
    name: Build Verification
    runs-on: ubuntu-latest
    needs: [lint, test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Verify build compiles
      run: ./gradlew assembleDebug --dry-run

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  archive-reports:
    name: Archive Reports to Repository
    runs-on: ubuntu-latest
    needs: [lint, test, documentation, build, security-scan]
    if: always() && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Generate all reports
      run: |
        ./gradlew ktlintCheck lintDebug testDebugUnitTest jacocoTestReport dokkaHtml --continue || true

    - name: Clear old reports from repository
      run: |
        echo "🗑️ Clearing old reports..."
        rm -rf reports/
        mkdir -p reports/{lint,test,coverage,docs}

    - name: Copy reports to repository folder
      run: |
        echo "📋 Copying reports to repository..."

        # Copy Ktlint reports
        if [ -d "app/build/reports/ktlint" ]; then
          cp -r app/build/reports/ktlint/* reports/lint/ 2>/dev/null || true
        fi

        # Copy Android Lint reports
        if [ -f "app/build/reports/lint-results-debug.html" ]; then
          cp app/build/reports/lint-results-debug.html reports/lint/
        fi
        if [ -f "app/build/reports/lint-results-debug.xml" ]; then
          cp app/build/reports/lint-results-debug.xml reports/lint/
        fi

        # Copy Test reports
        if [ -d "app/build/reports/tests" ]; then
          cp -r app/build/reports/tests/* reports/test/ 2>/dev/null || true
        fi

        # Copy Coverage reports
        if [ -d "app/build/reports/jacoco" ]; then
          cp -r app/build/reports/jacoco/* reports/coverage/ 2>/dev/null || true
        fi

        # Copy Documentation
        if [ -d "app/build/dokka" ]; then
          cp -r app/build/dokka/* reports/docs/ 2>/dev/null || true
        fi

    - name: Create reports index
      run: |
        cat > reports/README.md << 'EOF'
        # 📊 Latest Build Reports

        Generated on: $(date)
        Commit: ${{ github.sha }}
        Branch: ${{ github.ref_name }}

        ## 📁 Available Reports

        ### 🎨 Code Quality & Linting
        - **Android Lint**: [lint-results-debug.html](lint/lint-results-debug.html)
        - **Ktlint Reports**: [lint/](lint/)

        ### 🧪 Testing
        - **Unit Tests**: [test/testDebugUnitTest/](test/testDebugUnitTest/)
        - **Test Coverage**: [coverage/jacocoTestReport/html/](coverage/jacocoTestReport/html/)

        ### 📚 Documentation
        - **API Documentation**: [docs/html/](docs/html/)

        ## 🔗 Quick Links

        | Report Type | Direct Link |
        |-------------|-------------|
        | Android Lint | [View Report](lint/lint-results-debug.html) |
        | Unit Tests | [View Report](test/testDebugUnitTest/index.html) |
        | Test Coverage | [View Report](coverage/jacocoTestReport/html/index.html) |
        | API Docs | [View Report](docs/html/index.html) |

        ---
        *Reports are automatically updated on every push to main, develop, or feature branches.*
        EOF

    - name: Commit and push reports
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "Android Proxy Reports Bot"

        git add reports/

        if [[ -n $(git diff --cached --name-only) ]]; then
          echo "📊 Committing updated reports..."
          git commit -m "📊 Auto-update build reports

        - Updated lint reports (Ktlint + Android Lint)
        - Updated test results and coverage reports
        - Updated API documentation (KDoc)
        - Generated from commit: ${{ github.sha }}
        - Branch: ${{ github.ref_name }}

        [skip ci]"
          git push
          echo "✅ Reports committed and pushed successfully!"
        else
          echo "ℹ️ No changes in reports to commit"
        fi

    - name: Delete GitHub Actions artifacts
      if: always()
      run: |
        echo "🗑️ Deleting GitHub Actions artifacts..."
        echo "Reports have been archived to repository, artifacts are no longer needed"

        # Get current workflow run ID
        RUN_ID="${{ github.run_id }}"
        REPO="${{ github.repository }}"

        # Wait a moment for artifacts to be fully uploaded
        sleep 10

        # List and delete artifacts for this workflow run
        echo "📋 Listing artifacts for run ID: $RUN_ID"

        # Use GitHub CLI to delete artifacts
        if command -v gh >/dev/null 2>&1; then
          echo "Using GitHub CLI to delete artifacts..."

          # Delete lint-reports artifact
          gh api repos/$REPO/actions/runs/$RUN_ID/artifacts --jq '.artifacts[] | select(.name=="lint-reports") | .id' | \
          while read artifact_id; do
            if [ ! -z "$artifact_id" ]; then
              echo "🗑️ Deleting lint-reports artifact (ID: $artifact_id)"
              gh api --method DELETE repos/$REPO/actions/artifacts/$artifact_id || echo "⚠️ Failed to delete artifact $artifact_id"
            fi
          done

          # Delete test-results artifact
          gh api repos/$REPO/actions/runs/$RUN_ID/artifacts --jq '.artifacts[] | select(.name=="test-results") | .id' | \
          while read artifact_id; do
            if [ ! -z "$artifact_id" ]; then
              echo "🗑️ Deleting test-results artifact (ID: $artifact_id)"
              gh api --method DELETE repos/$REPO/actions/artifacts/$artifact_id || echo "⚠️ Failed to delete artifact $artifact_id"
            fi
          done

          echo "✅ Artifact cleanup completed"
        else
          echo "⚠️ GitHub CLI not available, artifacts will expire according to retention policy"
        fi

        echo "✅ Reports successfully archived to repository at: reports/"
        echo "🔗 Access reports directly from repository without downloading artifacts"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
